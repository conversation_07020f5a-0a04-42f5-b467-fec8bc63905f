# Auth.js Authentication System - Troubleshooting Guide

## 🔧 Fixed Issues

### 3. JWT Secret Missing Error (RESOLVED)
**Issue**: Critical authentication error preventing admin pages from loading
```
MissingSecret: Must pass `secret` if not set to JWT getToken(). Read more at https://errors.authjs.dev#missingsecret
```

**Root Cause**: Middleware was using JWT token validation (`getToken`) with database sessions
- Auth.js configuration used `strategy: "database"` for sessions
- Middleware was calling `getToken()` which requires JWT strategy
- Mismatch between session strategy and middleware approach

**Solution**: Updated middleware to use Auth.js v5 pattern
- Wrapped middleware with `auth()` function from Auth.js v5
- Use `request.auth` to access session data instead of `getToken()`
- Proper error handling and route protection
- Enhanced security with proper admin role checking

## 🔧 Previously Fixed Issues

### 1. Mongoose Schema Index Warning (RESOLVED)
**Issue**: Duplicate index warning for email field in User model
```
Warning: The email field has been indexed using both `index: true` and `schema.index()`
```

**Solution**: Removed duplicate email index from `src/models/User.js`
- The `unique: true` property automatically creates an index
- Removed explicit `UserSchema.index({ email: 1 })` call
- Added additional useful indexes for performance

### 2. Google OAuth Configuration (RESOLVED)
**Issue**: Auth.js using placeholder values instead of actual Google OAuth credentials

**Root Cause**: Environment variable naming mismatch
- Auth.js expected: `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
- Actual variables: `AUTH_GOOGLE_ID` and `AUTH_GOOGLE_SECRET`

**Solution**: Updated `.env.local` to use correct variable names
```env
GOOGLE_CLIENT_ID="1090336007340-vib1q42r4bpqn81dtqqa8ml06c7bb903.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
```

## 🚀 Current Configuration Status

### ✅ Working Providers
1. **Google OAuth** - Fully configured with real credentials
2. **Facebook OAuth** - Configured with real credentials
3. **Email Authentication** - Using existing SMTP configuration
4. **Credentials Provider** - Username/password authentication

### 📧 Email Provider Configuration
**Current Setup**: Using Nodemailer with existing SMTP settings
```env
EMAIL_SERVER_HOST=smtp.hostinger.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=k@6vW@fFatBbW?Y
EMAIL_FROM=<EMAIL>
```

## 🔍 Verification Steps

### 1. Check Environment Variables
Verify all required environment variables are set:
```bash
# Check if variables are loaded
node -e "console.log(process.env.GOOGLE_CLIENT_ID)"
node -e "console.log(process.env.FACEBOOK_CLIENT_ID)"
node -e "console.log(process.env.EMAIL_SERVER_HOST)"
```

### 2. Test Authentication Providers
1. **Google OAuth**:
   - Visit `/auth/signin`
   - Click "Google" button
   - Should redirect to Google OAuth consent screen

2. **Facebook OAuth**:
   - Visit `/auth/signin`
   - Click "Facebook" button
   - Should redirect to Facebook OAuth consent screen

3. **Email Authentication**:
   - Visit `/auth/signin`
   - Switch to "Email Link" tab
   - Enter email address
   - Check email for magic link

4. **Credentials Authentication**:
   - Visit `/auth/signin`
   - Use "Password" tab
   - Enter email/password or create account

### 3. Test Middleware Fix
Run the middleware test to verify the JWT secret issue is resolved:
```bash
npm run test-middleware
```

This will test:
- Public routes are accessible
- Protected routes redirect to sign-in when unauthenticated
- Admin routes are properly protected
- API endpoints return correct status codes

### 4. Debug Mode
Enable debug logging in development:
```env
NEXTAUTH_DEBUG=true
```

## 🛠️ Common Issues & Solutions

### Issue: "Configuration" Error
**Symptoms**: Auth.js shows configuration error
**Solutions**:
1. Check `NEXTAUTH_SECRET` is set and at least 32 characters
2. Verify `NEXTAUTH_URL` matches your domain
3. Ensure all provider credentials are valid

### Issue: OAuth Redirect Mismatch
**Symptoms**: OAuth providers show redirect URI mismatch
**Solutions**:
1. **Google Console**: Add redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (dev)
   - `https://yourdomain.com/api/auth/callback/google` (prod)

2. **Facebook App**: Add Valid OAuth Redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook` (dev)
   - `https://yourdomain.com/api/auth/callback/facebook` (prod)

### Issue: Email Not Sending
**Symptoms**: Magic link emails not received
**Solutions**:
1. Check SMTP credentials are correct
2. Verify email server allows connections
3. Check spam/junk folder
4. Test SMTP connection:
```javascript
const nodemailer = require('nodemailer');
const transporter = nodemailer.createTransporter({
  host: process.env.EMAIL_SERVER_HOST,
  port: process.env.EMAIL_SERVER_PORT,
  secure: true,
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
});

transporter.verify((error, success) => {
  if (error) {
    console.log('SMTP Error:', error);
  } else {
    console.log('SMTP Ready');
  }
});
```

### Issue: Database Connection Problems
**Symptoms**: Auth.js can't connect to MongoDB
**Solutions**:
1. Check MongoDB URI format
2. Ensure database is running
3. Verify network connectivity
4. Check MongoDB Atlas IP whitelist (if using Atlas)

### Issue: Session Not Persisting
**Symptoms**: User gets logged out frequently
**Solutions**:
1. Check `NEXTAUTH_SECRET` is consistent
2. Verify database adapter is working
3. Check session configuration in `src/auth.js`
4. Clear browser cookies and try again

## 📊 Monitoring & Logging

### Authentication Events
The system logs the following events:
- User sign in/out
- New user creation
- Authentication errors
- Rate limit violations

### Debug Information
In development mode, check browser console and server logs for:
- Auth.js debug messages
- Database connection status
- Provider configuration issues
- Session creation/validation

## 🔒 Security Checklist

### ✅ Implemented Security Features
- [x] Rate limiting (100 req/15min standard, 20 req/15min auth)
- [x] CSRF protection via Auth.js
- [x] Secure session cookies
- [x] Password hashing with bcrypt
- [x] Security headers (XSS, CSRF, etc.)
- [x] Input validation and sanitization
- [x] Role-based access control

### 🚨 Security Recommendations
1. **HTTPS in Production**: Ensure SSL/TLS is enabled
2. **Environment Variables**: Never commit `.env.local` to version control
3. **OAuth Credentials**: Rotate credentials periodically
4. **Database Security**: Use strong MongoDB credentials
5. **Email Security**: Use app-specific passwords for email providers

## 🧪 Testing Checklist

### Manual Testing
- [ ] Google OAuth sign in/up
- [ ] Facebook OAuth sign in/up
- [ ] Email magic link authentication
- [ ] Credentials sign in/up
- [ ] Role assignment (<NAME_EMAIL>)
- [ ] Guest booking flow
- [ ] Guest purchase flow
- [ ] Session persistence
- [ ] Sign out functionality

### Automated Testing
Consider adding tests for:
- Authentication flows
- Role-based access control
- API endpoint protection
- Session management
- Error handling

## 📞 Support

If issues persist:
1. Check Auth.js documentation: https://authjs.dev/
2. Review MongoDB adapter docs: https://authjs.dev/reference/adapter/mongodb
3. Check provider-specific documentation
4. Enable debug mode for detailed logs
5. Contact development team with specific error messages

## 🔄 Next Steps

1. **Test All Providers**: Verify each authentication method works
2. **Production Setup**: Configure production OAuth redirect URIs
3. **Email Templates**: Customize email templates for magic links
4. **Monitoring**: Set up production logging and monitoring
5. **Documentation**: Update team documentation with new auth flow
