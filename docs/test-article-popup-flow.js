/**
 * Test Script: Article Popup Flow Verification
 * 
 * This script tests the complete article popup flow to verify the fixes
 * for the POPUP_ITEM_ARTICLE_TOGGLE reducer case and state management.
 * 
 * Usage: Run this in the browser console on a 360° viewer page
 */

async function testArticlePopupFlow() {
  console.log('🧪 Starting Article Popup Flow Test...');
  
  try {
    // 1. Check if we're on a 360° page with the experience context
    console.log('📍 Checking page context...');
    
    if (typeof window === 'undefined') {
      console.error('❌ This test must be run in a browser environment');
      return;
    }
    
    // 2. Test API endpoint for info markers
    console.log('📡 Testing info markers API...');
    const response = await fetch('/api/info-markers?limit=5');
    const data = await response.json();
    
    if (!data.success || data.data.length === 0) {
      console.error('❌ No info markers found. Cannot test article popup flow.');
      return;
    }
    
    console.log(`✅ Found ${data.data.length} info markers`);
    const testMarker = data.data[0];
    console.log('🎯 Test marker:', {
      id: testMarker._id,
      title: testMarker.title,
      hasImage: !!testMarker.image,
      hasBody1: !!testMarker.body1
    });
    
    // 3. Test individual marker API
    console.log(`📡 Testing individual marker API for ID: ${testMarker._id}`);
    const markerResponse = await fetch(`/api/info-markers/${testMarker._id}`);
    const markerData = await markerResponse.json();
    
    if (!markerData.success) {
      console.error('❌ Failed to fetch individual marker data');
      return;
    }
    
    console.log('✅ Individual marker API working');
    
    // 4. Check if experience context is available
    console.log('🔍 Checking for experience context...');
    
    // Look for React components in the DOM
    const reactRoot = document.querySelector('#__next') || document.querySelector('[data-reactroot]');
    if (!reactRoot) {
      console.warn('⚠️ React root not found. Manual state testing required.');
    }
    
    // 5. Simulate the article popup trigger
    console.log('🎬 Simulating article popup trigger...');
    
    // Create a test function that can be called manually
    window.testArticlePopup = function(markerId = testMarker._id) {
      console.log('🚀 Testing article popup with marker ID:', markerId);
      
      // This would normally be called from the component
      // For testing, we'll provide instructions
      console.log('📋 To test manually:');
      console.log('1. Open browser console');
      console.log('2. Navigate to a 360° page with article markers');
      console.log('3. Click on an article marker (infoDoc type)');
      console.log('4. Check console logs for state transitions');
      console.log('5. Verify that GeneralPopupWrapper appears');
      console.log('6. Verify that ItemInfoComponent loads with correct data');
      
      return {
        testMarkerId: markerId,
        expectedState: {
          showPopupGeneral: true,
          showPopupVideo: false,
          showBookingPopup: false,
          showItemInfo: { showItem: true, id: markerId }
        }
      };
    };
    
    // 6. Test state validation function
    window.validateArticlePopupState = function(experienceState) {
      console.log('🔍 Validating article popup state...');
      
      const checks = {
        showPopupGeneral: experienceState?.showPopupGeneral === true,
        showPopupVideo: experienceState?.showPopupVideo === false,
        showBookingPopup: experienceState?.showBookingPopup === false,
        showItemInfoExists: !!experienceState?.showItemInfo,
        showItemInfoShowItem: experienceState?.showItemInfo?.showItem === true,
        showItemInfoHasId: !!experienceState?.showItemInfo?.id
      };
      
      console.log('📊 State validation results:', checks);
      
      const allPassed = Object.values(checks).every(check => check === true);
      
      if (allPassed) {
        console.log('✅ All state checks passed!');
      } else {
        console.log('❌ Some state checks failed. Review the results above.');
      }
      
      return { checks, allPassed };
    };
    
    // 7. Provide debugging helpers
    window.debugArticlePopup = {
      // Helper to check current state
      checkState: function() {
        console.log('🔍 Current page location:', window.location.href);
        console.log('🔍 Available test functions:', Object.keys(window).filter(key => key.includes('test') || key.includes('debug')));
      },

      // Helper to simulate reducer action
      simulateAction: function(markerId = testMarker._id) {
        return {
          type: 'POPUP_ITEM_ARTICLE_TOGGLE',
          payload: markerId
        };
      },

      // Helper to monitor all dispatches for a period
      monitorDispatches: function(durationMs = 10000) {
        console.log(`🔍 Monitoring all dispatches for ${durationMs}ms...`);
        const originalConsoleLog = console.log;
        const dispatchLogs = [];

        // Override console.log to capture dispatch logs
        console.log = function(...args) {
          if (args[0] && args[0].includes && args[0].includes('DISPATCH CALLED')) {
            dispatchLogs.push({
              timestamp: new Date().toISOString(),
              args: args
            });
          }
          return originalConsoleLog.apply(console, args);
        };

        setTimeout(() => {
          console.log = originalConsoleLog;
          console.log('📊 Dispatch monitoring complete. Captured dispatches:', dispatchLogs);
        }, durationMs);

        return dispatchLogs;
      },

      // Helper to track state persistence issue
      trackStatePersistence: function() {
        console.log('🔍 Starting state persistence tracking...');

        // Monitor for the specific issue
        const checkInterval = setInterval(() => {
          const popupWrapper = document.querySelector('.VideoPopupWrapper');
          const generalWrapper = document.querySelector('.GeneralPopupWrapper');

          if (popupWrapper && generalWrapper) {
            console.log('⚠️ ISSUE DETECTED: Both VideoPopupWrapper and GeneralPopupWrapper are present!');
            console.log('VideoPopupWrapper:', popupWrapper);
            console.log('GeneralPopupWrapper:', generalWrapper);
          } else if (popupWrapper && !generalWrapper) {
            console.log('🎥 VideoPopupWrapper is showing (expected for video popups)');
          } else if (!popupWrapper && generalWrapper) {
            console.log('📄 GeneralPopupWrapper is showing (expected for article popups)');
          }
        }, 1000);

        // Stop monitoring after 30 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          console.log('🔍 State persistence tracking stopped');
        }, 30000);

        return checkInterval;
      },

      // Helper to check expected state changes
      expectedStateChanges: {
        before: {
          showPopupGeneral: false,
          showPopupVideo: false,
          showItemInfo: {}
        },
        after: {
          showPopupGeneral: true,
          showPopupVideo: false,
          showBookingPopup: false,
          showItemInfo: { showItem: true, id: testMarker._id }
        }
      }
    };
    
    console.log('\n🎉 Article Popup Flow Test Setup Complete!');
    console.log('\n📋 Available Test Functions:');
    console.log('   • window.testArticlePopup(markerId) - Test article popup trigger');
    console.log('   • window.validateArticlePopupState(state) - Validate state after popup');
    console.log('   • window.debugArticlePopup - Debugging utilities');
    
    console.log('\n🔧 Manual Testing Steps:');
    console.log('   1. Navigate to /360s page');
    console.log('   2. Look for article markers (infoDoc type)');
    console.log('   3. Click on an article marker');
    console.log('   4. Check console for debug logs');
    console.log('   5. Verify popup appears with article content');
    
    console.log('\n📊 Test Data Available:');
    console.log(`   • Test Marker ID: ${testMarker._id}`);
    console.log(`   • Test Marker Title: ${testMarker.title}`);
    console.log(`   • API Endpoint: /api/info-markers/${testMarker._id}`);
    
    return {
      success: true,
      testMarkerId: testMarker._id,
      apiWorking: true,
      testFunctionsCreated: true
    };
    
  } catch (error) {
    console.error('❌ Test setup failed with error:', error);
    return { success: false, error: error.message };
  }
}

// Instructions for use
console.log(`
🧪 Article Popup Flow Test Script

To run this test:
1. Open browser developer tools (F12)
2. Go to Console tab
3. Copy and paste this entire script
4. Run: testArticlePopupFlow()

This will:
- Test the info markers API
- Set up debugging functions
- Provide manual testing instructions
- Create state validation helpers
`);

// Auto-run the test setup
testArticlePopupFlow();
