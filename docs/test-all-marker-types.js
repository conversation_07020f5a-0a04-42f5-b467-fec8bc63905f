/**
 * Test Script: All Marker Types Verification
 * 
 * This script tests all marker types to ensure they work correctly with CRUD operations.
 * It verifies guide, infoVideo, infoDoc, infoImage, landingPage, upstairs, and downstairs markers.
 * 
 * Usage: Run this in the browser console on the admin dashboard
 */

async function testAllMarkerTypes() {
  console.log('🧪 Starting All Marker Types Test...');
  
  const markerTypes = [
    'guide',
    'infoVideo', 
    'infoDoc',
    'infoImage',
    'landingPage',
    'upstairs',
    'downstairs'
  ];
  
  try {
    // 1. Get a test 360 image
    console.log('📡 Fetching 360 images for testing...');
    const response = await fetch('/api/360s?limit=1');
    const data = await response.json();
    
    if (!data.success || data.data.length === 0) {
      console.error('❌ No 360 images found for testing');
      return;
    }
    
    const testImage = data.data[0];
    console.log(`✅ Using test image: ${testImage.name} (ID: ${testImage._id})`);
    
    // 2. Test creating markers of each type
    console.log('\n🔨 Testing marker creation for each type...');
    
    const testMarkers = [];
    
    for (let i = 0; i < markerTypes.length; i++) {
      const markerType = markerTypes[i];
      const marker = {
        name: `test_${markerType}_${Date.now()}`,
        markerType: markerType,
        x: Math.random() * 10 - 5, // Random position between -5 and 5
        y: Math.random() * 10 - 5,
        z: Math.random() * 10 - 5,
      };
      
      // Add type-specific properties
      if (['landingPage', 'guide', 'upstairs', 'downstairs'].includes(markerType)) {
        marker._360Name = testImage.name; // Navigation markers use _360Name
      } else {
        marker.id = ''; // Content markers use id (will be empty for test)
      }
      
      testMarkers.push(marker);
      console.log(`   ✅ Created ${markerType} marker: ${marker.name}`);
    }
    
    // 3. Save all markers to the test image
    console.log('\n💾 Saving all test markers...');
    
    const savePayload = {
      markerList: testMarkers,
      cameraPosition: testImage.cameraPosition || 0,
      _360Rotation: testImage._360Rotation || 0
    };
    
    const saveResponse = await fetch(`/api/360s/${testImage._id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(savePayload)
    });
    
    const saveResult = await saveResponse.json();
    
    if (!saveResponse.ok || !saveResult.success) {
      console.error('❌ Failed to save test markers:', saveResult.message);
      return;
    }
    
    console.log(`✅ Successfully saved ${testMarkers.length} test markers`);
    
    // 4. Verify markers were saved correctly
    console.log('\n🔍 Verifying saved markers...');
    
    const verifyResponse = await fetch(`/api/360s?id=${testImage._id}`);
    const verifyData = await verifyResponse.json();
    
    if (!verifyData.success || verifyData.data.length === 0) {
      console.error('❌ Failed to retrieve saved markers for verification');
      return;
    }
    
    const savedImage = verifyData.data[0];
    const savedMarkers = savedImage.markerList || [];
    
    console.log(`✅ Retrieved ${savedMarkers.length} markers from database`);
    
    // 5. Test each marker type
    console.log('\n🧪 Testing each marker type...');
    
    const markerTypeResults = {};
    
    for (const markerType of markerTypes) {
      const markersOfType = savedMarkers.filter(m => m.markerType === markerType);
      markerTypeResults[markerType] = {
        count: markersOfType.length,
        markers: markersOfType
      };
      
      if (markersOfType.length > 0) {
        const marker = markersOfType[0];
        console.log(`   ✅ ${markerType}:`);
        console.log(`      - Name: ${marker.name}`);
        console.log(`      - Position: [${marker.x}, ${marker.y}, ${marker.z}]`);
        
        // Check type-specific properties
        if (['landingPage', 'guide', 'upstairs', 'downstairs'].includes(markerType)) {
          console.log(`      - _360Name: ${marker._360Name || 'NOT SET'}`);
          if (!marker._360Name) {
            console.warn(`      ⚠️  _360Name should be set for ${markerType} markers`);
          }
        } else {
          console.log(`      - id: ${marker.id || 'NOT SET'}`);
          // Note: id can be empty for test markers, this is expected
        }
      } else {
        console.error(`   ❌ No ${markerType} markers found`);
      }
    }
    
    // 6. Test marker update
    console.log('\n🔄 Testing marker update...');
    
    if (savedMarkers.length > 0) {
      const markerToUpdate = savedMarkers[0];
      const originalPosition = [markerToUpdate.x, markerToUpdate.y, markerToUpdate.z];
      
      // Update position
      markerToUpdate.x += 1;
      markerToUpdate.y += 1;
      markerToUpdate.z += 1;
      
      const updatePayload = {
        markerList: savedMarkers,
        cameraPosition: savedImage.cameraPosition || 0,
        _360Rotation: savedImage._360Rotation || 0
      };
      
      const updateResponse = await fetch(`/api/360s/${testImage._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatePayload)
      });
      
      const updateResult = await updateResponse.json();
      
      if (updateResponse.ok && updateResult.success) {
        console.log(`✅ Successfully updated marker: ${markerToUpdate.name}`);
        console.log(`   - Original position: [${originalPosition.join(', ')}]`);
        console.log(`   - New position: [${markerToUpdate.x}, ${markerToUpdate.y}, ${markerToUpdate.z}]`);
      } else {
        console.error('❌ Failed to update marker:', updateResult.message);
      }
    }
    
    // 7. Test marker deletion
    console.log('\n🗑️  Testing marker deletion...');
    
    if (savedMarkers.length > 1) {
      const markersAfterDeletion = savedMarkers.slice(1); // Remove first marker
      const deletedMarker = savedMarkers[0];
      
      const deletePayload = {
        markerList: markersAfterDeletion,
        cameraPosition: savedImage.cameraPosition || 0,
        _360Rotation: savedImage._360Rotation || 0
      };
      
      const deleteResponse = await fetch(`/api/360s/${testImage._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(deletePayload)
      });
      
      const deleteResult = await deleteResponse.json();
      
      if (deleteResponse.ok && deleteResult.success) {
        console.log(`✅ Successfully deleted marker: ${deletedMarker.name}`);
        console.log(`   - Remaining markers: ${markersAfterDeletion.length}`);
      } else {
        console.error('❌ Failed to delete marker:', deleteResult.message);
      }
    }
    
    console.log('\n🎉 All Marker Types Test Complete!');
    console.log('\n📊 Test Results Summary:');
    
    for (const [type, result] of Object.entries(markerTypeResults)) {
      const status = result.count > 0 ? '✅' : '❌';
      console.log(`   ${status} ${type}: ${result.count} marker(s)`);
    }
    
    console.log('\n✅ CRUD Operations Tested:');
    console.log('   ✅ CREATE - All marker types created successfully');
    console.log('   ✅ READ - Markers retrieved and verified');
    console.log('   ✅ UPDATE - Marker position updated successfully');
    console.log('   ✅ DELETE - Marker deletion working correctly');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Auto-run the test
testAllMarkerTypes();
