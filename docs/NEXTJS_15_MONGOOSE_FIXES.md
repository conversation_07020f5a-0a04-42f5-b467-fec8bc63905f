# Next.js 15 and Mongoose Fixes

## Summary
Fixed Next.js 15 async params warnings and Mongoose duplicate index warning that were causing console errors and warnings in the application.

## Issues Fixed

### 1. Next.js 15 Async Params Warning
**Problem**: Next.js 15 requires `params` to be awaited in API route handlers, causing warnings:
```
Warning: In route /api/[...]/[id] `params` is a Promise. Use `await params` instead.
```

**Root Cause**: Next.js 15 changed the behavior of `params` in API routes to be asynchronous for better performance and consistency.

### 2. Mongoose Duplicate Index Warning
**Problem**: Mongoose was showing duplicate index warnings:
```
(node:29092) [MONGOOSE] Warning: Duplicate schema index on {"email":1} found. 
This is often due to declaring an index using both "index: true" and "schema.index()". 
Please remove the duplicate index definition.
```

**Root Cause**: The User model had both `unique: true` (which automatically creates an index) and an explicit `UserSchema.index({ email: 1 })` declaration.

## Fixes Implemented

### 1. Updated API Routes for Next.js 15 Async Params

**Files Fixed**:
- `src/app/api/360s/[id]/route.js` ✅ (Already fixed)
- `src/app/api/hero-videos/[id]/route.js` ✅
- `src/app/api/packages/[id]/route.js` ✅
- `src/app/api/clients/[id]/route.js` ✅
- `src/app/api/stores/[id]/route.js` ✅
- `src/app/api/video-gallery/[id]/route.js` ✅

**Change Pattern**:
```javascript
// BEFORE (Next.js 14 pattern)
export const GET = async (request, { params }) => {
  const { id } = params;
  // ...
}

// AFTER (Next.js 15 pattern)
export const GET = async (request, { params }) => {
  const { id } = await params;
  // ...
}
```

**Methods Updated**:
- GET endpoints
- PUT endpoints  
- DELETE endpoints
- PATCH endpoints

### 2. Fixed Mongoose Duplicate Index Warning

**File**: `src/models/User.js`

**Change**:
```javascript
// BEFORE - Duplicate index declaration
email: {
  type: String,
  required: [true, 'Email is required'],
  unique: true, // This automatically creates an index
  // ... other properties
},

// Later in the file:
UserSchema.index({ email: 1 }); // Duplicate index!

// AFTER - Removed duplicate index
email: {
  type: String,
  required: [true, 'Email is required'],
  unique: true, // This automatically creates an index
  // ... other properties
},

// Indexes for performance
// Note: email index is automatically created by unique: true in schema definition
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ lastLogin: -1 });
```

## Technical Details

### Next.js 15 Async Params
- **Why**: Next.js 15 made `params` asynchronous to improve performance and enable better caching strategies
- **Impact**: All dynamic route API handlers needed to await the params object
- **Backward Compatibility**: The old synchronous pattern still works but shows warnings

### Mongoose Index Optimization
- **Why**: `unique: true` automatically creates a database index for uniqueness constraint
- **Impact**: Explicit index declaration was redundant and caused warnings
- **Performance**: No performance impact - the same index is still created, just without duplication

## Files Modified

### API Routes (Next.js 15 Async Params)
1. `src/app/api/hero-videos/[id]/route.js` - GET, PUT, DELETE, PATCH methods
2. `src/app/api/packages/[id]/route.js` - GET, PUT, DELETE, PATCH methods
3. `src/app/api/clients/[id]/route.js` - GET, PUT, DELETE, PATCH methods
4. `src/app/api/stores/[id]/route.js` - GET, PUT, DELETE methods
5. `src/app/api/video-gallery/[id]/route.js` - GET, PUT, DELETE methods

### Models (Mongoose Index Fix)
1. `src/models/User.js` - Removed duplicate email index

## Testing Results
- ✅ No more Next.js 15 async params warnings
- ✅ No more Mongoose duplicate index warnings
- ✅ All API endpoints continue to function correctly
- ✅ Database performance maintained (same indexes, no duplication)
- ✅ PATCH requests to 360s endpoints work correctly

## Additional Notes

### API Endpoints Status
All dynamic route API endpoints now properly support Next.js 15:
- **360s API**: Already fixed in previous updates
- **Bookings API**: Already fixed in previous updates  
- **Hero Videos API**: ✅ Fixed
- **Packages API**: ✅ Fixed
- **Clients API**: ✅ Fixed
- **Stores API**: ✅ Fixed
- **Video Gallery API**: ✅ Fixed

### Database Indexes
The User model now has clean, non-duplicate indexes:
- `email` (unique) - Automatically created by `unique: true`
- `role` - For role-based queries
- `isActive` - For active user filtering
- `createdAt` - For chronological sorting
- `lastLogin` - For login tracking

## Git Commit Message
```
fix: update API routes for Next.js 15 and resolve Mongoose warnings

- Add await to params in all dynamic route API handlers
- Remove duplicate email index from User model
- Fix Next.js 15 async params warnings across all endpoints
- Resolve Mongoose duplicate index warning for email field
- Maintain backward compatibility and performance

Fixes console warnings and ensures Next.js 15 compliance
```
