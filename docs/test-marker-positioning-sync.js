/**
 * Test Script: Marker Positioning and Metadata Synchronization
 * 
 * This script validates that marker positioning, properties, and metadata 
 * are correctly synchronized between client and server.
 * 
 * Usage: Run this in the browser console on the admin dashboard
 */

async function testMarkerPositioningSync() {
  console.log('🧪 Starting Marker Positioning & Metadata Sync Test...');
  
  try {
    // 1. Get test data
    console.log('📡 Setting up test environment...');
    const response = await fetch('/api/360s?limit=1');
    const data = await response.json();
    
    if (!data.success || data.data.length === 0) {
      console.error('❌ No 360 images found for testing');
      return;
    }
    
    const testImage = data.data[0];
    console.log(`✅ Using test image: ${testImage.name}`);
    
    // 2. Create precision test markers with specific positioning
    console.log('\n🎯 Creating precision test markers...');
    
    const precisionMarkers = [
      {
        name: 'precision_test_1',
        markerType: 'guide',
        x: 1.23456789,
        y: -2.87654321,
        z: 0.11111111,
        _360Name: testImage.name
      },
      {
        name: 'precision_test_2', 
        markerType: 'infoDoc',
        x: -5.99999999,
        y: 3.14159265,
        z: 2.71828182,
        id: 'test_doc_id'
      },
      {
        name: 'precision_test_3',
        markerType: 'upstairs',
        x: 0.00000001,
        y: -0.00000001,
        z: 10.12345678,
        _360Name: testImage.name
      }
    ];
    
    console.log('   📍 Test markers created with precision positioning');
    precisionMarkers.forEach((marker, i) => {
      console.log(`      ${i + 1}. ${marker.name}: [${marker.x}, ${marker.y}, ${marker.z}]`);
    });
    
    // 3. Save precision markers
    console.log('\n💾 Saving precision markers...');
    
    const savePayload = {
      markerList: precisionMarkers,
      cameraPosition: 1.5,
      _360Rotation: 0.785398163 // π/4 radians
    };
    
    const saveResponse = await fetch(`/api/360s/${testImage._id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(savePayload)
    });
    
    const saveResult = await saveResponse.json();
    
    if (!saveResponse.ok || !saveResult.success) {
      console.error('❌ Failed to save precision markers:', saveResult.message);
      return;
    }
    
    console.log('✅ Precision markers saved successfully');
    
    // 4. Verify precision is maintained
    console.log('\n🔍 Verifying positioning precision...');
    
    const verifyResponse = await fetch(`/api/360s?id=${testImage._id}`);
    const verifyData = await verifyResponse.json();
    
    if (!verifyData.success || verifyData.data.length === 0) {
      console.error('❌ Failed to retrieve markers for precision verification');
      return;
    }
    
    const retrievedImage = verifyData.data[0];
    const retrievedMarkers = retrievedImage.markerList || [];
    
    console.log(`✅ Retrieved ${retrievedMarkers.length} markers for precision check`);
    
    // Check positioning precision
    let precisionErrors = 0;
    const tolerance = 0.0000001; // Very small tolerance for floating point comparison
    
    for (const originalMarker of precisionMarkers) {
      const retrievedMarker = retrievedMarkers.find(m => m.name === originalMarker.name);
      
      if (!retrievedMarker) {
        console.error(`❌ Marker not found: ${originalMarker.name}`);
        precisionErrors++;
        continue;
      }
      
      // Check position precision
      const xDiff = Math.abs(originalMarker.x - retrievedMarker.x);
      const yDiff = Math.abs(originalMarker.y - retrievedMarker.y);
      const zDiff = Math.abs(originalMarker.z - retrievedMarker.z);
      
      if (xDiff > tolerance || yDiff > tolerance || zDiff > tolerance) {
        console.error(`❌ Position precision lost for ${originalMarker.name}:`);
        console.error(`   Original: [${originalMarker.x}, ${originalMarker.y}, ${originalMarker.z}]`);
        console.error(`   Retrieved: [${retrievedMarker.x}, ${retrievedMarker.y}, ${retrievedMarker.z}]`);
        console.error(`   Differences: [${xDiff}, ${yDiff}, ${zDiff}]`);
        precisionErrors++;
      } else {
        console.log(`✅ ${originalMarker.name}: Position precision maintained`);
      }
      
      // Check metadata preservation
      if (originalMarker.markerType !== retrievedMarker.markerType) {
        console.error(`❌ Marker type mismatch for ${originalMarker.name}: ${originalMarker.markerType} → ${retrievedMarker.markerType}`);
        precisionErrors++;
      }
      
      // Check type-specific properties
      if (['guide', 'upstairs', 'downstairs', 'landingPage'].includes(originalMarker.markerType)) {
        if (originalMarker._360Name !== retrievedMarker._360Name) {
          console.error(`❌ _360Name mismatch for ${originalMarker.name}: ${originalMarker._360Name} → ${retrievedMarker._360Name}`);
          precisionErrors++;
        }
      } else {
        if (originalMarker.id !== retrievedMarker.id) {
          console.error(`❌ ID mismatch for ${originalMarker.name}: ${originalMarker.id} → ${retrievedMarker.id}`);
          precisionErrors++;
        }
      }
    }
    
    // 5. Test camera settings precision
    console.log('\n📷 Testing camera settings precision...');
    
    const cameraPosDiff = Math.abs(savePayload.cameraPosition - retrievedImage.cameraPosition);
    const rotationDiff = Math.abs(savePayload._360Rotation - retrievedImage._360Rotation);
    
    if (cameraPosDiff > tolerance) {
      console.error(`❌ Camera position precision lost: ${savePayload.cameraPosition} → ${retrievedImage.cameraPosition}`);
      precisionErrors++;
    } else {
      console.log('✅ Camera position precision maintained');
    }
    
    if (rotationDiff > tolerance) {
      console.error(`❌ 360 rotation precision lost: ${savePayload._360Rotation} → ${retrievedImage._360Rotation}`);
      precisionErrors++;
    } else {
      console.log('✅ 360 rotation precision maintained');
    }
    
    // 6. Test rapid position updates (simulating Leva controls)
    console.log('\n⚡ Testing rapid position updates...');
    
    const rapidUpdateMarker = retrievedMarkers[0];
    const updateSteps = 5;
    const stepSize = 0.1;
    
    for (let step = 1; step <= updateSteps; step++) {
      rapidUpdateMarker.x += stepSize;
      rapidUpdateMarker.y += stepSize;
      rapidUpdateMarker.z += stepSize;
      
      const rapidUpdatePayload = {
        markerList: retrievedMarkers,
        cameraPosition: retrievedImage.cameraPosition,
        _360Rotation: retrievedImage._360Rotation
      };
      
      const rapidResponse = await fetch(`/api/360s/${testImage._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(rapidUpdatePayload)
      });
      
      const rapidResult = await rapidResponse.json();
      
      if (rapidResponse.ok && rapidResult.success) {
        console.log(`   ✅ Rapid update ${step}/${updateSteps}: Position [${rapidUpdateMarker.x.toFixed(6)}, ${rapidUpdateMarker.y.toFixed(6)}, ${rapidUpdateMarker.z.toFixed(6)}]`);
      } else {
        console.error(`   ❌ Rapid update ${step} failed:`, rapidResult.message);
        precisionErrors++;
      }
      
      // Small delay to simulate real usage
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // 7. Final verification
    console.log('\n🔍 Final verification after rapid updates...');
    
    const finalResponse = await fetch(`/api/360s?id=${testImage._id}`);
    const finalData = await finalResponse.json();
    
    if (finalData.success && finalData.data.length > 0) {
      const finalMarker = finalData.data[0].markerList.find(m => m.name === rapidUpdateMarker.name);
      
      if (finalMarker) {
        const expectedX = precisionMarkers[0].x + (updateSteps * stepSize);
        const expectedY = precisionMarkers[0].y + (updateSteps * stepSize);
        const expectedZ = precisionMarkers[0].z + (updateSteps * stepSize);
        
        const finalXDiff = Math.abs(expectedX - finalMarker.x);
        const finalYDiff = Math.abs(expectedY - finalMarker.y);
        const finalZDiff = Math.abs(expectedZ - finalMarker.z);
        
        if (finalXDiff > tolerance || finalYDiff > tolerance || finalZDiff > tolerance) {
          console.error(`❌ Final position verification failed for ${finalMarker.name}`);
          console.error(`   Expected: [${expectedX}, ${expectedY}, ${expectedZ}]`);
          console.error(`   Actual: [${finalMarker.x}, ${finalMarker.y}, ${finalMarker.z}]`);
          precisionErrors++;
        } else {
          console.log(`✅ Final position verification passed for ${finalMarker.name}`);
        }
      }
    }
    
    // 8. Results summary
    console.log('\n🎉 Marker Positioning & Metadata Sync Test Complete!');
    console.log('\n📊 Test Results:');
    
    if (precisionErrors === 0) {
      console.log('✅ ALL TESTS PASSED - Perfect synchronization achieved!');
      console.log('   ✅ Position precision maintained');
      console.log('   ✅ Metadata preservation working');
      console.log('   ✅ Camera settings synchronized');
      console.log('   ✅ Rapid updates handled correctly');
      console.log('   ✅ Type-specific properties preserved');
    } else {
      console.error(`❌ ${precisionErrors} precision/synchronization errors detected`);
      console.log('   Please review the errors above and check the implementation');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Auto-run the test
testMarkerPositioningSync();
