# State Persistence Issue Debugging Guide

## Problem Description
Despite the reducer correctly setting `showPopupVideo: false` in the `POPUP_ITEM_ARTICLE_TOGGLE` case, the component state shows `showPopupVideo: true` immediately after the action is dispatched, causing the VideoPopupWrapper to appear instead of the GeneralPopupWrapper.

## Root Cause Analysis

### Potential Causes Investigated:
1. ✅ **Reducer Logic** - Confirmed correct in `POPUP_ITEM_ARTICLE_TOGGLE` case
2. ✅ **Initial State** - Confirmed `showPopupVideo: false` in `INITIAL_EXPERIENCE_STATE`
3. ✅ **Context Provider** - Confirmed proper useReducer implementation
4. 🔍 **Race Conditions** - Likely cause: competing actions dispatched in quick succession
5. 🔍 **Automatic Triggers** - Possible: effects or components auto-triggering video popups

## Debugging Enhancements Implemented

### 1. Comprehensive Reducer Logging
**File:** `src/contexts/reducerExperience.js`

```javascript
// Added logging for all popup-related actions
if (action.type.includes('POPUP') || action.type === 'RESET' || action.type === 'CLOSE_POPUP') {
    console.log(`🔄 REDUCER ACTION: ${action.type}`, {
        before: {
            showPopupVideo: state.showPopupVideo,
            showPopupGeneral: state.showPopupGeneral,
            showVideoGallery: state.showVideoGallery,
            showSingleVideoGallery: state.showSingleVideoGallery,
            showVideo: state.showVideo
        },
        payload: action.payload
    });
}
```

### 2. Enhanced Action Dispatch Tracking
**File:** `src/contexts/useContextExperience.js`

```javascript
// Wrap dispatch with comprehensive logging
const disptachExperience = (action) => {
    console.log('🚀 DISPATCH CALLED:', {
        action,
        timestamp: new Date().toISOString(),
        stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
    });
    return originalDispatch(action);
};
```

### 3. State Change Monitoring
**File:** `src/components/menu-popup/PopupWrapper.jsx`

```javascript
// Track state changes with timestamps and change detection
useEffect(() => {
    // Detect and log all state changes with before/after values
    if (Object.keys(changes).length > 0) {
        console.log('🔄 PopupWrapper STATE CHANGE:', {
            timestamp: new Date().toISOString(),
            changes,
            fullState: currentState
        });
    }
}, [experienceState]);
```

### 4. Advanced Debugging Tools
**File:** `docs/test-article-popup-flow.js`

New debugging functions available:
- `window.debugArticlePopup.monitorDispatches(duration)` - Monitor all dispatches
- `window.debugArticlePopup.trackStatePersistence()` - Track DOM element presence
- Enhanced state validation and logging

## Debugging Instructions

### Step 1: Enable Debug Logging
1. Ensure development server is running
2. Navigate to a 360° page with article markers
3. Open browser console (F12)

### Step 2: Load Debugging Tools
```javascript
// Copy and paste the enhanced test script
// Then run:
testArticlePopupFlow()
```

### Step 3: Monitor Dispatch Sequence
```javascript
// Start monitoring all dispatches
window.debugArticlePopup.monitorDispatches(15000) // Monitor for 15 seconds

// Click an article marker within the monitoring period
// Check console for dispatch sequence
```

### Step 4: Track State Persistence
```javascript
// Start state persistence tracking
window.debugArticlePopup.trackStatePersistence()

// Click article marker and observe DOM changes
```

### Step 5: Analyze Console Logs

Look for this sequence in the console:

#### Expected Sequence (Working):
```
🚀 DISPATCH CALLED: { action: { type: 'POPUP_ITEM_ARTICLE_TOGGLE', payload: 'marker_id' } }
🔄 REDUCER ACTION: POPUP_ITEM_ARTICLE_TOGGLE (before state)
📄 POPUP_ITEM_ARTICLE_TOGGLE - After: { showPopupGeneral: true, showPopupVideo: false }
🔄 PopupWrapper STATE CHANGE: { changes: { showPopupGeneral: { from: false, to: true } } }
📊 PopupWrapper render state: { showPopupGeneral: true, showPopupVideo: false }
```

#### Problem Sequence (Race Condition):
```
🚀 DISPATCH CALLED: { action: { type: 'POPUP_ITEM_ARTICLE_TOGGLE', payload: 'marker_id' } }
🔄 REDUCER ACTION: POPUP_ITEM_ARTICLE_TOGGLE (before state)
📄 POPUP_ITEM_ARTICLE_TOGGLE - After: { showPopupGeneral: true, showPopupVideo: false }
🚀 DISPATCH CALLED: { action: { type: 'POPUP_VIDOE_GALLERY_TOGGLE' } } // ⚠️ Competing action!
🔄 REDUCER ACTION: POPUP_VIDOE_GALLERY_TOGGLE (before state)
🎥 POPUP_VIDOE_GALLERY_TOGGLE - After: { showPopupGeneral: false, showPopupVideo: true }
📊 PopupWrapper render state: { showPopupGeneral: false, showPopupVideo: true } // ❌ Wrong state
```

## Key Indicators to Look For

### 1. Multiple Rapid Dispatches
- Look for multiple `🚀 DISPATCH CALLED` logs within milliseconds
- Check stack traces to identify source of competing actions

### 2. State Reversion
- Look for state changes that immediately revert
- Check for `showPopupVideo` changing from `false` to `true` after article action

### 3. Component Effects
- Look for useEffect hooks triggering automatic actions
- Check for event listeners causing unintended dispatches

### 4. DOM Element Conflicts
- Both `.VideoPopupWrapper` and `.GeneralPopupWrapper` present simultaneously
- Incorrect popup type displaying

## Common Race Condition Sources

### 1. Event Handler Conflicts
```javascript
// Check for multiple event handlers on the same element
// Look for event bubbling issues
```

### 2. useEffect Dependencies
```javascript
// Check for effects that trigger on state changes
// Look for infinite loops or cascading effects
```

### 3. Automatic Video Triggers
```javascript
// Check for components that auto-trigger video popups
// Look for initialization logic that sets video states
```

### 4. Marker Click Handling
```javascript
// Check for multiple click handlers on markers
// Look for event delegation issues
```

## Resolution Strategy

Once the race condition is identified:

1. **Identify the Competing Action Source**
   - Use stack traces from dispatch logs
   - Find the component/effect causing the competing dispatch

2. **Implement Action Debouncing**
   - Add debouncing to prevent rapid successive actions
   - Use action queuing if multiple actions are necessary

3. **Fix Event Handler Conflicts**
   - Ensure single event handler per marker
   - Prevent event bubbling where appropriate

4. **Review useEffect Dependencies**
   - Remove unnecessary effect triggers
   - Add proper dependency arrays

5. **Test the Fix**
   - Verify single dispatch per marker click
   - Confirm correct popup type displays
   - Test across different marker types

## Next Steps

1. Run the debugging tools on the live application
2. Identify the exact sequence causing the race condition
3. Implement targeted fixes based on findings
4. Remove debug logging once issue is resolved
5. Add unit tests to prevent regression
