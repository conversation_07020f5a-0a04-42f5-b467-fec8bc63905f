# 360° Image Replacement Workflow - Testing Guide

## Overview

This guide outlines how to test the fixed 360° image replacement workflow to ensure all components work correctly.

## Test Scenarios

### **Scenario 1: Basic File Replacement**

**Steps:**
1. Navigate to 360° Management Dashboard
2. Select an existing 360° image
3. Click "Upload New Image" or drag a new file
4. When prompted with "File already exists", click "Yes, Replace File"
5. Wait for the replacement process to complete

**Expected Results:**
- ✅ No TypeError errors in console
- ✅ Firebase deletion attempt (may fail gracefully)
- ✅ New file uploads successfully to Firebase Storage
- ✅ Database updates with new Firebase URL
- ✅ UI refreshes showing new image
- ✅ All markers and camera settings preserved

### **Scenario 2: Firebase Deletion Failure (Expected)**

**Steps:**
1. Follow Scenario 1 steps
2. Monitor browser console for deletion errors

**Expected Results:**
- ⚠️ Firebase deletion may fail (400 error) - **This is OK**
- ✅ Workflow continues despite deletion failure
- ✅ Upload and database update still succeed
- ✅ Console shows: "Failed to delete existing Firebase file, proceeding with upload"

### **Scenario 3: New File Upload (No Replacement)**

**Steps:**
1. Navigate to 360° Management Dashboard
2. Upload a completely new file (different name)
3. Fill in form details and submit

**Expected Results:**
- ✅ No replacement dialog appears
- ✅ File uploads directly to Firebase Storage
- ✅ New 360° record created in database
- ✅ No TypeError errors

### **Scenario 4: Multiple File Upload**

**Steps:**
1. Select multiple 360° images at once
2. Upload them simultaneously

**Expected Results:**
- ✅ Each file handled correctly
- ✅ Multiple file response format handled properly
- ✅ All files get Firebase URLs

## Console Monitoring

### **Success Indicators:**
```
✅ Upload result: { success: true, url: "https://firebasestorage..." }
✅ Updating database with new Firebase URL: https://firebasestorage...
✅ Firebase file replacement workflow completed successfully
```

### **Expected Warnings (Non-Critical):**
```
⚠️ Failed to delete existing Firebase file, proceeding with upload: Deletion Failed
⚠️ POST https://localhost:3001/api/firebase/delete-file 400 (Bad Request)
```

### **Error Indicators (Should NOT Appear):**
```
❌ TypeError: Cannot read properties of undefined (reading 'length')
❌ Upload failed - no data returned from upload service
❌ No Firebase URL received from upload response
```

## API Response Formats

### **Single File Upload Response:**
```json
{
  "success": true,
  "url": "https://firebasestorage.googleapis.com/...",
  "path": "elephantisland/360s/filename.jpg",
  "filename": "filename.jpg",
  "size": 1234567,
  "type": "image/jpeg",
  "storage": "firebase",
  "message": "File uploaded successfully"
}
```

### **Multiple File Upload Response:**
```json
{
  "success": true,
  "data": [
    {
      "url": "https://firebasestorage.googleapis.com/...",
      "path": "elephantisland/360s/filename.jpg",
      "filename": "filename.jpg",
      "size": 1234567,
      "type": "image/jpeg",
      "storage": "firebase"
    }
  ],
  "errors": [],
  "message": "1 file(s) uploaded successfully"
}
```

## Database Verification

### **Check MongoDB Record:**
```javascript
// The 360° record should be updated with:
{
  "_id": "...",
  "url": "https://firebasestorage.googleapis.com/...", // NEW Firebase URL
  "originalFileName": "new-filename.jpg", // NEW filename
  "markerList": [...], // PRESERVED markers
  "cameraPosition": {...}, // PRESERVED camera settings
  "cameraRotation": {...}, // PRESERVED camera settings
  "updatedAt": "2024-01-XX..." // UPDATED timestamp
}
```

## Troubleshooting

### **If TypeError Still Occurs:**
1. Check browser console for exact line number
2. Verify the upload API response format
3. Ensure all 5 upload functions were updated correctly

### **If Firebase Deletion Always Fails:**
1. Check Firebase Storage permissions
2. Verify Firebase configuration in `.env.local`
3. This is non-critical - workflow should continue

### **If Database Update Fails:**
1. Check MongoDB connection
2. Verify 360° record exists with correct ID
3. Check API route `/api/360s/[id]` logs

## Success Criteria

The fix is successful if:

- ✅ **No TypeError errors** in any upload scenario
- ✅ **File replacement completes** despite deletion failures
- ✅ **New Firebase URLs** are properly extracted and saved
- ✅ **All metadata preserved** during replacement
- ✅ **UI updates correctly** after replacement
- ✅ **Both single and multiple** file uploads work

## Performance Notes

- File replacement may take 10-30 seconds for large images
- Firebase deletion timeout is acceptable and handled gracefully
- Page reload after successful replacement is intentional for UI refresh
