# PATCH Method Implementation for 360° Marker Updates

## Summary
Implemented PATCH HTTP method support for 360° marker updates to follow RESTful conventions where PATCH is used for partial updates rather than PUT (which is typically used for complete resource replacement).

## Changes Made

### 1. Updated MarkersInputList Component
**File**: `src/components/360s/MarkersInputList.jsx`

**Change**: Modified the `handleSubmit` function to use PATCH instead of PUT:
```javascript
// Before
method: 'PUT',

// After  
method: 'PATCH',
```

**Rationale**: Marker list updates are partial updates to the 360° resource, making PATCH more semantically appropriate than PUT.

### 2. Added PATCH Support to Bulk 360s API Endpoint
**File**: `src/app/api/360s/route.js`

**Changes**:
- Extracted shared update logic into `handleBulkUpdate` function
- Added PATCH method handler that uses the same logic as PUT
- Maintained backward compatibility by keeping PUT method

**Implementation**:
```javascript
// Shared update logic for both PUT and PATCH methods
const handleBulkUpdate = async (request) => {
  // ... existing update logic
};

// PUT /api/360s - Bulk update 360s (complete replacement, manager/admin only)
export const PUT = requireManagerAPI(handleBulkUpdate);

// PATCH /api/360s - Bulk partial update 360s (partial updates like markers, manager/admin only)
export const PATCH = requireManagerAPI(handleBulkUpdate);
```

### 3. Added PATCH Support to Individual 360s API Endpoint
**File**: `src/app/api/360s/[id]/route.js`

**Changes**:
- Extracted shared update logic into `handleUpdate` function
- Added PATCH method handler for individual 360° updates
- Maintained backward compatibility by keeping PUT method

**Implementation**:
```javascript
// Shared update logic for both PUT and PATCH methods
const handleUpdate = async (request, { params }) => {
  // ... existing update logic
};

// PUT /api/360s/[id] - Update 360 (complete replacement, manager/admin only)
export const PUT = requireManagerAPI(handleUpdate);

// PATCH /api/360s/[id] - Partial update 360 (partial updates, manager/admin only)
export const PATCH = requireManagerAPI(handleUpdate);
```

## RESTful Convention Compliance

### HTTP Method Semantics
- **PUT**: Complete resource replacement - replaces the entire resource
- **PATCH**: Partial resource update - modifies only specified fields

### Implementation Benefits
1. **Semantic Correctness**: PATCH better represents the nature of marker updates
2. **API Consistency**: Both bulk and individual endpoints now support PATCH
3. **Backward Compatibility**: Existing PUT endpoints remain functional
4. **Future-Proofing**: Proper RESTful conventions for API evolution

## API Endpoint Summary

### Bulk Operations (`/api/360s`)
- **GET**: Public access for retrieving 360s
- **POST**: Create new 360° (manager/admin only)
- **PUT**: Complete bulk replacement (manager/admin only)
- **PATCH**: Partial bulk updates (manager/admin only) ✨ NEW
- **DELETE**: Bulk delete (manager/admin only)

### Individual Operations (`/api/360s/[id]`)
- **GET**: Retrieve specific 360° (manager/admin only)
- **PUT**: Complete resource replacement (manager/admin only)
- **PATCH**: Partial resource update (manager/admin only) ✨ NEW
- **DELETE**: Delete specific 360° (manager/admin only)

## Usage Examples

### Marker List Update (Now using PATCH)
```javascript
const response = await fetch('/api/360s', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    items: [{ _id: '...', markerList: updatedMarkers }]
  })
});
```

### Priority Update (Can use either PUT or PATCH)
```javascript
const response = await fetch(`/api/360s/${id}`, {
  method: 'PATCH', // or 'PUT'
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ priority: newPriority })
});
```

## Backward Compatibility
- All existing PUT requests continue to work unchanged
- No breaking changes to existing functionality
- Components can gradually migrate to PATCH for partial updates

## Files Modified
1. `src/components/360s/MarkersInputList.jsx` - Changed method from PUT to PATCH
2. `src/app/api/360s/route.js` - Added PATCH handler for bulk operations
3. `src/app/api/360s/[id]/route.js` - Added PATCH handler for individual operations

## Git Commit Message
```
feat: implement PATCH method for 360° marker updates

- Add PATCH support to bulk and individual 360s API endpoints
- Update MarkersInputList to use PATCH for marker updates
- Extract shared update logic for PUT/PATCH methods
- Maintain backward compatibility with existing PUT endpoints
- Follow RESTful conventions for partial resource updates
```
