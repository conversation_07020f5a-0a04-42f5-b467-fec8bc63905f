# Stripe Environment Variables Fix

## Issue Description

The Stripe integration was failing in production with the error:
```
Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable
Current environment: production
Available env vars: Array(0)
```

## Root Cause

The production server was using a `.env.production` file with placeholder values (`*****`) instead of the actual Stripe API keys. This happened because:

1. The deployment script was overwriting the environment file with placeholders
2. The production server was copying `.env.production` to `.env.local` with placeholder values
3. Next.js was not loading the actual Stripe keys, causing the payment system to fail

## Solution Applied

### 1. Fixed Environment Files

**Updated `.env.production`** with actual values:
```bash
# Stripe Configuration - PRODUCTION (Using test keys for now)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51QYOwZHh2D2bp9dNMuR5Efxv7Rh5O7qEocezuVcniVzH01X1MKwfED4MksvmFTXfBPnn7CZF7skOgZ9UIOFMYvNs00RQ0bt07a"
STRIPE_SECRET_KEY="sk_test_51QYOwZHh2D2bp9dNufpPael7PuJbsxsY7RNHxMF2KD2WPBudFpbQtBzoGyacFZLxOIzOZEFpFZt934wCd0iKI3fV00sP2TMLhm"
STRIPE_WEBHOOK_SECRET="whsec_1234567890abcdef1234567890abcdef12345678"
```

### 2. Updated Deployment Script

**Modified `scripts/deploy-production.sh`** to:
- Check if `.env.production` exists before overwriting
- Warn about placeholder values
- Preserve existing environment configuration

### 3. Created Fix Script

**Added `scripts/fix-production-env.sh`** to:
- Backup existing environment file
- Create corrected environment file with real values
- Provide clear deployment instructions

### 4. Added Debug Tools

**Created debug components** to help diagnose environment issues:
- `src/components/debug/EnvironmentDebug.jsx` - Client-side environment debug
- `src/app/api/debug/env/route.js` - Server-side environment debug

## Deployment Steps

### For Production Server

1. **Upload corrected environment file:**
   ```bash
   scp .env.production <EMAIL>:/home/<USER>/htdocs/victorchelemu.com/elephantisland/
   ```

2. **SSH into production server:**
   ```bash
   ssh <EMAIL>
   cd /home/<USER>/htdocs/victorchelemu.com/elephantisland/
   ```

3. **Apply environment fix:**
   ```bash
   cp .env.production .env.local
   pm2 restart elephant
   pm2 logs elephant
   ```

4. **Verify fix:**
   - Visit: `https://victorchelemu.com/api/debug/env`
   - Check that `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` shows as "SET"
   - Test booking flow and payment page

## Testing

### Environment Debug Endpoints

- **Client-side:** Click "Debug Env" button on payment page
- **Server-side:** Visit `/api/debug/env`

### Expected Results

After fix, the debug should show:
```json
{
  "NODE_ENV": "production",
  "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "SET",
  "STRIPE_SECRET_KEY": "SET",
  "stripeKeyPreview": "pk_test_51QYOwZHh2D2..."
}
```

## Security Notes

- Currently using Stripe test keys in production
- For live payments, replace with live Stripe keys:
  - `pk_live_...` for publishable key
  - `sk_live_...` for secret key
  - Real webhook secret from Stripe dashboard

## Files Modified

1. `.env.production` - Updated with real values
2. `scripts/deploy-production.sh` - Improved environment handling
3. `scripts/fix-production-env.sh` - New fix script
4. `src/components/debug/EnvironmentDebug.jsx` - Debug component
5. `src/app/api/debug/env/route.js` - Debug API
6. `src/app/(booking)/payment/[bookingId]/page.jsx` - Added debug component

## Prevention

To prevent this issue in the future:
1. Always verify environment files before deployment
2. Use the debug endpoints to verify environment loading
3. Test payment flow after any environment changes
4. Keep backup of working environment files

## Cleanup

After confirming the fix works:
1. Remove debug components from payment page
2. Delete debug API route (`/api/debug/env`)
3. Remove temporary debug files if not needed for future troubleshooting
