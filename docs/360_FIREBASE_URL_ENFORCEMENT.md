# 360° Firebase URL Enforcement

## Issue Description
The 360° system was storing local upload URLs (`/uploads/360s/filename.jpg`) in the database instead of Firebase Storage URLs. This caused issues in production where local files might not be accessible, and violated the requirement that all 360° images must be stored in Firebase.

## Root Cause
The file upload system had a fallback mechanism that saved files locally when Firebase upload failed. This fallback was being used for 360° images, resulting in local URLs being stored in the database instead of Firebase URLs.

## Solutions Implemented

### 1. **Firebase-Only Upload Function** ✅

#### **New Function: `uploadFileFirebaseOnly()`**
- **Location**: `src/lib/file-upload.js`
- **Purpose**: Upload files to Firebase Storage without local fallback
- **Behavior**: Throws error if Firebase upload fails (no local fallback)

```javascript
export async function uploadFileFirebaseOnly(file, folder = 'general', filename = null) {
  // Upload to Firebase Storage - no fallback
  const storageRef = ref(storage, filePath);
  const snapshot = await uploadBytes(storageRef, file);
  const downloadURL = await getDownloadURL(snapshot.ref);
  
  return {
    success: true,
    url: downloadURL, // Always Firebase URL
    storage: 'firebase'
  };
}
```

### 2. **Enhanced File Upload Handler** ✅

#### **Modified: `createFileUploadHandler()`**
- **Location**: `src/lib/file-upload.js`
- **Enhancement**: Detects 360° uploads and uses Firebase-only mode
- **Logic**: `folder === '360s'` triggers Firebase-only upload

```javascript
// Determine if this is a 360° upload (requires Firebase-only)
const isThreeSixtyUpload = folder === '360s';

const uploadResult = isThreeSixtyUpload 
  ? await uploadFileFirebaseOnly(file, folder)  // No fallback
  : await uploadFile(file, folder);             // With fallback
```

### 3. **API Validation for Firebase URLs** ✅

#### **Enhanced: POST `/api/360s`**
- **Location**: `src/app/api/360s/route.js`
- **Validation**: Rejects non-Firebase URLs for new 360° records

```javascript
// Validate that URL is a Firebase URL (not local upload)
const url = String(body.url).trim();
if (!url.startsWith('https://firebasestorage.googleapis.com/')) {
  return NextResponse.json({
    success: false,
    error: 'Invalid URL',
    message: 'URL must be a Firebase Storage URL. Local upload URLs are not allowed for 360° images.',
  }, { status: 400 });
}
```

#### **Enhanced: PATCH `/api/360s/[id]`**
- **Location**: `src/app/api/360s/[id]/route.js`
- **Validation**: Rejects non-Firebase URLs for 360° updates

### 4. **Migration System** ✅

#### **Migration Utility: `src/lib/360-url-migration.js`**
- **Purpose**: Migrate existing local URLs to Firebase URLs
- **Features**:
  - Detect records needing migration
  - Upload local files to Firebase
  - Update database with new Firebase URLs
  - Generate migration reports
  - Optional cleanup of local files

#### **Migration API: `/api/360s/migrate-to-firebase`**
- **GET**: Check migration status and preview
- **POST**: Run migration (dry-run or actual)

#### **Migration Admin Interface: `/admin/360-migration`**
- **Purpose**: User-friendly interface for running migrations
- **Features**:
  - View migration status
  - Preview what would be migrated
  - Run actual migration
  - View results and reports

### 5. **Migration Process Flow**

#### **Step 1: Check Status**
```bash
GET /api/360s/migrate-to-firebase
```
- Returns count of records needing migration
- Shows sample local and Firebase URLs
- Provides recommendations

#### **Step 2: Dry Run (Preview)**
```bash
POST /api/360s/migrate-to-firebase
Content-Type: application/json

{
  "dryRun": true,
  "cleanup": false
}
```
- Shows what would be migrated without making changes
- Validates local files exist
- Generates preview report

#### **Step 3: Run Migration**
```bash
POST /api/360s/migrate-to-firebase
Content-Type: application/json

{
  "dryRun": false,
  "cleanup": false
}
```
- Uploads local files to Firebase
- Updates database with Firebase URLs
- Preserves local files for safety

#### **Step 4: Cleanup (Optional)**
```bash
POST /api/360s/migrate-to-firebase
Content-Type: application/json

{
  "dryRun": false,
  "cleanup": true
}
```
- Migrates files and deletes local copies
- Only deletes after successful Firebase upload

## Migration Safety Features

### **Validation Checks**
- ✅ Verify local file exists before migration
- ✅ Validate Firebase upload success before database update
- ✅ Preserve original URL in database for rollback
- ✅ Generate detailed migration reports

### **Error Handling**
- ✅ Continue migration if individual files fail
- ✅ Detailed error logging for failed migrations
- ✅ Rollback capability with original URLs preserved

### **Dry Run Mode**
- ✅ Preview all changes before execution
- ✅ Validate file accessibility without uploading
- ✅ Generate migration reports without database changes

## Usage Instructions

### **For New 360° Uploads**
1. **Upload files through the 360° dashboard**
2. **System automatically uses Firebase-only upload**
3. **Database stores Firebase URLs only**
4. **Local fallback is disabled for 360° images**

### **For Existing Records Migration**

#### **Option 1: Admin Interface (Recommended)**
1. Navigate to `/admin/360-migration`
2. Review migration status and sample records
3. Run dry run to preview changes
4. Execute migration when ready
5. Optionally cleanup local files

#### **Option 2: API Calls**
```javascript
// Check status
const status = await fetch('/api/360s/migrate-to-firebase');

// Dry run
const preview = await fetch('/api/360s/migrate-to-firebase', {
  method: 'POST',
  body: JSON.stringify({ dryRun: true })
});

// Actual migration
const result = await fetch('/api/360s/migrate-to-firebase', {
  method: 'POST',
  body: JSON.stringify({ dryRun: false })
});
```

## Verification Steps

### **After Migration**
1. **Check Database**: All 360° records should have Firebase URLs
2. **Test Loading**: 360° viewer should load images from Firebase
3. **Verify Upload**: New uploads should go directly to Firebase
4. **API Validation**: Local URLs should be rejected by API

### **Database Query to Check**
```javascript
// Find any remaining local URLs
db.collection('360s').find({
  url: { $regex: '^/?uploads/' }
}).count()

// Should return 0 after successful migration
```

## Rollback Plan

### **If Migration Issues Occur**
1. **Database Rollback**: Use `originalLocalUrl` field to restore local URLs
2. **File Restoration**: Local files are preserved unless cleanup was run
3. **API Rollback**: Temporarily disable Firebase URL validation

### **Emergency Rollback Script**
```javascript
// Restore original local URLs
db.collection('360s').updateMany(
  { originalLocalUrl: { $exists: true } },
  { 
    $set: { url: "$originalLocalUrl" },
    $unset: { originalLocalUrl: 1, migratedToFirebase: 1 }
  }
)
```

## Benefits

### **Production Reliability**
- ✅ All 360° images guaranteed to be in Firebase
- ✅ No dependency on local file system in production
- ✅ Consistent URL patterns across environments

### **Scalability**
- ✅ Firebase Storage handles large files efficiently
- ✅ CDN distribution for global access
- ✅ No server disk space concerns

### **Maintenance**
- ✅ Centralized asset management in Firebase
- ✅ Automatic backup and versioning
- ✅ Easy monitoring and analytics

## Git Commit Message

```
feat: enforce Firebase URLs for all 360° images

- Add Firebase-only upload function for 360° images
- Implement API validation to reject local URLs
- Create comprehensive migration system for existing records
- Add admin interface for migration management
- Ensure all new 360° uploads go directly to Firebase
- Preserve backward compatibility with migration tools
- Add safety features including dry-run and rollback capabilities
```
