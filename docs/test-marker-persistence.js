// Test script for 360° marker persistence
// Run this in browser console to test the API endpoint

async function testMarkerPersistence() {
  console.log('🧪 Testing 360° Marker Persistence...');
  
  try {
    // 1. Get list of 360° images
    console.log('📋 Fetching 360° images...');
    const listResponse = await fetch('/api/360s');
    const listData = await listResponse.json();
    
    if (!listData.success || !listData.data.length) {
      console.error('❌ No 360° images found');
      return;
    }
    
    const testImage = listData.data[0];
    console.log('✅ Using test image:', testImage.name, testImage._id);
    
    // 2. Create test marker data
    const testMarkers = [
      {
        name: 'test-marker-1',
        markerType: 'guide',
        x: 5.5,
        y: 3.2,
        z: -2.1,
        _360Name: 'test-destination'
      },
      {
        name: 'test-marker-2',
        markerType: 'infoVideo',
        x: -1.8,
        y: 4.7,
        z: 6.3,
        id: 'test-video-id'
      }
    ];
    
    // 3. Update the 360° image with test markers
    console.log('💾 Saving test markers...');
    const updatePayload = {
      markerList: testMarkers,
      cameraPosition: testImage.cameraPosition || 0,
      _360Rotation: testImage._360Rotation || 0
    };
    
    const updateResponse = await fetch(`/api/360s/${testImage._id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updatePayload)
    });
    
    const updateData = await updateResponse.json();
    
    if (!updateResponse.ok) {
      console.error('❌ Update failed:', updateData);
      return;
    }
    
    console.log('✅ Update successful:', updateData.data);
    
    // 4. Verify the data was saved correctly
    console.log('🔍 Verifying saved data...');
    const verifyResponse = await fetch(`/api/360s/${testImage._id}`);
    const verifyData = await verifyResponse.json();
    
    if (!verifyData.success) {
      console.error('❌ Verification failed:', verifyData);
      return;
    }
    
    const savedMarkers = verifyData.data.markerList || [];
    console.log('📊 Saved markers:', savedMarkers);
    
    // 5. Compare submitted vs saved data
    let allMatch = true;
    testMarkers.forEach((submitted, index) => {
      const saved = savedMarkers[index];
      if (!saved) {
        console.error(`❌ Marker ${index} not found in saved data`);
        allMatch = false;
        return;
      }
      
      const fieldsToCheck = ['name', 'markerType', 'x', 'y', 'z', '_360Name', 'id'];
      fieldsToCheck.forEach(field => {
        if (submitted[field] !== saved[field]) {
          console.error(`❌ Marker ${index} field '${field}' mismatch:`, {
            submitted: submitted[field],
            saved: saved[field]
          });
          allMatch = false;
        }
      });
    });
    
    if (allMatch) {
      console.log('🎉 All marker data persisted correctly!');
    } else {
      console.error('❌ Some marker data did not persist correctly');
    }
    
    // 6. Test position precision
    console.log('🎯 Testing position precision...');
    const precisionTest = {
      name: 'precision-test',
      markerType: 'guide',
      x: 1.23456789,
      y: -9.87654321,
      z: 0.00001234,
      _360Name: ''
    };
    
    const precisionPayload = {
      markerList: [precisionTest],
      cameraPosition: testImage.cameraPosition || 0,
      _360Rotation: testImage._360Rotation || 0
    };
    
    const precisionResponse = await fetch(`/api/360s/${testImage._id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(precisionPayload)
    });
    
    const precisionData = await precisionResponse.json();
    const savedPrecision = precisionData.data.markerList[0];
    
    console.log('📐 Precision test results:', {
      submitted: { x: precisionTest.x, y: precisionTest.y, z: precisionTest.z },
      saved: { x: savedPrecision.x, y: savedPrecision.y, z: savedPrecision.z }
    });
    
    // 7. Restore original markers
    console.log('🔄 Restoring original markers...');
    const restorePayload = {
      markerList: testImage.markerList || [],
      cameraPosition: testImage.cameraPosition || 0,
      _360Rotation: testImage._360Rotation || 0
    };
    
    await fetch(`/api/360s/${testImage._id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(restorePayload)
    });
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Instructions for use:
console.log(`
🧪 360° Marker Persistence Test Script

To run this test:
1. Open browser developer tools (F12)
2. Go to Console tab
3. Copy and paste this entire script
4. Run: testMarkerPersistence()

This will test:
- Marker data submission to API
- Data persistence in MongoDB
- Position precision accuracy
- Complete round-trip data integrity
`);

// Uncomment the line below to run the test automatically
// testMarkerPersistence();
