# Production 502 Error Fix - Info Markers API

## Issue Description
The production server is returning 502 Bad Gateway errors and "Unexpected token '<', '<html>' is not valid JSON" errors when accessing the info markers API endpoints. This indicates the server is returning HTML error pages instead of JSON responses.

## Root Cause Analysis
The 502 errors are typically caused by:

1. **MongoDB Connection Failures**: Database connection timeouts or authentication issues
2. **Server Resource Exhaustion**: Memory or CPU limits exceeded
3. **Environment Variable Issues**: Missing or incorrect configuration
4. **API Route Crashes**: Unhandled exceptions causing server crashes

## Immediate Fixes Applied

### 1. Enhanced Error Handling in API Routes

#### Info Markers API (`src/app/api/info-markers/[id]/route.js`):
- Added database connection timeouts (10 seconds)
- Added MongoDB ObjectId validation
- Enhanced error logging and categorization
- Added specific error responses for different failure types

#### Main Info Markers Route (`src/app/api/info-markers/route.js`):
- Added connection timeout handling
- Enhanced request logging
- Improved error categorization and responses

### 2. Fixed Client-Side Error Handling

#### ItemInfoComponent (`src/components/menu-popup/ItemInfoComponent.jsx`):
- Fixed logic error in data validation
- Added proper HTTP status checking
- Added content-type validation to detect HTML responses
- Enhanced error messages and logging

### 3. Production Debugging Endpoint

Created `/api/debug/production` endpoint to diagnose issues:
- Tests database connectivity
- Validates info markers collection
- Checks environment variables
- Provides system health status

## Production Deployment Steps

### Step 1: Check Environment Variables
Ensure these variables are set in production:

```bash
# On your production server
echo $MONGODB_URI
echo $NODE_ENV
echo $NEXTAUTH_URL
```

### Step 2: Test Database Connection
Visit: `https://victorchelemu.com/api/debug/production`

This will show:
- Database connection status
- Info markers collection health
- System resource usage
- Specific error messages if any

### Step 3: Check Server Logs
```bash
# PM2 logs
pm2 logs elephant

# Or check application logs
tail -f /path/to/your/logs
```

### Step 4: Restart Application
```bash
# Restart PM2 process
pm2 restart elephant

# Or restart all processes
pm2 restart all
```

## Common Issues and Solutions

### Issue 1: MongoDB Connection Timeout
**Symptoms**: 504 Gateway Timeout errors
**Solution**: 
- Check MongoDB Atlas cluster status
- Verify IP whitelist includes server IP
- Check network connectivity

### Issue 2: Invalid MongoDB URI
**Symptoms**: Connection refused errors
**Solution**:
- Verify MONGODB_URI format
- Check username/password encoding
- Ensure database name is correct

### Issue 3: Server Memory Issues
**Symptoms**: Random 502 errors, slow responses
**Solution**:
- Check server memory usage
- Restart PM2 processes
- Consider upgrading server resources

### Issue 4: Environment Variables Missing
**Symptoms**: "Environment variable not defined" errors
**Solution**:
- Copy .env.production to .env.local on server
- Restart application after updating variables

## Testing the Fix

### 1. Test Info Marker Retrieval
```bash
curl -X GET "https://victorchelemu.com/api/info-markers/[valid-id]" \
  -H "Content-Type: application/json"
```

### 2. Test Info Marker Creation
```bash
curl -X POST "https://victorchelemu.com/api/info-markers" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Marker",
    "body1": "Test body 1",
    "body2": "Test body 2",
    "image": "https://example.com/image.jpg"
  }'
```

### 3. Check Debug Endpoint
```bash
curl -X GET "https://victorchelemu.com/api/debug/production"
```

## Monitoring and Prevention

### 1. Set Up Health Checks
- Monitor `/api/debug/production` endpoint
- Set up alerts for 502 errors
- Monitor database connection status

### 2. Log Monitoring
- Check PM2 logs regularly
- Monitor for memory leaks
- Watch for database connection errors

### 3. Regular Maintenance
- Restart PM2 processes weekly
- Update dependencies regularly
- Monitor server resource usage

## Emergency Rollback Plan

If issues persist:

1. **Revert to Previous Version**:
   ```bash
   git checkout [previous-working-commit]
   npm run build
   pm2 restart elephant
   ```

2. **Use Fallback Database**:
   - Switch to backup MongoDB cluster
   - Update MONGODB_URI environment variable

3. **Enable Maintenance Mode**:
   - Display maintenance page
   - Investigate issues without user impact

## Contact Information
For urgent production issues:
- Check server logs first
- Use debug endpoint for diagnostics
- Document all error messages and steps taken
