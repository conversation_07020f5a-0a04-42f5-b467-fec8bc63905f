#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to remove authentication from admin pages
 * This will make all admin pages accessible without authentication
 */

const fs = require('fs');
const path = require('path');

// Admin pages that need authentication removed
const adminPages = [
  'src/app/(admin)/admin/packages/page.jsx',
  'src/app/(admin)/admin/bookings/page.jsx',
  'src/app/(admin)/admin/clients/page.jsx',
  'src/app/(admin)/admin/stores/page.jsx',
  'src/app/(admin)/admin/videos/page.jsx',
  'src/app/(admin)/admin/info-markers/page.jsx',
  'src/app/(admin)/admin/360s-manager/file-manager/page.jsx',
  'src/app/(admin)/admin/360s-manager/360-viewer/page.jsx',
  'src/app/(admin)/dashboard/page.jsx'
];

// API routes that need authentication removed
const apiRoutes = [
  'src/app/api/admin/dashboard/route.js',
  'src/app/api/site-management/route.js',
  'src/app/api/hero-videos/route.js',
  'src/app/api/info-markers/route.js',
  'src/app/api/stores/route.js',
  'src/app/api/packages/route.js',
  'src/app/api/360s/route.js',
  'src/app/api/360s/[id]/route.js',
  'src/app/api/video-gallery/route.js',
  'src/app/api/video-gallery/[id]/route.js'
];

function removeAuthFromAdminPage(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Remove auth imports
    if (content.includes('import { auth }') || content.includes('import { requireManager }')) {
      content = content.replace(/import { auth } from '@\/auth';\s*/g, '');
      content = content.replace(/import { requireManager } from '@\/lib\/auth-utils';\s*/g, '');
      content = content.replace(/import { redirect } from 'next\/navigation';\s*/g, '');
      modified = true;
    }

    // Remove session checks
    if (content.includes('const session = await auth()')) {
      content = content.replace(/\s*const session = await auth\(\);\s*/g, '\n');
      modified = true;
    }

    // Remove requireManager calls
    if (content.includes('const user = await requireManager()')) {
      content = content.replace(/\s*const user = await requireManager\(\);\s*/g, '\n');
      modified = true;
    }

    // Remove authentication checks and redirects
    const authCheckPatterns = [
      /\s*if \(!session\?\?\.user \|\| !\['manager', 'admin'\]\.includes\(session\.user\.role\)\) \{\s*redirect\('\/auth\/signin'\);\s*\}/g,
      /\s*if \(!session\) \{\s*redirect\('\/auth\/signin\?callbackUrl=.*?'\);\s*\}/g,
      /\s*\/\/ Check if user is authenticated and has manager\/admin role\s*/g,
      /\s*\/\/ Redirect admins and managers to admin dashboard\s*/g,
      /\s*if \(session\.user\.role === 'admin' \|\| session\.user\.role === 'manager'\) \{\s*redirect\('\/admin\/dashboard'\);\s*\}/g
    ];

    authCheckPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        content = content.replace(pattern, '');
        modified = true;
      }
    });

    // Add comment about no authentication
    if (modified && !content.includes('// No authentication required')) {
      const exportDefaultMatch = content.match(/export default async function \w+\(\) \{/);
      if (exportDefaultMatch) {
        content = content.replace(
          exportDefaultMatch[0],
          `${exportDefaultMatch[0]}\n  // No authentication required - page is now public`
        );
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Removed authentication from: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No authentication found in: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function removeAuthFromAPIRoute(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Remove requireManagerAPI and requireAdminAPI wrappers
    const wrapperPatterns = [
      /export const (GET|POST|PUT|DELETE|PATCH) = requireManagerAPI\(async \(request(?:, \{ params \})?\) => \{/g,
      /export const (GET|POST|PUT|DELETE|PATCH) = requireAdminAPI\(async \(request(?:, \{ params \})?\) => \{/g
    ];

    wrapperPatterns.forEach(pattern => {
      content = content.replace(pattern, (match, method) => {
        modified = true;
        const hasParams = match.includes('{ params }');
        return `export async function ${method}(request${hasParams ? ', { params }' : ''}) {`;
      });
    });

    // Fix closing brackets - replace }); with }
    if (modified) {
      // Count opening and closing braces to fix the wrapper closing
      const lines = content.split('\n');
      for (let i = lines.length - 1; i >= 0; i--) {
        if (lines[i].trim() === '});' && i > 0) {
          // Check if this is likely a wrapper closing
          let braceCount = 0;
          let foundFunction = false;
          
          for (let j = i - 1; j >= 0; j--) {
            if (lines[j].includes('export async function')) {
              foundFunction = true;
              break;
            }
            if (lines[j].includes('{')) braceCount++;
            if (lines[j].includes('}')) braceCount--;
          }
          
          if (foundFunction && braceCount > 0) {
            lines[i] = '}';
            break;
          }
        }
      }
      content = lines.join('\n');
    }

    // Remove auth imports
    if (content.includes('requireManagerAPI') || content.includes('requireAdminAPI')) {
      content = content.replace(/import { requireManagerAPI(?:, requireAdminAPI)? } from '@\/lib\/auth-utils';\s*/g, '');
      content = content.replace(/import { requireAdminAPI } from '@\/lib\/auth-utils';\s*/g, '');
      content = content.replace(/import { requireManagerAPI } from '@\/lib\/auth-utils';\s*/g, '');
      modified = true;
    }

    // Update comments
    if (modified) {
      content = content.replace(
        /(\/\/ \w+ \/api\/[\w\/-]+ - .+?) \(manager\/admin only\)/g,
        '$1 (no authentication required)'
      );
      content = content.replace(
        /(\/\/ \w+ \/api\/[\w\/-]+ - .+?) \(admin only\)/g,
        '$1 (no authentication required)'
      );
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Removed authentication from API: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No authentication wrappers found in: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 Starting authentication removal from admin pages and APIs...\n');

  let totalModified = 0;

  console.log('📄 Processing admin pages...');
  adminPages.forEach(page => {
    if (removeAuthFromAdminPage(page)) {
      totalModified++;
    }
  });

  console.log('\n🔌 Processing API routes...');
  apiRoutes.forEach(route => {
    if (removeAuthFromAPIRoute(route)) {
      totalModified++;
    }
  });

  console.log('\n📊 Summary:');
  console.log(`✅ Total files modified: ${totalModified}`);
  console.log(`📁 Admin pages processed: ${adminPages.length}`);
  console.log(`🔌 API routes processed: ${apiRoutes.length}`);
  
  if (totalModified > 0) {
    console.log('\n🎉 Authentication successfully removed from admin system!');
    console.log('🔓 All admin pages and APIs are now publicly accessible');
    console.log('💡 Booking and payment functionality will continue to work');
  } else {
    console.log('\n⚠️  No authentication found to remove');
  }
}

if (require.main === module) {
  main();
}

module.exports = { removeAuthFromAdminPage, removeAuthFromAPIRoute };
