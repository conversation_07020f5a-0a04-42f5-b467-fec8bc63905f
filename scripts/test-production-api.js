#!/usr/bin/env node

/**
 * Production API Test Script
 * Tests the info markers API endpoints to verify 502 error fixes
 */

const https = require('https');
const http = require('http');

// Configuration
const PRODUCTION_URL = 'https://victorchelemu.com';
const LOCAL_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const req = client.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
            raw: data
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: null,
            raw: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testEndpoint(name, url, options = {}) {
  log(`\n🧪 Testing: ${name}`, 'cyan');
  log(`📍 URL: ${url}`, 'blue');
  
  try {
    const response = await makeRequest(url, options);
    
    log(`📊 Status: ${response.status}`, response.status < 400 ? 'green' : 'red');
    log(`📋 Content-Type: ${response.headers['content-type'] || 'Not Set'}`, 'yellow');
    
    if (response.parseError) {
      log(`❌ JSON Parse Error: ${response.parseError}`, 'red');
      log(`📄 Raw Response (first 200 chars): ${response.raw.substring(0, 200)}...`, 'yellow');
      return false;
    }
    
    if (response.data) {
      log(`✅ JSON Response: ${response.data.success ? 'Success' : 'Failed'}`, 'green');
      if (response.data.message) {
        log(`💬 Message: ${response.data.message}`, 'blue');
      }
      if (response.data.error) {
        log(`⚠️  Error: ${response.data.error}`, 'red');
      }
    }
    
    return response.status < 400 && !response.parseError;
  } catch (error) {
    log(`❌ Request Failed: ${error.message}`, 'red');
    return false;
  }
}

async function runTests() {
  log('🚀 Starting Production API Tests', 'magenta');
  log('=' * 50, 'blue');
  
  const baseUrl = process.argv.includes('--local') ? LOCAL_URL : PRODUCTION_URL;
  log(`🌐 Testing against: ${baseUrl}`, 'cyan');
  
  const tests = [
    {
      name: 'Production Debug Endpoint',
      url: `${baseUrl}/api/debug/production`,
      options: { method: 'GET' }
    },
    {
      name: 'Info Markers List',
      url: `${baseUrl}/api/info-markers`,
      options: { method: 'GET' }
    },
    {
      name: 'MongoDB Test Endpoint',
      url: `${baseUrl}/api/test-mongodb`,
      options: { method: 'GET' }
    }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    const passed = await testEndpoint(test.name, test.url, test.options);
    if (passed) passedTests++;
  }
  
  // Test specific info marker if we can get one
  try {
    log('\n🔍 Attempting to test specific info marker...', 'cyan');
    const listResponse = await makeRequest(`${baseUrl}/api/info-markers`);
    
    if (listResponse.data && listResponse.data.success && listResponse.data.data && listResponse.data.data.length > 0) {
      const firstMarkerId = listResponse.data.data[0]._id;
      const passed = await testEndpoint(
        'Specific Info Marker',
        `${baseUrl}/api/info-markers/${firstMarkerId}`,
        { method: 'GET' }
      );
      totalTests++;
      if (passed) passedTests++;
    } else {
      log('⚠️  No info markers found to test specific endpoint', 'yellow');
    }
  } catch (error) {
    log(`⚠️  Could not test specific info marker: ${error.message}`, 'yellow');
  }
  
  // Summary
  log('\n' + '=' * 50, 'blue');
  log('📊 Test Summary', 'magenta');
  log(`✅ Passed: ${passedTests}/${totalTests}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`, totalTests - passedTests === 0 ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! API is working correctly.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Check the debug endpoint for more information.', 'yellow');
    log(`🔗 Debug URL: ${baseUrl}/api/debug/production`, 'blue');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Run the tests
runTests().catch((error) => {
  log(`💥 Test runner failed: ${error.message}`, 'red');
  process.exit(1);
});
