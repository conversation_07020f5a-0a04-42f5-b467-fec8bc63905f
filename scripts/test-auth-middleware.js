#!/usr/bin/env node

/**
 * Auth.js Middleware Test Script
 * 
 * This script tests the authentication middleware to ensure
 * it's working correctly after fixing the JWT secret issue.
 */

const { spawn } = require('child_process');
const http = require('http');

console.log('🧪 Testing Auth.js Middleware Fix\n');

// Function to test a URL
async function testUrl(url, expectedStatus, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: url,
      method: 'GET',
      headers: {
        'User-Agent': 'Auth-Test-Script/1.0'
      }
    };

    const req = http.request(options, (res) => {
      const success = res.statusCode === expectedStatus;
      console.log(`${success ? '✅' : '❌'} ${description}`);
      console.log(`   URL: ${url}`);
      console.log(`   Expected: ${expectedStatus}, Got: ${res.statusCode}`);
      
      if (!success) {
        console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
      }
      
      console.log('');
      resolve(success);
    });

    req.on('error', (err) => {
      console.log(`❌ ${description}`);
      console.log(`   URL: ${url}`);
      console.log(`   Error: ${err.message}`);
      console.log('');
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.log(`❌ ${description}`);
      console.log(`   URL: ${url}`);
      console.log(`   Error: Request timeout`);
      console.log('');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Function to check if server is running
async function checkServer() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 2000
    }, (res) => {
      resolve(true);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🔍 Checking if development server is running...');
  
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Development server is not running on localhost:3000');
    console.log('');
    console.log('Please start the development server first:');
    console.log('   npm run dev');
    console.log('');
    console.log('Then run this test script again.');
    process.exit(1);
  }
  
  console.log('✅ Development server is running');
  console.log('');
  
  console.log('🧪 Running authentication middleware tests...');
  console.log('');
  
  const tests = [
    {
      url: '/',
      expectedStatus: 200,
      description: 'Home page (public) should be accessible'
    },
    {
      url: '/auth/signin',
      expectedStatus: 200,
      description: 'Sign-in page should be accessible'
    },
    {
      url: '/admin/lodges',
      expectedStatus: 302,
      description: 'Admin lodges page should redirect to sign-in (unauthenticated)'
    },
    {
      url: '/admin',
      expectedStatus: 302,
      description: 'Admin dashboard should redirect to sign-in (unauthenticated)'
    },
    {
      url: '/api/lodges',
      expectedStatus: 200,
      description: 'Public API endpoint should be accessible'
    },
    {
      url: '/api/users',
      expectedStatus: 401,
      description: 'Protected API endpoint should return 401 (unauthenticated)'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    const success = await testUrl(test.url, test.expectedStatus, test.description);
    if (success) {
      passed++;
    } else {
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('📊 Test Results:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  console.log('');
  
  if (failed === 0) {
    console.log('🎉 All tests passed! The authentication middleware is working correctly.');
    console.log('');
    console.log('✅ The JWT secret issue has been resolved.');
    console.log('✅ Protected routes are properly secured.');
    console.log('✅ Public routes remain accessible.');
  } else {
    console.log('⚠️  Some tests failed. Please check the middleware configuration.');
    console.log('');
    console.log('Common issues to check:');
    console.log('   • NEXTAUTH_SECRET environment variable');
    console.log('   • Auth.js configuration in src/auth.js');
    console.log('   • Middleware configuration in src/middleware.js');
  }
  
  console.log('');
  console.log('📚 For more help, see:');
  console.log('   • AUTH_SETUP.md');
  console.log('   • AUTH_TROUBLESHOOTING.md');
  
  process.exit(failed === 0 ? 0 : 1);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n🛑 Test interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 Test terminated');
  process.exit(1);
});

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test script error:', error);
  process.exit(1);
});
