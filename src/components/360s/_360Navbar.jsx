'use client'
import Link from 'next/link';
import { useSearchParams } from 'next/navigation'
import React from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive';
import _360BtnMenu from './_360BtnMenuUpdate'
import _360BookNowBtn from './_360BookNowBtn'
import { useContextExperience } from '@/contexts/useContextExperience';

export default function _360Navbar() {
    const searchParams = useSearchParams()
    const { experienceState } = useContextExperience()
    const id = searchParams.get('id')

    // console.log('_360Navbar:',experienceState)

  return (
    <div className='flex absolute z-10 top-0 left-0 w-full h-fit items-center from-black bg-gradient-to-b'>
      <div className='flex w-full h-fit items-center justify-between'>
        <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-full text-lg tracking-[6px]">
          <ImageWrapperResponsive className={'w-auto h-full'} src={'/assets/elephant_island_logo_white_for_nav_bar.png'} alt='elephant island logo'/>
        </Link>
        <div className='flex items-center w-fit h-full'>
          <_360BtnMenu/>
          <_360BookNowBtn/>
        </div>
      </div>
    </div>
  )
}
