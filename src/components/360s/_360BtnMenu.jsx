'use client'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRef } from 'react'

export default function _360BtnMenu() {
  const {experienceState,disptachExperience} = useContextExperience()
  const menuRef = useRef(null)

  const handleClick = () => {
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
  }

  // console.log('_360BtnMenu:',experienceState?.showMenu || experienceState?.showBookingPopup)

  return (
    <div
      ref={menuRef}
      onClick={handleClick}
      className="flex cursor-pointer w-24 h-full relative group"
    >
      {experienceState?.showMenu || experienceState?.showBookingPopup || experienceState?.showPopup
        ? null
        : <div className='flex left-7 relative items-center justify-center w-10 h-fit'>
            <div className={`flex flex-col gap-2 items-center justify-center w-fit h-full group-hover:opacity-0 opacity-100 transition-opacity duration-200`}>
              <hr className="h-[6px] rounded-full w-10 bg-white"/>
              <hr className="h-[6px] rounded-full w-10 bg-white"/>
              <hr className="h-[6px] rounded-full w-10 bg-white"/>
            </div>
            <div className={`flex absolute m-auto flex-col gap-2 items-center justify-center w-10 h-full opacity-0 group-hover:opacity-100 transition-opacity duration-200`}>
              <hr className="absolute border-2 rotate-45 rounded-full w-10 border-white"/>
              <hr className="absolute border-2 -rotate-45 rounded-full w-10 border-white"/>
            </div>
          </div>
      }
    </div>
  )
}
