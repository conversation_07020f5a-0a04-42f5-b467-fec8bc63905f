'use client';

import { useState, useRef, useEffect } from 'react';
import { MdChevronLeft, MdChevronRight, MdImage } from 'react-icons/md';
import { preloadUIAssets } from '@/lib/ui-asset-cache';

export default function ThumbnailPanel({ 
  images, 
  currentIndex, 
  onImageSelect, 
  isTransitioning 
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollContainerRef = useRef(null);
  const thumbnailRefs = useRef([]);

  // Auto-scroll to current thumbnail
  useEffect(() => {
    if (thumbnailRefs.current[currentIndex] && scrollContainerRef.current) {
      const thumbnail = thumbnailRefs.current[currentIndex];
      const container = scrollContainerRef.current;

      const thumbnailTop = thumbnail.offsetTop;
      const thumbnailHeight = thumbnail.offsetHeight;
      const containerHeight = container.clientHeight;
      const containerScrollTop = container.scrollTop;

      // Check if thumbnail is out of view
      if (thumbnailTop < containerScrollTop ||
          thumbnailTop + thumbnailHeight > containerScrollTop + containerHeight) {

        // Scroll to center the thumbnail
        const scrollTo = thumbnailTop - (containerHeight / 2) + (thumbnailHeight / 2);
        container.scrollTo({
          top: Math.max(0, scrollTo),
          behavior: 'smooth'
        });
      }
    }
  }, [currentIndex]);

  // Preload thumbnail images for better performance
  useEffect(() => {
    if (images && images.length > 0) {
      const thumbnailUrls = images
        .filter(image => image.url)
        .map(image => image.url);

      if (thumbnailUrls.length > 0) {
        preloadUIAssets(thumbnailUrls);
      }
    }
  }, [images]);

  const handleScroll = (direction) => {
    if (!scrollContainerRef.current) return;
    
    const container = scrollContainerRef.current;
    const scrollAmount = 200;
    
    if (direction === 'up') {
      container.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
    } else {
      container.scrollBy({ top: scrollAmount, behavior: 'smooth' });
    }
  };

  const handleThumbnailClick = (index) => {
    if (isTransitioning || index === currentIndex) return;
    onImageSelect(index);
  };

  const togglePanel = () => {
    setIsExpanded(!isExpanded);
  };

  if (images.length === 0) return null;

  return (
    <div className={`absolute left-4 top-20 bottom-4 z-10 transition-all duration-300 ${
      isExpanded ? 'w-52' : 'w-12'
    }`}>
      <div className="h-full bg-black/70 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
        {/* Header */}
        <div className="p-3 border-b border-white/10 flex items-center justify-between">
          {isExpanded && (
            <h3 className="text-white font-medium text-sm">
              360° Images ({images.length})
            </h3>
          )}
          <button
            onClick={togglePanel}
            className="text-white/70 hover:text-white transition-colors p-1"
          >
            {isExpanded ? <MdChevronLeft size={20} /> : <MdChevronRight size={20} />}
          </button>
        </div>

        {isExpanded && (
          <>
            {/* Scroll Up Button */}
            <button
              onClick={() => handleScroll('up')}
              className="w-full p-2 text-white/70 hover:text-white hover:bg-white/10 transition-colors border-b border-white/10"
            >
              <MdChevronLeft size={16} className="rotate-90 mx-auto" />
            </button>

            {/* Thumbnails Container */}
            <div 
              ref={scrollContainerRef}
              className="flex-1 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/20"
              style={{ maxHeight: 'calc(100% - 120px)' }}
            >
              <div className="p-2 space-y-2">
                {images.map((image, index) => (
                  <div
                    key={image._id}
                    ref={el => thumbnailRefs.current[index] = el}
                    onClick={() => handleThumbnailClick(index)}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                      index === currentIndex 
                        ? 'ring-2 ring-blue-400 shadow-lg' 
                        : 'hover:ring-1 hover:ring-white/30'
                    } ${isTransitioning ? 'pointer-events-none' : ''}`}
                  >
                    {/* Thumbnail Image */}
                    <div className="aspect-video bg-gray-800 relative">
                      {image.url ? (
                        <img
                          src={image.url}
                          alt={image.name || 'Untitled'}
                          className="w-full h-full object-cover"
                          loading="lazy"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      
                      {/* Fallback Icon */}
                      <div 
                        className="absolute inset-0 flex items-center justify-center text-white/50"
                        style={{ display: image.url ? 'none' : 'flex' }}
                      >
                        <MdImage size={24} />
                      </div>

                      {/* Current Indicator */}
                      {index === currentIndex && (
                        <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                          <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                        </div>
                      )}

                      {/* Loading Overlay */}
                      {isTransitioning && index === currentIndex && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>

                    {/* Image Info */}
                    <div className="px-2 py-1 bg-black/50">
                      <p className="text-white text-xs font-medium truncate">
                        {image.name || 'Untitled'}
                      </p>
                      <p className="text-white/60 text-xs">
                        Priority: {image.priority || 0}
                      </p>
                    </div>

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
                  </div>
                ))}
              </div>
            </div>

            {/* Scroll Down Button */}
            <button
              onClick={() => handleScroll('down')}
              className="w-full p-2 text-white/70 hover:text-white hover:bg-white/10 transition-colors border-t border-white/10"
            >
              <MdChevronLeft size={16} className="-rotate-90 mx-auto" />
            </button>
          </>
        )}

        {/* Collapsed State */}
        {!isExpanded && (
          <div className="p-2 flex flex-col items-center space-y-2">
            <div className="text-white/70 text-xs">
              {currentIndex + 1}
            </div>
            <div className="w-1 h-8 bg-white/20 rounded-full relative">
              <div 
                className="w-1 bg-blue-400 rounded-full transition-all duration-300"
                style={{ 
                  height: `${((currentIndex + 1) / images.length) * 100}%`,
                }}
              />
            </div>
            <div className="text-white/70 text-xs">
              {images.length}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
