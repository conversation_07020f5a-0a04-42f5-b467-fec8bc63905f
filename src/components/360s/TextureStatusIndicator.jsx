'use client';

import { useState, useEffect } from 'react';
import { MdDownload, MdCheckCircle, MdPlayArrow, MdPending } from 'react-icons/md';

export default function TextureStatusIndicator({ 
  loadingQueue, 
  textureCache, 
  currentImageId,
  isVisible = true 
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Get status for current image
  const getCurrentImageStatus = () => {
    if (!currentImageId) return 'pending';
    
    if (textureCache.has(currentImageId)) {
      return 'ready';
    }
    
    const queueItem = loadingQueue.find(item => item._id === currentImageId);
    return queueItem?.status || 'pending';
  };

  // Get overall loading statistics
  const getLoadingStats = () => {
    const total = loadingQueue.length;
    const cached = loadingQueue.filter(item => textureCache.has(item._id)).length;
    const downloading = loadingQueue.filter(item => item.status === 'downloading').length;
    const pending = total - cached - downloading;

    return { total, cached, downloading, pending };
  };

  const currentStatus = getCurrentImageStatus();
  const stats = getLoadingStats();

  // Status icon and color mapping
  const getStatusDisplay = (status) => {
    switch (status) {
      case 'downloading':
        return {
          icon: <MdDownload className="animate-bounce" />,
          color: 'bg-blue-500',
          text: 'Downloading'
        };
      case 'cached':
        return {
          icon: <MdCheckCircle />,
          color: 'bg-green-500',
          text: 'Cached'
        };
      case 'ready':
        return {
          icon: <MdPlayArrow />,
          color: 'bg-emerald-500',
          text: 'Ready'
        };
      default:
        return {
          icon: <MdPending />,
          color: 'bg-gray-500',
          text: 'Pending'
        };
    }
  };

  const currentDisplay = getStatusDisplay(currentStatus);

  if (!isVisible || loadingQueue.length === 0) return null;

  return (
    <div className="fixed top-20 right-4 z-30">
      <div className="bg-black/80 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
        {/* Main Status Indicator */}
        <div 
          className="flex items-center space-x-3 p-3 cursor-pointer hover:bg-white/10 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className={`w-3 h-3 rounded-full ${currentDisplay.color} flex items-center justify-center`}>
            <div className="text-white text-xs">
              {currentDisplay.icon}
            </div>
          </div>
          
          <div className="text-white">
            <div className="text-sm font-medium">Texture Status</div>
            <div className="text-xs text-gray-300">{currentDisplay.text}</div>
          </div>
          
          <div className="text-white/70 text-xs">
            {stats.cached}/{stats.total}
          </div>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="border-t border-white/20 p-3 space-y-3">
            {/* Current Image Status */}
            <div className="space-y-2">
              <div className="text-white text-xs font-medium">Current Image</div>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${currentDisplay.color}`} />
                <span className="text-white text-xs">{currentDisplay.text}</span>
              </div>
            </div>

            {/* Overall Progress */}
            <div className="space-y-2">
              <div className="text-white text-xs font-medium">Overall Progress</div>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(stats.cached / stats.total) * 100}%` }}
                />
              </div>
              
              {/* Stats */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-green-500" />
                  <span className="text-white">Cached: {stats.cached}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <span className="text-white">Loading: {stats.downloading}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-gray-500" />
                  <span className="text-white">Pending: {stats.pending}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 rounded-full bg-white" />
                  <span className="text-white">Total: {stats.total}</span>
                </div>
              </div>
            </div>

            {/* Priority Queue Preview */}
            {stats.pending > 0 && (
              <div className="space-y-2">
                <div className="text-white text-xs font-medium">Next in Queue</div>
                <div className="space-y-1 max-h-20 overflow-y-auto">
                  {loadingQueue
                    .filter(item => !textureCache.has(item._id) && item.status !== 'downloading')
                    .slice(0, 3)
                    .map((item, index) => (
                      <div key={item._id} className="flex items-center space-x-2 text-xs">
                        <div className="w-1.5 h-1.5 rounded-full bg-gray-400" />
                        <span className="text-gray-300 truncate">
                          {item.name || 'Untitled'} (P:{item.priority || 0})
                        </span>
                      </div>
                    ))
                  }
                  {stats.pending > 3 && (
                    <div className="text-xs text-gray-400">
                      +{stats.pending - 3} more...
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
