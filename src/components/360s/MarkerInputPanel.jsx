'use client';

import { useState, useRef, useEffect, useMemo } from 'react';
import { MdChevronLeft, MdChevronRight, MdImage } from 'react-icons/md';
import MarkersInputList from './MarkersInputList';
import { useContextExperience } from '@/contexts/useContextExperience';

export default function MarkerInputPanel({
  _360Object,
  _360sList,
  set_360Object
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollContainerRef = useRef(null);
  const thumbnailRefs = useRef([]);

  const togglePanel = () => {
    setIsExpanded(!isExpanded);
  };
  
  // console.log('MarkerInputPanel:',_360Object)

  return (
    <div className={`absolute right-4 top-20 bottom-4 z-10 transition-all duration-300 ${
      isExpanded ? 'w-80' : 'w-12'
    }`}>
      <div className="h-full bg-black/70 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
        {/* Header */}
        <div className="p-3 border-b border-white/10 flex items-center justify-between">
          <button
            onClick={togglePanel}
            className="text-white/70 hover:text-white transition-colors p-1"
            >
            {!isExpanded ? <MdChevronLeft size={20} /> : <MdChevronRight size={20} />}
          </button>
          {isExpanded && (
            <h3 className="text-white font-medium text-sm">
              Marker Input Panel
            </h3>
          )}
        </div>

        {isExpanded && (
          <>
            <div className="p-3 h-[calc(100%-64px)]">
              <MarkersInputList
                _360Object={_360Object}
                _360sList={_360sList}
                set_360Object={set_360Object}
              />
            </div>
          </>
        )}

        {/* Collapsed State */}
        {!isExpanded && (
          <div className="p-2 flex flex-col items-center space-y-2">
          </div>
        )}
      </div>
    </div>
  );
}
