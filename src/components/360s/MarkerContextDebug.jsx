'use client'

import React from 'react'
import { useMarkerContext } from '@/contexts/MarkerContext'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useMarkerContextWithFallback, useExperienceContextWithFallback } from '@/hooks/useSafeContext'

/**
 * MarkerContextDebug - Debug component to verify Context integration
 * This component displays the current state of both MarkerContext and ExperienceContext
 * for debugging purposes. Remove or disable in production.
 */
export default function MarkerContextDebug() {
  const {
    selectedMarker,
    activeMarkerId,
    isMarkerPopupOpen,
    markerState
  } = useMarkerContextWithFallback()

  const { experienceState } = useExperienceContextWithFallback()

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50 border border-gray-600">
      <h3 className="font-bold text-sm mb-2 text-yellow-400">Context Debug</h3>
      
      <div className="space-y-2">
        <div>
          <h4 className="font-semibold text-green-400">MarkerContext:</h4>
          <div className="ml-2 space-y-1">
            <div>Selected Marker: {selectedMarker?.name || 'None'}</div>
            <div>Marker Type: {selectedMarker?.markerType || 'None'}</div>
            <div>Active ID: {activeMarkerId || 'None'}</div>
            <div>Popup Open: {isMarkerPopupOpen ? 'Yes' : 'No'}</div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-blue-400">ExperienceContext:</h4>
          <div className="ml-2 space-y-1">
            <div>Show Popup: {experienceState?.showPopup ? 'Yes' : 'No'}</div>
            <div>Show Gallery: {experienceState?.showGalleryStore ? 'Yes' : 'No'}</div>
            <div>Show Video: {experienceState?.showVideoGallery ? 'Yes' : 'No'}</div>
            <div>Show Info: {experienceState?.showItemInfo ? 'Yes' : 'No'}</div>
            <div>Experience ID: {experienceState?.id || 'None'}</div>
          </div>
        </div>

        {selectedMarker && (
          <div>
            <h4 className="font-semibold text-purple-400">Selected Marker Details:</h4>
            <div className="ml-2 space-y-1">
              <div>Name: {selectedMarker.name}</div>
              <div>Type: {selectedMarker.markerType}</div>
              <div>ID: {selectedMarker.id || 'N/A'}</div>
              <div>360Name: {selectedMarker._360Name || 'N/A'}</div>
              <div>Position: ({selectedMarker.x}, {selectedMarker.y}, {selectedMarker.z})</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
