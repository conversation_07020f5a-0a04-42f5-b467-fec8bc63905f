'use client';

import { useState, useEffect } from 'react';
import { MdWifiOff, MdClose } from 'react-icons/md';

/**
 * OfflineNotification Component
 * Shows an alert popup ONLY when the user goes offline
 * Does not show notification when coming back online
 * 
 * @param {boolean} isOnline - Current network connectivity status
 */
export default function OfflineNotification({ isOnline }) {
  const [showNotification, setShowNotification] = useState(false);
  const [hasBeenOnline, setHasBeenOnline] = useState(true);

  useEffect(() => {
    // Track if user has been online before
    if (isOnline) {
      setHasBeenOnline(true);
      setShowNotification(false); // Hide notification when back online
    } else if (hasBeenOnline) {
      // Only show notification if user was previously online and now goes offline
      setShowNotification(true);
    }
  }, [isOnline, hasBeenOnline]);

  // Auto-hide notification after 10 seconds
  useEffect(() => {
    if (showNotification) {
      const timer = setTimeout(() => {
        setShowNotification(false);
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [showNotification]);

  // Don't render if notification should not be shown
  if (!showNotification) {
    return null;
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 animate-slide-down">
      <div className="bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg border border-red-600 flex items-center gap-3 min-w-80">
        {/* Offline Icon */}
        <MdWifiOff className="text-xl flex-shrink-0" />
        
        {/* Message */}
        <div className="flex-1">
          <p className="font-medium text-sm">You're offline</p>
          <p className="text-xs text-red-100">
            Check your internet connection. Some features may not work properly.
          </p>
        </div>
        
        {/* Close Button */}
        <button
          onClick={() => setShowNotification(false)}
          className="text-red-100 hover:text-white transition-colors p-1 rounded"
          aria-label="Close notification"
        >
          <MdClose className="text-lg" />
        </button>
      </div>
    </div>
  );
}
