'use client'

import ImageWrapperResponsive from "../ImageWrapperResponsive"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { ACTIONS_EXPERIENCE_STATE } from "@/contexts/reducerExperience"
import { useContextExperience } from "@/contexts/useContextExperience"

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit h-full'
        >
            <div 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-fit'} width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-fit'} width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function _360BookNowBtn() {  
    const {disptachExperience,experienceState}=useContextExperience()  
    const handleClick = () => {
        // console.log('click')
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
    }
    
    // console.log('_360BookNowBtn:',experienceState?.showPopup)  
  return (
    <div 
        onClick={handleClick} 
        className='flex w-fit h-fit justify-end items-center'
    >
        <BtnLandingpageComponent 
            data={{width:104,height:46,btnIcons:{off:'/assets/book_btn_off.png',ov:'/assets/book_btn_ov.png'}}}
        />
    </div>
  )
}
