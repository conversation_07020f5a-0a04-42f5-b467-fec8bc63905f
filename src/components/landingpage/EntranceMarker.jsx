import Image from 'next/image'
import React, { useState } from 'react'
import ImageScalerComponent from '../ImageScalerComponent'
import { settings } from '@/lib/settings'
import ImageWrapperResponsive from '../ImageWrapperResponsive'


function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                // onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={settings?.markerList?.markerTypeIcons?.enterButton?.btnIcons?.ov}/>
            </div>
            <div
                // onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={settings?.markerList?.markerTypeIcons?.enterButton?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function EntranceMarker() {
  return (
    <div className="flex text-white flex-col items-center cursor-pointer select-none absolute text-center left-0 right-0 bottom-0 mx-auto bg-black/75' justify-end w-[332px] h-[541px] bg-black/25' p-2">
        <div className="flex relative max-w-fit max-h-fit">
          <ImageScalerComponent alt='swipe info icon' src={'/assets/elephant_island_logo.png'}/>
        </div>

        <div className="flex relative items-center  text-center flex-col uppercase mb-2">
        <span className="font-medium text-nowrap text-2xl">your safari holiday</span>
        <span className="font-bold text-4xl">destination</span>
        </div>

        <div className="flex relative max-h-fit max-w-fit rounded-full">
          <ImageScalerComponent alt='swipe info icon' src={'/assets/swipe_icon.png'}/>
        </div>

        <span className="font-bold tracking-tighter text-center text-lg uppercase mb-3">look around and explore</span>
        <BtnLandingpageComponent data={settings}/>
    </div>
  )
}
