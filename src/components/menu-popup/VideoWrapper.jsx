'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'
import VideoPlayer from './VideoPlayer'
import Image from 'next/image'

function RoverOverButton({data,handleVideoClick}) {
  const [onhover,setOnHover]=useState(false)
  return(
    <div 
      onClick={()=>handleVideoClick(data)}
      onMouseEnter={()=>setOnHover(true)} 
      onMouseLeave={()=>setOnHover(false)} 
      className='z-10 absolute w-fit h-fit m-auto'
    >
        {onhover ? <ImageWrapperResponsive src={'assets/video_btn_ov.png'}/> :
        <ImageWrapperResponsive src={'assets/video_btn_off.png'}/>}
    </div>
  )
}

export default function VideoWrapper() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const [showVideoPlayer,setShowVideoPlayer]=useState(false) // show video player state
  const [videoData,setVideoData]=useState({}) // show video player state
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/video-gallery`)
      const responseData=await serverResponse.json()
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      // console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    // console.log(data)
    const filterVideoByTitle = () => {
      console.log(data,experienceState?.videoDetials)
      if (!data || !experienceState?.videoDetials) return;
      const filteredVideo = data.find(item => item.title === experienceState.videoDetials);
      console.log(filteredVideo)
      if (filteredVideo) {
        setShowVideoPlayer(false);
        setVideoData([filteredVideo]);
        setShowVideoPlayer(true);
      }
    }
    filterVideoByTitle();
  }, [data,experienceState?.videoDetials])

  const handleVideoClick = () => { 
    console.log('handleVideoClick:',experienceState?.videoDetials)
    
    // console.log('handleVideoClick:',data)
    console.log(data?.find(({title})=>title==experienceState?.videoDetials))
    setShowVideoPlayer(false)
    setVideoData(data?.filter(item=>item?.title==experienceState?.videoDetials))
    setShowVideoPlayer(true)
  }
  
  // console.log('VideoWrapper:',videoData)
  
  return (
    <div className='video-wrapper flex m-auto w-full h-full text-white'>
      {loading 
        ? <SpinerComponent/>  
        : showVideoPlayer && videoData 
            && <VideoPlayer 
              data={videoData} 
              setShowVideoPlayer={setShowVideoPlayer}
            />
      }
    </div>
  )
}
