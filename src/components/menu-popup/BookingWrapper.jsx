'use client'
import { useEffect, useLayoutEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'
import Image from 'next/image'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import {createRoot} from 'react-dom/client';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import IslandAndExperiencesPopup from './PopupIslandAndExperiences'
import {APIProvider, Map} from '@vis.gl/react-google-maps';
import PopupIslandAndExperiences from './PopupIslandAndExperiences'
import PopupTestimonials from './PopupTestimonials'
import PagePopupLocationAndContact from './PagePopupLocationAndContact'
import { HiX } from 'react-icons/hi'
import _360BookNowBtn from '../360s/_360BookNowBtn'


export default function BookingWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const {experienceState,disptachExperience}=useContextExperience()
    const [pageInfo,setPageInfo]=useState([])
    const [closePopup,setClosePopup]=useState(false)
    const [showPages,setShowPages]=useState(false)
    // console.log('PopupWrapper:',query)

    const handleBookingClose=()=>{
      // console.log('booking close')
      disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
      // setClosePopup(true)
    }

    const handlePopupClose=()=>{
      {experienceState?.showTheIslandPage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_ISLAND_PAGE})}
      {experienceState?.showExperiencePage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_EXPERIENCE_PAGE})}
      {experienceState?.showTestimonials && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})}
      {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})}
    }

    const closeButton=()=>{
      console.log('close button click')
      setClosePopup(false)
      setShowPages(false)
    }    

    useEffect(() => {
      const fetchPageInfo = async () => {
        try {
          const res = await fetch('/api/pages', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (!res.ok) {
            throw new Error('Failed to fetch page info');
          }
          
          const data = await res.json();
          // console.log(data)
          setPageInfo(data?.data)
          return data;
        } catch (error) {
          console.error('Error fetching page info:', error);
        }
      };
      fetchPageInfo()
    }, [])
    
    // console.log('BookingWrapper:',pageInfo)

  return (
    // (closePopup &&
      <>
        {experienceState?.showBookingPopup && <div className='popup-wrapper flex z-20 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto'>
          <div
            onClick={handleBookingClose}
            className=" flex fixed z-10 items-center justify-center top-0 right-0 h-[75px] w-fit text-4xl text-white cursor-pointer"
          >
            <HiX className='mb-2'/>
            <div className='w-fit h-fit invisible'>
              <_360BookNowBtn/>
            </div>
          </div>
          <div className='flex relative top-0 left0 h-fit px-5 md:w-[995px] mx-auto'>
            <BookingFormComponent/>
          </div>
        </div>}
        {experienceState?.showTheIslandPage && <PopupIslandAndExperiences data={pageInfo?.island}/>}
        {experienceState?.showExperiencePage && <PopupIslandAndExperiences data={pageInfo?.experiences}/>}
        {experienceState?.showTestimonials && <PopupTestimonials data={pageInfo?.testimonials}/>}
        {experienceState?.showLocationAndContacts && <PagePopupLocationAndContact data={pageInfo?.locationAndcontacts}/>}
      </>
    // )
  )
}