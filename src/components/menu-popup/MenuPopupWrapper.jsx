'use client'
import { settings } from '@/lib/settings'
import React, { useRef, useState } from 'react'
import Image from 'next/image'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRouter, useSearchParams } from 'next/navigation'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import MenuWrapper from './MenuWrapper'
import { HiX } from 'react-icons/hi'
import _360BookNowBtn from '../360s/_360BookNowBtn'

function BtnLandingpageComponent({data,fn,fnEntrance,fnHome,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',data?.naem,index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            onClick={data?.name=='home' ? fnHome : data?.name=='entrance' ? fnEntrance : ()=>fn(data?.name,data?.location)}
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function MenuPopupWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const refGroup=useRef()
    const {experienceState,disptachExperience}=useContextExperience()
    const router=useRouter()
    const searchParams=useSearchParams()
    const query=searchParams.get('id')
    // console.log('MenuPopupWrapper:',query)

    const handle360=(name,location)=>{
        // console.log('handle360:',name,location)
        // disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
        // router.push(`/360s?id=${location}_${name}`)
    }

    const handleEntrance=()=>{
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
        router.push(`/360s?id=entrance_360`)
    }

    const handleHome=()=>{
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
        router.push(`/`)
    }

    // console.log('MenuPopupWrapper:',experienceState?.showMenu)

  return (
    (experienceState?.showMenu && <div className='menu-Wrapper flex z-20 absolute top-0 left-0 w-full h-full bg-black/75'>
      <div className='popup-wrapper flex z-10 absolute text-white top-0 left-0 w-full h-full overflow-hidden'>
        <div 
            onClick={e=>disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})} 
            // onClick={e=>console.log('clicked')} 
            // onClick={e=>handle360('')} 
            className=" flex z-40 items-center justify-center absolute top-0 text-4xl mr-2 right-0 h-[75px] w-fit cursor-pointer"
        >
          <HiX />
          <div className='w-fit h-fit invisible'>
            <_360BookNowBtn/></div>
          </div>
        <div className='relative mx-auto px-2 w-full h-full z-20'>
          <div className='flex absolute bottom-0 left-2 md:left-[75px] w-full h-[calc(100%-75px)] overflow-y-auto overflow-x-hidden'>
            <MenuWrapper/>
          </div>
          {/* <div className='flex absolute top-[75px] right-0 h-fit md:h-[75px] max-w-fit from-black bg-gradient-to-l min-w-48 justify-end'>
            <span className='flex items-center justify-center mr-6 text-2xl uppercase'>
              {
                query?.includes('liv') 
                  ? 'lounge' : 
                query?.includes('lou') 
                  ? 'lounge' : 
                  query?.includes('din')
                  ? 'dining': 
                  query?.includes('bedroom 1')
                  ? 'bedroom 1' : 
                  query?.includes('bedroom 2')
                  ? 'bedroom 2' : 
                  query?.includes('mast')
                  ? 'master bedroom' : null
              }
            </span>
          </div> */}
        </div>
      </div>
    </div>)
  )
}
