import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import Image from 'next/image'
import React from 'react'
import HtmlContentDisplay from '@/components/HtmlContentDisplay'
import { HiX } from 'react-icons/hi'
import _360BookNowBtn from '../360s/_360BookNowBtn'
import { cleanTextFields } from '@/utils/textUtils'

export default function PopupIslandAndExperiences({data}) {
  const {experienceState,disptachExperience}=useContextExperience()

  // Clean HTML tags and markup from the data before displaying
  const cleanedData = data ? cleanTextFields(data, ['title', 'body1']) : null;

  const handlePopupClose=()=>{
    // console.log('handlePopupCLose',experienceState?.showTheIslandPage)
    // console.log('handlePopupCLose',experienceState?.showExperiencePage)
    {experienceState?.showTheIslandPage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_ISLAND_PAGE})}
    {experienceState?.showExperiencePage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_EXPERIENCE_PAGE})}
  }
  // console.log('PopupIslandAndExperiences:',data)
  return (
    <div className='popu-island-experiences flex z-30 absolute top-0 left-0 w-full h-full -bg-black/75 overflow-hidden overflow-y-auto text-white items-center justify-center'>
      <div className='popup-wrapper text-white flex z-10 absolute top-0 left-0 w-full h-full bg-black/85 overflow-y-auto'>
        <div 
          onClick={handlePopupClose} 
          className=" flex z-40 items-center text-4xl text-white justify-center fixed right-0 top-[0] h-[75px] w-fit cursor-pointer"
        >
          <HiX className='mb-2'/>
          <div className='w-fit h-fit invisible'><_360BookNowBtn/></div>
        </div>
        <div className='flex relative top-[75px] left-0 h-fit px-5 md:w-[995px] mx-auto text-white mb-40'>
          <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
            <div className='flex font-thin w-full h-auto items-center justify-center relative'>
              <img src={data?.image} alt='page image' className='object-cover h-auto w-full'/>
            </div>
            <div className='flex w-full h-fit gap-10 flex-col lg:flex-row'>
              <div className='flex flex-col lg:flex-row flew-row w-full h-fit gap-4'>
                <div className='w-60 text-6xl text-left text-wrap leading-12'>
                  <HtmlContentDisplay htmlString={data?.title}/>
                </div>
                <div className='flex flex-col max-w-full md:min-w-[676px] gap-4'>
                  <div className='text-left text-[28px] leading-8'>
                    <HtmlContentDisplay htmlString={data?.body1}/>
                  </div>
              </div>
              </div>
            </div>
            <div className='flex flex-col w-full h-fit gap-20'>
              {data?.additionalContent?.map((i,index)=>{
                // Clean each additional content item
                const cleanedItem = cleanTextFields(i, ['title', 'body1']);
                return (
                  <div key={index} className='flex flex-col md:flex-col lg:flex-row md:even:flex-row-reverse w-full h-fit items-start justify-start gap-4'>
                    <div className='flex h-fit w-fit relative items-center justify-center'>
                      <img src={cleanedItem?.image} alt='page image' className='object-cover w-auto h-full'/>
                    </div>
                    <div className='flex max-w-full lg:w-[calc(100%-474px)] flex-col h-fit gap-4'>
                      <div className='w-full text-[40px] text-left leading-8'>
                        <HtmlContentDisplay htmlString={data?.title}/>
                      </div>
                      <div>
                      <div className='w-full text-left leading-6 text-[20px]'>
                        <HtmlContentDisplay htmlString={data?.body1}/>
                      </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
          </div>
      <div className='popup-island/experiences flex flex-col mt-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
      </div>
    </div>
  )
}
