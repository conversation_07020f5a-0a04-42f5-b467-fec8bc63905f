'use client'
import React from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useEffect, useState } from 'react'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import SpinerComponent from '../SpinerComponent'

function GalleryItem(params) {
  return(
    <div className='popupWrapper flex z-10 fixed top-0 left-0 w-full h-full bg-black/80'>
      <div
        onClick={()=>params.setShowGalleyItem(false)}
        className=" flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
      >
        <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
      </div>
      <div onClick={params?.handleGalleryCLick} className='flex relative w-full h-full items-center justify-center'>
        <ImageWrapperResponsive
          src={params?.item?.image[0]}
        />
      </div>
    </div>
  )
}

export default function GalleryStoreComponent() {
  const [error,setError]=useState('')
    const [showError,setShowError]=useState(false)
    const [loading,setLoading]=useState(false)
    const [showGalleyItem,setShowGalleyItem]=useState(false)
    const [data,setData]=useState(null)
    const [imageData,setImageData]=useState(null)
    const {experienceState,disptachExperience}=useContextExperience()
  
    const fetchData = async (id) => {
      try {
        setLoading(true)
        const serverResponse=await fetch(`/api/stores`)
        const responseData=await serverResponse.json()
        if(!data){
          setError('Failed to load data')
          setShowError(true)
        }
        // console.log(responseData?.data)
        setData(responseData?.data)
        setLoading(false)
        // return responseData
      } catch (error) {
        console.log(error)
        setError(error.message)
        setShowError(true)
      }
    }
  
    useEffect(() => {
      fetchData(experienceState?.showItemInfo?.id)
    }, [experienceState?.showItemInfo?.id])

    const handleGalleryCLick = (item) => {
      setShowGalleyItem(true)
      setImageData(item)
    }
    
    
    // console.log('ItemInfoComponent:',data)
    
    return (
      <div className='GalleryStoreComponent flex w-full h-fit text-white'>
        {loading 
          ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>
          : <div className='flex mt-16 w-full h-full items-start justify-start'>
              <div className='flex flex-col'>
                {data?.map((item) => (
                  <div 
                    onClick={()=>handleGalleryCLick(item)} 
                    key={item._id} 
                    className='flex hover:opacity-85 duration-300 ease-linear cursor-pointer relative w-full h-fit'
                  >
                    <ImageWrapperResponsive
                      src={item.image[0]}
                    />
                  </div>
                ))} 
              </div>
            </div>
        }
        {showGalleyItem && imageData && <GalleryItem item={imageData} setShowGalleyItem={setShowGalleyItem}/>}
      </div>
    )
}
