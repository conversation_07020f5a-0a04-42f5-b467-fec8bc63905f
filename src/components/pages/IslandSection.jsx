'use client';

import React, { useState, useCallback, useEffect } from 'react';
import TextEditor from '@/components/common/TextEditor';
import { ActionButton } from '@/components/pages/ActionButtonGroup';

const IslandSection = ({ 
  pages, 
  onSave, 
  isLoading 
}) => {
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    image: '',
    body1: '',
    additionalContent: []
  });

  const [errors, setErrors] = useState({});
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);

  // Additional content form states
  const [showAdditionalContentForm, setShowAdditionalContentForm] = useState(false);
  const [additionalContentFormData, setAdditionalContentFormData] = useState({
    image: '', 
    title: '', 
    body1: ''
  });
  const [additionalContentImageFile, setAdditionalContentImageFile] = useState(null);
  const [additionalContentImagePreview, setAdditionalContentImagePreview] = useState('');
  const [uploadingAdditionalContentImage, setUploadingAdditionalContentImage] = useState(false);

  // Edit mode state management for additional content
  const [editingAdditionalContent, setEditingAdditionalContent] = useState(null);

  // Main image state management
  const [mainImageFile, setMainImageFile] = useState(null);
  const [mainImagePreview, setMainImagePreview] = useState('');
  const [uploadingMainImage, setUploadingMainImage] = useState(false);

  // Initialize form data when pages prop changes
  useEffect(() => {
    if (pages?.island) {
      setFormData({
        title: pages.island.title || '',
        image: pages.island.image || '',
        body1: pages.island.body1 || '',
        additionalContent: pages.island.additionalContent || []
      });
    }
  }, [pages]);

  // Fetch section data from server
  const fetchSectionData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages');
      const result = await response.json();

      if (result.success && result.data?.island) {
        setFormData({
          title: result.data.island.title || '',
          image: result.data.island.image || '',
          body1: result.data.island.body1 || '',
          additionalContent: result.data.island.additionalContent || []
        });
      } else {
        throw new Error(result.message || 'Failed to fetch island data');
      }
    } catch (error) {
      console.error('Error fetching island data:', error);
      setDataError('Failed to load island data. Please try again.');
    } finally {
      setIsLoadingData(false);
    }
  }, []);

  // Validation
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.title?.trim()) {
      newErrors['title'] = 'Island title is required';
    }
    if (!formData.body1?.trim()) {
      newErrors['body1'] = 'Island body1 is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle field changes
  const handleQuillChange = useCallback((content, field) => {
    setFormData(prev => ({
      ...prev,
      [field]: content
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  }, [errors]);

  // Handle main image changes
  const handleMainImageChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      setMainImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setMainImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  }, []);

  // Upload main image
  const uploadMainImage = useCallback(async () => {
    if (!mainImageFile) return null;

    setUploadingMainImage(true);

    try {
      const uploadFormData = new FormData();
      uploadFormData.append('file', mainImageFile);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: uploadFormData,
      });

      const data = await response.json();

      if (data.success && data.files && data.files.length > 0) {
        return data.files[0].url;
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Main image upload error:', error);
      throw error;
    } finally {
      setUploadingMainImage(false);
    }
  }, [mainImageFile]);

  const handleMainImageSave = useCallback(async () => {
    try {
      if (!mainImageFile) {
        setErrors(prev => ({
          ...prev,
          image: 'Please select an image to upload'
        }));
        return;
      }

      const imageUrl = await uploadMainImage();

      // Update form data with the new image URL
      setFormData(prev => ({
        ...prev,
        image: imageUrl
      }));

      // Reset image upload state
      setMainImageFile(null);
      setMainImagePreview('');

      // Clear any errors
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.image;
        return newErrors;
      });

    } catch (error) {
      console.error('Save main image error:', error);
      setErrors(prev => ({
        ...prev,
        image: 'Failed to upload image: ' + error.message
      }));
    }
  }, [mainImageFile, uploadMainImage]);

  const handleRemoveMainImage = useCallback(() => {
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    setMainImageFile(null);
    setMainImagePreview('');
  }, []);

  // Handle additional content form changes
  const handleAdditionalContentFormChange = useCallback((field, value) => {
    setAdditionalContentFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Handle additional content image changes
  const handleAdditionalContentImageChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      setAdditionalContentImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setAdditionalContentImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  }, []);

  // Upload additional content image
  const uploadAdditionalContentImage = useCallback(async () => {
    if (!additionalContentImageFile) return null;

    setUploadingAdditionalContentImage(true);

    try {
      const uploadFormData = new FormData();
      uploadFormData.append('file', additionalContentImageFile);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: uploadFormData,
      });

      const data = await response.json();

      if (data.success && data.files && data.files.length > 0) {
        return data.files[0].url;
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploadingAdditionalContentImage(false);
    }
  }, [additionalContentImageFile]);

  // Save additional content
  const handleSaveAdditionalContent = useCallback(async () => {
    try {
      // Validate required fields
      if (!additionalContentFormData.title || !additionalContentFormData.body1) {
        setErrors(prev => ({
          ...prev,
          additionalContent: 'All fields are required'
        }));
        return;
      }

      if (!additionalContentImageFile && !additionalContentFormData.image) {
        setErrors(prev => ({
          ...prev,
          additionalContent: 'Image is required'
        }));
        return;
      }

      let imageUrl = additionalContentFormData.image;

      // Upload new image if selected
      if (additionalContentImageFile) {
        imageUrl = await uploadAdditionalContentImage();
      }

      const itemData = {
        image: imageUrl,
        title: additionalContentFormData.title,
        body1: additionalContentFormData.body1
      };

      // Update or add item based on edit mode
      setFormData(prev => ({
        ...prev,
        additionalContent: editingAdditionalContent !== null
          ? prev.additionalContent.map((item, index) =>
              index === editingAdditionalContent ? itemData : item
            )
          : [...prev.additionalContent, itemData]
      }));

      // Reset form and edit state
      setAdditionalContentFormData({ image: '', title: '', body1: '' });
      setAdditionalContentImageFile(null);
      setAdditionalContentImagePreview('');
      setEditingAdditionalContent(null);
      setShowAdditionalContentForm(false);

      // Clear any errors
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.additionalContent;
        return newErrors;
      });

    } catch (error) {
      console.error('Save additional content error:', error);
      setErrors(prev => ({
        ...prev,
        additionalContent: 'Failed to save content: ' + error.message
      }));
    }
  }, [additionalContentFormData, additionalContentImageFile, editingAdditionalContent, uploadAdditionalContentImage]);

  // Remove additional content
  const handleRemoveAdditionalContent = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      additionalContent: prev.additionalContent.filter((_, i) => i !== index)
    }));
  }, []);

  // Edit additional content
  const handleEditAdditionalContent = useCallback((index) => {
    const item = formData.additionalContent[index];

    // Populate form with existing data
    setAdditionalContentFormData({
      image: item.image,
      title: item.title,
      body1: item.body1
    });

    // Set edit mode
    setEditingAdditionalContent(index);
    setShowAdditionalContentForm(true);

    // Clear any existing image file/preview since we're editing
    setAdditionalContentImageFile(null);
    setAdditionalContentImagePreview('');
  }, [formData]);

  const handleCancelEditAdditionalContent = useCallback(() => {
    setEditingAdditionalContent(null);
    setShowAdditionalContentForm(false);
    setAdditionalContentFormData({ image: '', title: '', body1: '' });
    setAdditionalContentImageFile(null);
    setAdditionalContentImagePreview('');
  }, []);

  // Handle section save
  const handleSectionSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await onSave({ island: formData });
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Island Page Content</h3>
        <div className="flex items-center gap-2">
          <ActionButton
            variant="outlinedPrimary"
            onClick={fetchSectionData}
            disabled={isLoadingData}
            loading={isLoadingData}
            loadingText="Refreshing..."
          >
            {isLoadingData ? 'Refreshing...' : 'Refresh Data'}
          </ActionButton>
          <ActionButton
            variant="success"
            onClick={handleSectionSave}
            disabled={isLoading}
            loading={isLoading}
            loadingText="Saving..."
          >
            {isLoading ? 'Saving...' : 'Save Island Section'}
          </ActionButton>
        </div>
      </div>

      {/* Data Error Display */}
      {dataError && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{dataError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoadingData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-sm text-blue-800">Loading latest island data...</p>
          </div>
        </div>
      )}

      {/* Title */}
      <div>
        <label htmlFor="island-title" className="block text-sm font-medium text-gray-700 mb-2">
          Title *
        </label>
        <div className={`${errors.title ? 'border-red-500' : ''}`}>
          <TextEditor
            value={formData.title}
            onChange={(content) => handleQuillChange(content, 'title')}
            placeholder="Enter island page title"
            style={{ minHeight: '80px' }}
            className={`border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
          />
        </div>
        {errors.title && (
          <p className="mt-1 text-sm text-red-600">{errors.title}</p>
        )}
      </div>

      {/* Main Image */}
      <MainImageUploadSection
        formData={formData}
        imageFile={mainImageFile}
        imagePreview={mainImagePreview}
        onImageChange={handleMainImageChange}
        uploading={uploadingMainImage}
        onSave={handleMainImageSave}
        onRemove={handleRemoveMainImage}
        errors={errors}
      />

      {/* Body 1 */}
      <div>
        <label htmlFor="island-body1" className="block text-sm font-medium text-gray-700 mb-2">
          Body 1 *
        </label>
        <div className={`${errors.body1 ? 'border-red-500' : ''}`}>
          <TextEditor
            value={formData.body1}
            onChange={(content) => handleQuillChange(content, 'body1')}
            placeholder="Enter first body content"
            style={{ minHeight: '120px' }}
            className={`border rounded-md ${errors.body1 ? 'border-red-500' : 'border-gray-300'}`}
          />
        </div>
        {errors.body1 && (
          <p className="mt-1 text-sm text-red-600">{errors.body1}</p>
        )}
      </div>

      {/* Additional Content Section */}
      <AdditionalContentSection
        formData={formData}
        showForm={showAdditionalContentForm}
        setShowForm={setShowAdditionalContentForm}
        additionalContentFormData={additionalContentFormData}
        onFormChange={handleAdditionalContentFormChange}
        imagePreview={additionalContentImagePreview}
        onImageChange={handleAdditionalContentImageChange}
        uploading={uploadingAdditionalContentImage}
        onSave={handleSaveAdditionalContent}
        onRemove={handleRemoveAdditionalContent}
        errors={errors}
        editingIndex={editingAdditionalContent}
        onEdit={handleEditAdditionalContent}
        onCancelEdit={handleCancelEditAdditionalContent}
      />
    </div>
  );
};

// Main Image Upload Section Component
const MainImageUploadSection = React.memo(({
  formData,
  imageFile,
  imagePreview,
  onImageChange,
  uploading,
  onSave,
  onRemove,
  errors
}) => (
  <div className="space-y-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Main Image
      </label>

      {/* Current Image Display */}
      {formData.image && !imagePreview && (
        <div className="mb-3">
          <div className="flex items-center space-x-3">
            <img
              src={formData.image}
              alt="Current main image"
              className="w-20 h-20 object-cover rounded border"
            />
            <div className="flex-1">
              <p className="text-sm text-gray-600">Current main image</p>
              <button
                type="button"
                onClick={onRemove}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Remove Image
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Upload Input */}
      <div className="flex items-center space-x-3">
        <input
          type="file"
          accept="image/*"
          onChange={onImageChange}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {imageFile && (
          <button
            type="button"
            onClick={onSave}
            disabled={uploading}
            className="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {uploading ? 'Uploading...' : 'Upload Image'}
          </button>
        )}
      </div>

      {/* Image Preview */}
      {imagePreview && (
        <div className="mt-3">
          <p className="text-sm text-gray-600 mb-2">Preview:</p>
          <img
            src={imagePreview}
            alt="Preview"
            className="w-32 h-32 object-cover rounded border"
          />
        </div>
      )}

      {/* Error Display */}
      {errors.image && (
        <p className="mt-1 text-sm text-red-600">{errors.image}</p>
      )}
    </div>
  </div>
));

// Additional Content Section Component
const AdditionalContentSection = React.memo(({
  formData,
  showForm,
  setShowForm,
  additionalContentFormData,
  onFormChange,
  imagePreview,
  onImageChange,
  uploading,
  onSave,
  onRemove,
  errors,
  editingIndex,
  onEdit,
  onCancelEdit
}) => (
  <div className="space-y-4">
    <div className="border-t border-gray-200 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-md font-medium text-gray-900">Additional Content</h4>
        <button
          type="button"
          onClick={() => {
            if (showForm && editingIndex !== null) {
              onCancelEdit();
            } else {
              setShowForm(!showForm);
            }
          }}
          className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {showForm
            ? (editingIndex !== null ? 'Cancel Edit' : 'Cancel')
            : 'Add Additional Content'
          }
        </button>
      </div>

      {/* Display existing additional content */}
      {formData.additionalContent && formData.additionalContent.length > 0 && (
        <div className="space-y-3 mb-4">
          <h5 className="text-sm font-medium text-gray-700">Current Additional Content:</h5>
          {formData.additionalContent.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-3">
                {item.image && (
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-12 h-12 object-cover rounded"
                  />
                )}
                <div>
                  <p className="text-sm font-medium text-gray-900" dangerouslySetInnerHTML={{ __html: item.title }} />
                  <p className="text-xs text-gray-500">Content item {index + 1}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => onEdit(index)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Edit
                </button>
                <button
                  type="button"
                  onClick={() => onRemove(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Additional content form */}
      {showForm && (
        <div className="space-y-4 p-4 bg-gray-50 rounded-md">
          <h5 className="text-sm font-medium text-gray-700">Add New Content</h5>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image *
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={onImageChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            {imagePreview && (
              <div className="mt-2">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-20 h-20 object-cover rounded"
                />
              </div>
            )}
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <TextEditor
              value={additionalContentFormData.title}
              onChange={(content) => onFormChange('title', content)}
              placeholder="Enter content title"
              style={{ minHeight: '60px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          {/* Body 1 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Body 1 *
            </label>
            <TextEditor
              value={additionalContentFormData.body1}
              onChange={(content) => onFormChange('body1', content)}
              placeholder="Enter first body content"
              style={{ minHeight: '100px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          {/* Error display */}
          {errors.additionalContent && (
            <p className="text-sm text-red-600">{errors.additionalContent}</p>
          )}

          {/* Save button */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onSave}
              disabled={uploading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading
                ? (editingIndex !== null ? 'Updating...' : 'Saving...')
                : (editingIndex !== null ? 'Update Content' : 'Save Content')
              }
            </button>
          </div>
        </div>
      )}
    </div>
  </div>
));

// Set display names
MainImageUploadSection.displayName = 'MainImageUploadSection';
AdditionalContentSection.displayName = 'AdditionalContentSection';
IslandSection.displayName = 'IslandSection';

export default IslandSection;
