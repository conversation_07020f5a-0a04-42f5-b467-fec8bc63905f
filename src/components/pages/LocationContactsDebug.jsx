'use client';

import React, { useState, useEffect } from 'react';

// Debug component to test data pre-population
const LocationContactsDebug = () => {
  const [testData, setTestData] = useState({
    title: '',
    body: '',
    details: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const fetchTestData = async () => {
    setIsLoading(true);
    console.log('🔄 Fetching test data...');
    
    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      });

      console.log('📡 Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Raw API response:', data);

      if (data.success && data.data) {
        const locationData = data.data.locationAndcontacts || {};
        console.log('📍 Location data from API:', locationData);
        
        const newTestData = {
          title: locationData.title || '',
          body: locationData.body || '',
          details: locationData.details || ''
        };
        
        console.log('✅ Setting test data:', newTestData);
        setTestData(newTestData);
      } else {
        console.error('❌ Invalid API response format:', data);
      }
    } catch (error) {
      console.error('❌ Fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-medium mb-4">Location Contacts Debug Panel</h3>
      
      <button
        onClick={fetchTestData}
        disabled={isLoading}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 mb-4"
      >
        {isLoading ? 'Loading...' : 'Test API Fetch'}
      </button>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Title Data:
          </label>
          <div className="p-3 bg-gray-50 border rounded">
            <pre className="text-sm">{JSON.stringify(testData.title, null, 2)}</pre>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Body Data:
          </label>
          <div className="p-3 bg-gray-50 border rounded">
            <pre className="text-sm">{JSON.stringify(testData.body, null, 2)}</pre>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Details Data:
          </label>
          <div className="p-3 bg-gray-50 border rounded">
            <pre className="text-sm">{JSON.stringify(testData.details, null, 2)}</pre>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Full Test Data Object:
          </label>
          <div className="p-3 bg-gray-50 border rounded">
            <pre className="text-sm">{JSON.stringify(testData, null, 2)}</pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationContactsDebug;
