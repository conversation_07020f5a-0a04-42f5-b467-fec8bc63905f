import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LocationContactsManager from '../LocationContactsManager';

// Mock the TextEditor component
jest.mock('@/components/common/TextEditor', () => {
  return function MockTextEditor({ value, onChange, placeholder, disabled }) {
    return (
      <div data-testid="text-editor">
        <textarea
          value={value || ''}
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          data-testid="text-editor-input"
        />
      </div>
    );
  };
});

// Mock fetch
global.fetch = jest.fn();

describe('LocationContactsManager - Automatic Data Retrieval', () => {
  const mockFormData = {
    title: 'Existing Title',
    body: 'Existing body content with <strong>formatting</strong>',
    details: 'Existing contact details'
  };

  const mockProps = {
    formData: mockFormData,
    errors: {},
    onQuillChange: jest.fn(),
    onSectionSave: jest.fn(),
    isLoading: false
  };

  beforeEach(() => {
    fetch.mockClear();
    mockProps.onQuillChange.mockClear();
    mockProps.onSectionSave.mockClear();
  });

  test('should automatically fetch and pre-populate data when entering edit mode', async () => {
    // Mock successful API response
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          locationAndcontacts: {
            title: 'Fresh Title from DB',
            body: 'Fresh body content from <em>database</em>',
            details: 'Fresh contact details from DB'
          }
        }
      })
    });

    render(<LocationContactsManager {...mockProps} />);

    // Click edit button
    const editButton = screen.getByText('Edit Content');
    fireEvent.click(editButton);

    // Should show loading state
    expect(screen.getByText('Pre-populating form fields...')).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Form fields have been pre-populated with your existing content. You can now edit directly.')).toBeInTheDocument();
    });

    // Verify fetch was called correctly
    expect(fetch).toHaveBeenCalledWith('/api/pages', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-cache'
    });

    // Verify text editors are populated with fresh data
    const textEditors = screen.getAllByTestId('text-editor-input');
    expect(textEditors[0]).toHaveValue('Fresh Title from DB');
    expect(textEditors[1]).toHaveValue('Fresh body content from <em>database</em>');
    expect(textEditors[2]).toHaveValue('Fresh contact details from DB');
  });

  test('should handle empty/null fields gracefully', async () => {
    // Mock API response with empty fields
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          locationAndcontacts: {
            title: null,
            body: '',
            details: undefined
          }
        }
      })
    });

    render(<LocationContactsManager {...mockProps} />);

    const editButton = screen.getByText('Edit Content');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Form fields have been pre-populated with your existing content. You can now edit directly.')).toBeInTheDocument();
    });

    // Verify empty fields are handled correctly
    const textEditors = screen.getAllByTestId('text-editor-input');
    expect(textEditors[0]).toHaveValue('');
    expect(textEditors[1]).toHaveValue('');
    expect(textEditors[2]).toHaveValue('');
  });

  test('should show error message and use fallback data when API fails', async () => {
    // Mock API failure
    fetch.mockRejectedValueOnce(new Error('Network error'));

    render(<LocationContactsManager {...mockProps} />);

    const editButton = screen.getByText('Edit Content');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText(/Failed to load latest data/)).toBeInTheDocument();
    });

    // Should use fallback data from props
    const textEditors = screen.getAllByTestId('text-editor-input');
    expect(textEditors[0]).toHaveValue('Existing Title');
    expect(textEditors[1]).toHaveValue('Existing body content with <strong>formatting</strong>');
    expect(textEditors[2]).toHaveValue('Existing contact details');
  });

  test('should disable text editors while loading data', async () => {
    // Mock slow API response
    fetch.mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({
            success: true,
            data: { locationAndcontacts: mockFormData }
          })
        }), 100)
      )
    );

    render(<LocationContactsManager {...mockProps} />);

    const editButton = screen.getByText('Edit Content');
    fireEvent.click(editButton);

    // Text editors should be disabled while loading
    const textEditors = screen.getAllByTestId('text-editor-input');
    textEditors.forEach(editor => {
      expect(editor).toBeDisabled();
    });

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Form fields have been pre-populated with your existing content. You can now edit directly.')).toBeInTheDocument();
    });

    // Text editors should be enabled after loading
    textEditors.forEach(editor => {
      expect(editor).not.toBeDisabled();
    });
  });
});
