'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import TextEditor from '@/components/TextEditor';
import ActionButtonGroup from '@/components/pages/ActionButtonGroup';

/**
 * LocationAndContactsInput Component
 *
 * Integrated with TextEditor component for managing location and contact information
 *
 * API Endpoints Used:
 * - GET /api/pages - Fetch all page data including locationAndContacts section
 * - POST /api/pages - Update specific page section (locationAndContacts)
 * - PATCH /api/pages - Update multiple sections at once
 *
 * Features:
 * - Rich text editing with TextEditor component for title, body, and details
 * - Sample content loading
 * - Direct API submission
 * - Enhanced error handling and user feedback
 * - Line height customization
 * - Text selection controls
 */

const LocationAndContactsInput = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  const [localFormData, setLocalFormData] = useState({
    title: '',
    body: '',
    details: ''
  });

  // Refs for each editor
  const titleEditorRef = useRef(null);
  const bodyEditorRef = useRef(null);
  const detailsEditorRef = useRef(null);

  // Selection and formatting states
  const [selectedText, setSelectedText] = useState('');
  const [showSelectionControls, setShowSelectionControls] = useState(false);
  const [activeEditor, setActiveEditor] = useState(null);
  const [lineHeightState, setLineHeightState] = useState({
    global: '1.2',
    current: '1.2',
    hasSelection: false
  });

  // Sample content for quick loading
  const sampleContent = {
    title: '<h2 style="color: white; font-family: Arial;">Contact Information</h2>',
    body: '<p style="color: white; font-family: Arial;">Get in touch with us for more information about Elephant Island Lodge. We are here to help you plan your perfect getaway.</p>',
    details: '<div style="color: white; font-family: Arial;"><h3>Location Details</h3><p>Address: Elephant Island Lodge<br>Phone: +****************<br>Email: <EMAIL></p><h3>Operating Hours</h3><p>Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday - Sunday: 10:00 AM - 4:00 PM</p></div>'
  };

  // Validation functions
  const validateLocationAndContacts = (data) => {
    const errors = [];
    if (!data.title || data.title.trim() === '') {
      errors.push('Title is required');
    }
    if (!data.body || data.body.trim() === '') {
      errors.push('Body content is required');
    }
    if (!data.details || data.details.trim() === '') {
      errors.push('Details are required');
    }
    return errors;
  };

  // Success/Error message handlers
  const showSuccessMessage = (message) => {
    console.log('✅ Success:', message);
    // You can integrate with a toast notification system here
  };

  const showErrorMessage = (message) => {
    console.error('❌ Error:', message);
    setDataError(message);
    // You can integrate with a toast notification system here
  };

  // Handle local form changes
  const handleLocalChange = useCallback((content, field) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: content
    }));
  }, []);

  // Load sample content
  const loadSampleContent = useCallback(() => {
    setLocalFormData(sampleContent);
    showSuccessMessage('Sample content loaded successfully!');
  }, []);

  // Submit location and contacts data directly to API
  const submitLocationAndContactsDirectly = useCallback(async (data) => {
    try {
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'locationAndcontacts',
          data: data
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to save location and contacts data');
      }

      console.log('✅ Location and contacts data saved successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Error saving location and contacts data:', error);
      throw error;
    }
  }, []);

  // Fetch latest data from API
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const locationAndContactsData = data.data?.locationAndcontacts || {};
        const freshFormData = {
          title: locationAndContactsData.title || '',
          body: locationAndContactsData.body || '',
          details: locationAndContactsData.details || ''
        };
        setLocalFormData(freshFormData);
        console.log('✅ Successfully fetched latest location and contacts data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest location and contacts data:', error);
      setDataError(`Failed to load latest data: ${error.message}. Using current values.`);
      // Fallback to current form data
      const currentFormData = {
        title: formData?.title || '',
        body: formData?.body || '',
        details: formData?.details || ''
      };
      setLocalFormData(currentFormData);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Update local form data when formData changes
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        title: formData.title || '',
        body: formData.body || '',
        details: formData.details || ''
      });
    }
  }, [formData]);

  // Handle edit mode toggle
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    await fetchLatestData();
  }, [fetchLatestData]);

  // Handle save changes with enhanced error handling and API integration
  const handleSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateLocationAndContacts(localFormData);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      // Clear any previous errors
      setDataError(null);

      // Update the parent form data first (synchronously)
      Object.keys(localFormData).forEach(field => {
        onQuillChange(localFormData[field], 'locationAndcontacts', field);
      });

      // Then save via parent component
      await onSectionSave();
      setIsEditing(false);

      // Show success feedback
      showSuccessMessage('Location and contacts information saved successfully!');
    } catch (error) {
      console.error('Error saving location and contacts:', error);
      const errorMessage = error.message || 'Failed to save location and contacts information';
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
    }
  }, [localFormData, onQuillChange, onSectionSave]);

  // Handle direct save to API (bypassing parent form)
  const handleDirectSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateLocationAndContacts(localFormData);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      // Clear any previous errors
      setDataError(null);

      // Save directly to API
      await submitLocationAndContactsDirectly(localFormData);
      setIsEditing(false);

      // Show success feedback
      showSuccessMessage('Location and contacts information saved directly to database!');
    } catch (error) {
      console.error('Error in direct save:', error);
      const errorMessage = error.message || 'Failed to save location and contacts information directly';
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
    }
  }, [localFormData, submitLocationAndContactsDirectly]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setLocalFormData({
      title: formData?.title || '',
      body: formData?.body || '',
      details: formData?.details || ''
    });
    setIsEditing(false);
    setDataError(null);
  }, [formData]);

  // Handle delete
  const handleDelete = useCallback(async () => {
    try {
      // Clear all fields
      const emptyData = {
        title: '',
        body: '',
        details: ''
      };
      
      Object.keys(emptyData).forEach(field => {
        onQuillChange(emptyData[field], 'locationAndcontacts', field);
      });
      
      await onSectionSave();
      setShowDeleteConfirm(false);
      setIsEditing(false);
    } catch (error) {
      console.error('Error deleting location and contacts:', error);
    }
  }, [onQuillChange, onSectionSave]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData.title || formData.body || formData.details;

  return (
    <>
      {/* CSS for preserving line heights */}
      <style jsx>{`
        .preserve-line-heights span[style*="line-height"] {
          display: inline-block;
        }
        .selection-highlight {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
          padding: 1px 2px;
        }
      `}</style>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Location & Contacts Information</h2>

          <ActionButtonGroup
            mode={isEditing ? 'edit' : 'view'}
            isLoading={isLoadingData}
            hasContent={hasContent}
            onEdit={handleEdit}
            onSave={handleSave}
            onCancel={handleCancel}
            onDelete={() => setShowDeleteConfirm(true)}
            onDirectSave={handleDirectSave}
            onLoadSample={loadSampleContent}
            showDirectSave={true}
            showLoadSample={true}
            showDelete={true}
            responsive={true}
          />
        </div>

        {/* Data loading indicator */}
        {isLoadingData && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-700 text-sm">Loading latest data...</p>
          </div>
        )}

        {/* Error display */}
        {dataError && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 text-sm">{dataError}</p>
          </div>
        )}

        {/* Delete confirmation modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Delete</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete all location and contacts information? This action cannot be undone.
              </p>
              <div className="flex space-x-3 justify-end">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Form content */}
        {(isEditing || hasContent) && (
          <div className="space-y-6">
            {/* Title Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title
              </label>
              {isEditing ? (
                <div className="space-y-2">
                  <div
                    className={`${errors['locationAndcontacts.title'] ? 'border-red-500' : ''}`}
                    ref={titleEditorRef}
                  >
                    <TextEditor
                      key={`title-edit-${isEditing}`}
                      value={currentData.title}
                      onChange={(content) => handleLocalChange(content, 'title')}
                      placeholder="Enter location and contacts title"
                      style={{ minHeight: '80px', lineHeight: lineHeightState.global }}
                      className={`border rounded-md ${errors['locationAndcontacts.title'] ? 'border-red-500' : 'border-gray-300'}`}
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.title || 'No title set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>
              )}
              {errors['locationAndcontacts.title'] && (
                <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.title']}</p>
              )}
            </div>

            {/* Body Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Body Content
              </label>
              {isEditing ? (
                <div className="space-y-2">
                  <div
                    className={`${errors['locationAndcontacts.body'] ? 'border-red-500' : ''}`}
                    ref={bodyEditorRef}
                  >
                    <TextEditor
                      key={`body-edit-${isEditing}`}
                      value={currentData.body}
                      onChange={(content) => handleLocalChange(content, 'body')}
                      placeholder="Enter body content"
                      style={{ minHeight: '120px', lineHeight: lineHeightState.global }}
                      className={`border rounded-md ${errors['locationAndcontacts.body'] ? 'border-red-500' : 'border-gray-300'}`}
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.body || 'No body content set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>
              )}
              {errors['locationAndcontacts.body'] && (
                <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.body']}</p>
              )}
            </div>

            {/* Details Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Details
              </label>
              {isEditing ? (
                <div className="space-y-2">
                  <div
                    className={`${errors['locationAndcontacts.details'] ? 'border-red-500' : ''}`}
                    ref={detailsEditorRef}
                  >
                    <TextEditor
                      key={`details-edit-${isEditing}`}
                      value={currentData.details}
                      onChange={(content) => handleLocalChange(content, 'details')}
                      placeholder="Enter contact details"
                      style={{ minHeight: '150px', lineHeight: lineHeightState.global }}
                      className={`border rounded-md ${errors['locationAndcontacts.details'] ? 'border-red-500' : 'border-gray-300'}`}
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                    <div
                      dangerouslySetInnerHTML={{ __html: currentData.details || 'No details set' }}
                      className="preserve-line-heights"
                    />
                  </div>
                </div>
              )}
              {errors['locationAndcontacts.details'] && (
                <p className="mt-1 text-sm text-red-600">{errors['locationAndcontacts.details']}</p>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default LocationAndContactsInput;
