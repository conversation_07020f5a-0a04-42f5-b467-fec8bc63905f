'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import LocationAndContactsInput from '@/components/pages/LocationAndContactsInput';
import BookingDetailsText from '@/components/pages/BookingDetailsText';
import TestimonialsInput from '@/components/pages/TestimonialsInput';
import IslandSection from '@/components/pages/IslandSection';
import ExperiencesSection from '@/components/pages/ExperiencesSection';

const PagesForm = React.memo(({ pages, onSave, onCancel, isLoading }) => {
  const [activeSection, setActiveSection] = useState('island');
  const [formData, setFormData] = useState({
    island: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    experiences: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    testimonials: {
      testimonials: []
    },
    locationAndcontacts: {
      title: '',
      body: '',
      details: ''
    },
    booking: {
      details: ''
    }
  });

  const [errors, setErrors] = useState({});

  // Initialize form data when pages prop changes
  useEffect(() => {
    if (pages) {
      setFormData({
        testimonials: pages.testimonials || {
          testimonials: []
        },
        locationAndcontacts: pages.locationAndcontacts || {
          title: '',
          body: '',
          details: ''
        },
        booking: pages.booking || {
          details: ''
        }
      });
    }
  }, [pages]);

  // Validation rules
  const validateForm = useCallback(() => {
    const newErrors = {};

    // Validate island section
    if (!formData.island.title?.trim()) {
      newErrors['island.title'] = 'Island title is required';
    }
    if (!formData.island.body1?.trim()) {
      newErrors['island.body1'] = 'Island body1 is required';
    }

    // Validate experiences section
    if (!formData.experiences.title?.trim()) {
      newErrors['experiences.title'] = 'Experiences title is required';
    }
    if (!formData.experiences.body1?.trim()) {
      newErrors['experiences.body1'] = 'Experiences body1 is required';
    }

    // Validate location and contacts section
    if (!formData.locationAndcontacts.title?.trim()) {
      newErrors['locationAndcontacts.title'] = 'Location title is required';
    }
    if (!formData.locationAndcontacts.body?.trim()) {
      newErrors['locationAndcontacts.body'] = 'Location body is required';
    }
    if (!formData.locationAndcontacts.details?.trim()) {
      newErrors['locationAndcontacts.details'] = 'Location details are required';
    }

    // Validate booking section
    if (!formData.booking.details?.trim()) {
      newErrors['booking.details'] = 'Booking details are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle Quill editor content changes
  const handleQuillChange = useCallback((content, section, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [fieldName]: content
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${fieldName}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${fieldName}`]: ''
      }));
    }
  }, [errors]);











  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  // Handle section save
  const handleSectionSave = useCallback(async (section, sectionData = null) => {
    if (!validateForm()) {
      return;
    }

    try {
      if (section) {
        // Use provided section data or fall back to formData
        const dataToSave = sectionData || formData[section];
        // Save specific section
        await onSave({ section, data: dataToSave });
      } else {
        // Save entire form
        await onSave(formData);
      }
    } catch (error) {
      console.error('Save error:', error);
      throw error;
    }
  }, [formData, validateForm, onSave]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: 'island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'locationAndcontacts', label: 'Location & Contacts' },
    { value: 'booking', label: 'Booking' }
  ], []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-trasandina-black text-gray-900 uppercase tracking-wide">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="pages-form"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {/* Section Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {sectionOptions.map((section) => (
            <button
              key={section.value}
              type="button"
              onClick={() => setActiveSection(section.value)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSection === section.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      <form id="pages-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Island Section */}
        {activeSection === 'island' && (
          <IslandSection
            pages={pages}
            onSave={onSave}
            isLoading={isLoading}
          />
        )}

        {/* Experiences Section */}
        {activeSection === 'experiences' && (
          <ExperiencesSection
            pages={pages}
            onSave={onSave}
            isLoading={isLoading}
          />
        )}

        {/* Testimonials Section */}
        {activeSection === 'testimonials' && (
          <TestimonialsInput
            formData={formData.testimonials}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('testimonials')}
            isLoading={isLoading}
          />
        )}

        {/* Location and Contacts Section */}
        {activeSection === 'locationAndcontacts' && (
          <LocationAndContactsInput
            formData={formData.locationAndcontacts}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('locationAndcontacts')}
            isLoading={isLoading}
          />
        )}

        {/* Booking Section */}
        {activeSection === 'booking' && (
          <BookingDetailsText
            formData={formData.booking}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={(section, sectionData) => handleSectionSave(section, sectionData)}
            isLoading={isLoading}
          />
        )}
      </form>
    </div>
  );
});

// Set display names
PagesForm.displayName = 'PagesForm';

export default PagesForm;
