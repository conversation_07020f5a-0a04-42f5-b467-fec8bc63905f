'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import TextEditor from '@/components/TextEditor';
import ActionButtonGroup, { ActionButton } from '@/components/pages/ActionButtonGroup';

/**
 * TestimonialsInput Component
 *
 * Integrated with TextEditor component for managing testimonials array
 *
 * API Endpoints Used:
 * - GET /api/pages - Fetch all page data including testimonials section
 * - POST /api/pages - Update specific page section (testimonials)
 * - PATCH /api/pages - Update multiple sections at once
 *
 * Features:
 * - Full CRUD operations for testimonials array
 * - Rich text editing with TextEditor component for comments
 * - Individual testimonial management (Create, Read, Update, Delete)
 * - Sample content loading
 * - Direct API submission
 * - Enhanced error handling and user feedback
 * - Unique name validation
 * - Inline editing capabilities
 */

const TestimonialsInput = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null); // 'all' or testimonial index
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  
  // Local form data for testimonials array
  const [localFormData, setLocalFormData] = useState({
    testimonials: []
  });

  // Individual testimonial form state
  const [testimonialFormData, setTestimonialFormData] = useState({
    name: '',
    comment: ''
  });

  // Testimonial editing state
  const [editingTestimonialIndex, setEditingTestimonialIndex] = useState(null);
  const [isEditingTestimonial, setIsEditingTestimonial] = useState(false);
  const [showTestimonialForm, setShowTestimonialForm] = useState(false);

  // Refs for editors
  const nameEditorRef = useRef(null);
  const commentEditorRef = useRef(null);

  // Sample testimonials content
  const sampleTestimonials = [
    {
      name: 'Sarah Johnson',
      comment: '<p style="color: white; font-family: Arial;">Our stay at Elephant Island Lodge was absolutely magical! The pristine beaches and crystal-clear waters made for an unforgettable experience. The staff went above and beyond to ensure our comfort.</p>'
    },
    {
      name: 'Michael Chen',
      comment: '<p style="color: white; font-family: Arial;">I\'ve traveled to many tropical destinations, but Elephant Island Lodge stands out as truly exceptional. The attention to detail and personalized service made our honeymoon perfect.</p>'
    },
    {
      name: 'Emma Rodriguez',
      comment: '<p style="color: white; font-family: Arial;">The lodge offers the perfect blend of luxury and natural beauty. Waking up to stunning ocean views every morning was a dream come true. Highly recommended!</p>'
    }
  ];

  // Validation functions
  const validateTestimonials = (data) => {
    const errors = [];
    if (!data.testimonials || data.testimonials.length === 0) {
      errors.push('At least one testimonial is required');
    }
    
    // Check for unique names
    const names = data.testimonials.map(t => t.name.toLowerCase().trim());
    const uniqueNames = new Set(names);
    if (names.length !== uniqueNames.size) {
      errors.push('Testimonial names must be unique');
    }

    // Validate individual testimonials
    data.testimonials.forEach((testimonial, index) => {
      if (!testimonial.name || testimonial.name.trim() === '') {
        errors.push(`Testimonial ${index + 1}: Name is required`);
      }
      if (!testimonial.comment || testimonial.comment.trim() === '') {
        errors.push(`Testimonial ${index + 1}: Comment is required`);
      }
      if (testimonial.name && testimonial.name.length > 100) {
        errors.push(`Testimonial ${index + 1}: Name cannot exceed 100 characters`);
      }
      if (testimonial.comment && testimonial.comment.length > 1000) {
        errors.push(`Testimonial ${index + 1}: Comment cannot exceed 1000 characters`);
      }
    });

    return errors;
  };

  const validateSingleTestimonial = (testimonial, existingTestimonials = [], excludeIndex = null) => {
    const errors = [];
    
    if (!testimonial.name || testimonial.name.trim() === '') {
      errors.push('Name is required');
    }
    if (!testimonial.comment || testimonial.comment.trim() === '') {
      errors.push('Comment is required');
    }
    if (testimonial.name && testimonial.name.length > 100) {
      errors.push('Name cannot exceed 100 characters');
    }
    if (testimonial.comment && testimonial.comment.length > 1000) {
      errors.push('Comment cannot exceed 1000 characters');
    }

    // Check for unique name
    const existingNames = existingTestimonials
      .map((t, index) => index !== excludeIndex ? t.name.toLowerCase().trim() : null)
      .filter(name => name !== null);
    
    if (testimonial.name && existingNames.includes(testimonial.name.toLowerCase().trim())) {
      errors.push('Testimonial name must be unique');
    }

    return errors;
  };

  // Success/Error message handlers
  const showSuccessMessage = (message) => {
    console.log('✅ Success:', message);
    // You can integrate with a toast notification system here
  };

  const showErrorMessage = (message) => {
    console.error('❌ Error:', message);
    setDataError(message);
    // You can integrate with a toast notification system here
  };

  // Handle local form changes
  const handleLocalChange = useCallback((testimonials) => {
    setLocalFormData({ testimonials });
  }, []);

  // Handle testimonial form field changes
  const handleTestimonialFieldChange = useCallback((field, value) => {
    setTestimonialFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Load sample content
  const loadSampleContent = useCallback(() => {
    setLocalFormData({ testimonials: sampleTestimonials });
    showSuccessMessage('Sample testimonials loaded successfully!');
  }, []);

  // Submit testimonials data directly to API
  const submitTestimonialsDirectly = useCallback(async (data) => {
    try {
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'testimonials',
          data: data
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to save testimonials data');
      }

      console.log('✅ Testimonials data saved successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ Error saving testimonials data:', error);
      throw error;
    }
  }, []);

  // Fetch latest data from API
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const testimonialsData = data.data?.testimonials || { testimonials: [] };
        setLocalFormData(testimonialsData);
        console.log('✅ Successfully fetched latest testimonials data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest testimonials data:', error);
      setDataError(`Failed to load latest data: ${error.message}. Using current values.`);
      // Fallback to current form data
      const currentFormData = {
        testimonials: formData?.testimonials || []
      };
      setLocalFormData(currentFormData);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Update local form data when formData changes
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        testimonials: formData.testimonials || []
      });
    }
  }, [formData]);

  // Handle edit mode toggle
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    await fetchLatestData();
  }, [fetchLatestData]);

  // Handle save changes with enhanced error handling and API integration
  const handleSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateTestimonials(localFormData);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      // Clear any previous errors
      setDataError(null);

      // Update the parent form data first (synchronously)
      onQuillChange(localFormData.testimonials, 'testimonials', 'testimonials');

      // Then save via parent component
      await onSectionSave();
      setIsEditing(false);

      // Show success feedback
      showSuccessMessage('Testimonials saved successfully!');
    } catch (error) {
      console.error('Error saving testimonials:', error);
      const errorMessage = error.message || 'Failed to save testimonials';
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
    }
  }, [localFormData, onQuillChange, onSectionSave]);

  // Handle direct save to API (bypassing parent form)
  const handleDirectSave = useCallback(async () => {
    try {
      // Enhanced validation with fallback
      const validationErrors = validateTestimonials(localFormData);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      // Clear any previous errors
      setDataError(null);

      // Save directly to API
      await submitTestimonialsDirectly(localFormData);
      setIsEditing(false);

      // Show success feedback
      showSuccessMessage('Testimonials saved directly to database!');
    } catch (error) {
      console.error('Error in direct save:', error);
      const errorMessage = error.message || 'Failed to save testimonials directly';
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
    }
  }, [localFormData, submitTestimonialsDirectly]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setLocalFormData({
      testimonials: formData?.testimonials || []
    });
    setIsEditing(false);
    setDataError(null);
    setShowTestimonialForm(false);
    setIsEditingTestimonial(false);
    setEditingTestimonialIndex(null);
    setTestimonialFormData({ name: '', comment: '' });
  }, [formData]);

  // Handle delete all testimonials
  const handleDeleteAll = useCallback(async () => {
    try {
      // Clear all testimonials
      const emptyData = { testimonials: [] };
      
      onQuillChange(emptyData.testimonials, 'testimonials', 'testimonials');
      
      await onSectionSave();
      setShowDeleteConfirm(false);
      setDeleteTarget(null);
      setIsEditing(false);
    } catch (error) {
      console.error('Error deleting all testimonials:', error);
    }
  }, [onQuillChange, onSectionSave]);

  // CRUD Operations for individual testimonials

  // Add new testimonial
  const handleAddTestimonial = useCallback(() => {
    const validationErrors = validateSingleTestimonial(testimonialFormData, localFormData.testimonials);
    if (validationErrors.length > 0) {
      const errorMessage = validationErrors.join(', ');
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
      return;
    }

    // Add testimonial to local data
    const newTestimonials = [...localFormData.testimonials, { ...testimonialFormData }];
    setLocalFormData({ testimonials: newTestimonials });

    // Reset form
    setTestimonialFormData({ name: '', comment: '' });
    setShowTestimonialForm(false);
    setDataError(null);

    showSuccessMessage('Testimonial added successfully!');
  }, [testimonialFormData, localFormData.testimonials]);

  // Remove testimonial
  const handleRemoveTestimonial = useCallback((index) => {
    const newTestimonials = localFormData.testimonials.filter((_, i) => i !== index);
    setLocalFormData({ testimonials: newTestimonials });

    // If we're editing this testimonial, cancel the edit
    if (editingTestimonialIndex === index) {
      setIsEditingTestimonial(false);
      setEditingTestimonialIndex(null);
      setTestimonialFormData({ name: '', comment: '' });
      setShowTestimonialForm(false);
    }

    showSuccessMessage('Testimonial removed successfully!');
  }, [localFormData.testimonials, editingTestimonialIndex]);

  // Edit testimonial
  const handleEditTestimonial = useCallback((index) => {
    const testimonial = localFormData.testimonials[index];

    // Populate form with existing testimonial data
    setTestimonialFormData({
      name: testimonial.name,
      comment: testimonial.comment
    });

    // Set edit mode
    setEditingTestimonialIndex(index);
    setIsEditingTestimonial(true);
    setShowTestimonialForm(true);
    setDataError(null);
  }, [localFormData.testimonials]);

  // Update testimonial
  const handleUpdateTestimonial = useCallback(() => {
    const validationErrors = validateSingleTestimonial(
      testimonialFormData,
      localFormData.testimonials,
      editingTestimonialIndex
    );

    if (validationErrors.length > 0) {
      const errorMessage = validationErrors.join(', ');
      setDataError(errorMessage);
      showErrorMessage(errorMessage);
      return;
    }

    // Update the testimonial
    const newTestimonials = localFormData.testimonials.map((testimonial, index) =>
      index === editingTestimonialIndex
        ? { ...testimonialFormData }
        : testimonial
    );

    setLocalFormData({ testimonials: newTestimonials });

    // Reset edit state
    setTestimonialFormData({ name: '', comment: '' });
    setEditingTestimonialIndex(null);
    setIsEditingTestimonial(false);
    setShowTestimonialForm(false);
    setDataError(null);

    showSuccessMessage('Testimonial updated successfully!');
  }, [testimonialFormData, localFormData.testimonials, editingTestimonialIndex]);

  // Cancel testimonial edit
  const handleCancelTestimonialEdit = useCallback(() => {
    setTestimonialFormData({ name: '', comment: '' });
    setEditingTestimonialIndex(null);
    setIsEditingTestimonial(false);
    setShowTestimonialForm(false);
    setDataError(null);
  }, []);

  // Handle delete confirmation
  const handleDeleteConfirm = useCallback((target) => {
    setDeleteTarget(target);
    setShowDeleteConfirm(true);
  }, []);

  const handleDeleteCancel = useCallback(() => {
    setShowDeleteConfirm(false);
    setDeleteTarget(null);
  }, []);

  const handleDeleteExecute = useCallback(async () => {
    if (deleteTarget === 'all') {
      await handleDeleteAll();
    } else if (typeof deleteTarget === 'number') {
      handleRemoveTestimonial(deleteTarget);
      setShowDeleteConfirm(false);
      setDeleteTarget(null);
    }
  }, [deleteTarget, handleDeleteAll, handleRemoveTestimonial]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData?.testimonials && formData.testimonials.length > 0;

  return (
    <>
      {/* CSS for preserving line heights */}
      <style jsx>{`
        .preserve-line-heights span[style*="line-height"] {
          display: inline-block;
        }
        .selection-highlight {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
          padding: 1px 2px;
        }
      `}</style>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Testimonials Management</h2>

          <ActionButtonGroup
            mode={isEditing ? 'edit' : 'view'}
            isLoading={isLoadingData}
            hasContent={hasContent}
            onEdit={handleEdit}
            onSave={handleSave}
            onCancel={handleCancel}
            onDelete={() => handleDeleteConfirm('all')}
            onDirectSave={handleDirectSave}
            onLoadSample={loadSampleContent}
            showDirectSave={true}
            showLoadSample={true}
            showDelete={true}
            responsive={true}
          />
        </div>

        {/* Data loading indicator */}
        {isLoadingData && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-700 text-sm">Loading latest data...</p>
          </div>
        )}

        {/* Error display */}
        {dataError && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 text-sm">{dataError}</p>
          </div>
        )}

        {/* Delete confirmation modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Delete</h3>
              <p className="text-gray-600 mb-6">
                {deleteTarget === 'all'
                  ? 'Are you sure you want to delete all testimonials? This action cannot be undone.'
                  : 'Are you sure you want to delete this testimonial? This action cannot be undone.'
                }
              </p>
              <div className="flex space-x-3 justify-end">
                <ActionButton
                  variant="secondary"
                  onClick={handleDeleteCancel}
                >
                  Cancel
                </ActionButton>
                <ActionButton
                  variant="destructive"
                  onClick={handleDeleteExecute}
                >
                  Delete
                </ActionButton>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        {!hasContent && !isEditing ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <p className="text-gray-500 mb-4">No testimonials available</p>
            <ActionButton
              variant="outlinedPrimary"
              onClick={handleEdit}
              disabled={isLoadingData}
              loading={isLoadingData}
              loadingText="Loading..."
            >
              {isLoadingData ? 'Loading...' : 'Create Testimonials'}
            </ActionButton>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Add/Edit Testimonial Form */}
            {isEditing && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-md font-medium text-gray-800">
                    {isEditingTestimonial ? 'Edit Testimonial' : 'Add New Testimonial'}
                  </h4>
                  {!showTestimonialForm && (
                    <ActionButton
                      variant="success"
                      size="small"
                      onClick={() => setShowTestimonialForm(true)}
                      disabled={isEditingTestimonial}
                    >
                      Add Testimonial
                    </ActionButton>
                  )}
                </div>

                {/* Testimonial Form */}
                {(showTestimonialForm || isEditingTestimonial) && (
                  <div className="space-y-4">
                    {/* Name Field */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Name *
                      </label>
                      <div ref={nameEditorRef}>
                        <TextEditor
                          key={`name-${isEditingTestimonial ? editingTestimonialIndex : 'new'}`}
                          value={testimonialFormData.name}
                          onChange={(content) => handleTestimonialFieldChange('name', content)}
                          placeholder="Enter testimonial author name"
                          style={{ minHeight: '60px' }}
                          className="border rounded-md border-gray-300"
                        />
                      </div>
                    </div>

                    {/* Comment Field */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Comment *
                      </label>
                      <div ref={commentEditorRef}>
                        <TextEditor
                          key={`comment-${isEditingTestimonial ? editingTestimonialIndex : 'new'}`}
                          value={testimonialFormData.comment}
                          onChange={(content) => handleTestimonialFieldChange('comment', content)}
                          placeholder="Enter testimonial comment"
                          style={{ minHeight: '120px' }}
                          className="border rounded-md border-gray-300"
                        />
                      </div>
                    </div>

                    {/* Form Buttons */}
                    <div className="flex gap-2">
                      {isEditingTestimonial ? (
                        <>
                          <ActionButton
                            variant="primary"
                            onClick={handleUpdateTestimonial}
                            disabled={!testimonialFormData.name || !testimonialFormData.comment}
                          >
                            Update Testimonial
                          </ActionButton>
                          <ActionButton
                            variant="secondary"
                            onClick={handleCancelTestimonialEdit}
                          >
                            Cancel
                          </ActionButton>
                        </>
                      ) : (
                        <>
                          <ActionButton
                            variant="success"
                            onClick={handleAddTestimonial}
                            disabled={!testimonialFormData.name || !testimonialFormData.comment}
                          >
                            Add Testimonial
                          </ActionButton>
                          <ActionButton
                            variant="secondary"
                            onClick={() => {
                              setShowTestimonialForm(false);
                              setTestimonialFormData({ name: '', comment: '' });
                              setDataError(null);
                            }}
                          >
                            Cancel
                          </ActionButton>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Existing Testimonials List */}
            {currentData.testimonials && currentData.testimonials.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-md font-medium text-gray-800">
                    {isEditing ? 'Manage Testimonials' : 'Current Testimonials'} ({currentData.testimonials.length})
                  </h4>
                  {currentData.testimonials.length > 0 && (
                    <span className="text-sm text-gray-500">
                      Maximum: 20 testimonials
                    </span>
                  )}
                </div>

                <div className="space-y-3">
                  {currentData.testimonials.map((testimonial, index) => (
                    <div
                      key={index}
                      className={`bg-white border rounded-lg p-4 ${
                        isEditingTestimonial && editingTestimonialIndex === index
                          ? 'border-blue-300 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h5
                            className="font-medium text-gray-900 mb-2"
                            dangerouslySetInnerHTML={{ __html: testimonial.name }}
                          />
                          <div
                            className="text-gray-600 text-sm"
                            dangerouslySetInnerHTML={{ __html: testimonial.comment }}
                          />
                        </div>

                        {isEditing && (
                          <div className="ml-4 flex gap-2">
                            <ActionButton
                              variant="outlinedPrimary"
                              size="small"
                              onClick={() => handleEditTestimonial(index)}
                              disabled={isEditingTestimonial && editingTestimonialIndex !== index}
                            >
                              Edit
                            </ActionButton>
                            <ActionButton
                              variant="outlinedDestructive"
                              size="small"
                              onClick={() => handleDeleteConfirm(index)}
                              disabled={isEditingTestimonial}
                            >
                              Remove
                            </ActionButton>
                          </div>
                        )}
                      </div>

                      {isEditingTestimonial && editingTestimonialIndex === index && (
                        <div className="mt-2 p-2 bg-blue-100 rounded text-sm text-blue-800">
                          Currently editing this testimonial
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Empty state for editing mode */}
            {isEditing && currentData.testimonials.length === 0 && (
              <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                <p className="text-gray-500 mb-4">No testimonials yet</p>
                <ActionButton
                  variant="success"
                  onClick={() => setShowTestimonialForm(true)}
                  disabled={showTestimonialForm}
                >
                  Add First Testimonial
                </ActionButton>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default TestimonialsInput;