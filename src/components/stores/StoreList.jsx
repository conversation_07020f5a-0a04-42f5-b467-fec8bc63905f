'use client';

import { useState, useEffect } from 'react';
import { 
  MdEdit, 
  MdDelete, 
  MdSearch, 
  MdImage,
  MdSort,
  MdChevronLeft,
  MdChevronRight,
  MdFilterList
} from 'react-icons/md';

export default function StoreList({ 
  onEdit, 
  onDelete, 
  onBulkDelete, 
  refreshTrigger = 0 
}) {
  const [storeItems, setStoreItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [availabilityFilter, setAvailabilityFilter] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);

  const availabilityOptions = ['Available', 'Sold', 'Reserved'];

  useEffect(() => {
    fetchStoreItems();
  }, [refreshTrigger, searchTerm, availabilityFilter, sortField, sortDirection, currentPage]);

  const fetchStoreItems = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: searchTerm,
        sort: `${sortDirection === 'desc' ? '-' : ''}${sortField}`,
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
      });

      if (availabilityFilter) {
        params.append('availability', availabilityFilter);
      }

      const response = await fetch(`/api/stores?${params}`);
      const data = await response.json();

      if (data.success) {
        setStoreItems(data.data);
        setTotalPages(data.pagination.pages);
      } else {
        setError(data.message || 'Failed to fetch store items');
      }
    } catch (err) {
      setError('Failed to fetch store items');
      console.error('Fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  };

  const handleSelectAll = () => {
    if (selectedItems.length === storeItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(storeItems.map(item => item._id));
    }
  };

  const handleSelectItem = (id) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;
    
    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} store items?`)) {
      await onBulkDelete(selectedItems);
      setSelectedItems([]);
    }
  };

  const handleBulkAvailabilityUpdate = async (availability) => {
    if (selectedItems.length === 0) return;
    
    try {
      const items = selectedItems.map(id => ({ _id: id, availability }));
      
      const response = await fetch('/api/stores', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items, action: `mark_${availability.toLowerCase()}` }),
      });

      if (response.ok) {
        fetchStoreItems();
        setSelectedItems([]);
      }
    } catch (error) {
      console.error('Bulk update error:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAvailabilityBadge = (availability) => {
    const colors = {
      Available: 'bg-green-100 text-green-800',
      Sold: 'bg-red-100 text-red-800',
      Reserved: 'bg-yellow-100 text-yellow-800',
    };
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[availability] || 'bg-gray-100 text-gray-800'}`}>
        {availability}
      </span>
    );
  };

  const getMainImage = (images) => {
    if (Array.isArray(images) && images.length > 0) {
      return images[0];
    }
    return images || null;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Store Items</h2>
          
          <div className="flex items-center space-x-2">
            {selectedItems.length > 0 && (
              <>
                <div className="flex items-center space-x-2">
                  <select
                    onChange={(e) => handleBulkAvailabilityUpdate(e.target.value)}
                    className="px-3 py-1 text-sm border border-gray-300 rounded-md"
                    defaultValue=""
                  >
                    <option value="" disabled>Update Status</option>
                    {availabilityOptions.map(option => (
                      <option key={option} value={option}>
                        Mark as {option}
                      </option>
                    ))}
                  </select>
                  
                  <button
                    onClick={handleBulkDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                  >
                    <MdDelete className="mr-2" />
                    Delete ({selectedItems.length})
                  </button>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <MdSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by title or author..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="relative">
            <MdFilterList className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={availabilityFilter}
              onChange={(e) => {
                setAvailabilityFilter(e.target.value);
                setCurrentPage(1);
              }}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              {availabilityOptions.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-500">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedItems.length === storeItems.length && storeItems.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center">
                  Title
                  <MdSort className="ml-1" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('author')}
              >
                <div className="flex items-center">
                  Author
                  <MdSort className="ml-1" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('price')}
              >
                <div className="flex items-center">
                  Price
                  <MdSort className="ml-1" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('availability')}
              >
                <div className="flex items-center">
                  Status
                  <MdSort className="ml-1" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('createdAt')}
              >
                <div className="flex items-center">
                  Created
                  <MdSort className="ml-1" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {storeItems.map((item) => (
              <tr key={item._id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item._id)}
                    onChange={() => handleSelectItem(item._id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4">
                  {getMainImage(item.image) ? (
                    <img
                      src={getMainImage(item.image)}
                      alt={item.title}
                      className="w-16 h-16 object-cover rounded-md"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                      <MdImage className="text-gray-400" />
                    </div>
                  )}
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-gray-900">
                    {item.title}
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  {item.author}
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {item.price}
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  {item.size || 'N/A'}
                </td>
                <td className="px-6 py-4">
                  {getAvailabilityBadge(item.availability)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  {formatDate(item.createdAt)}
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEdit(item)}
                      className="p-2 text-blue-600 hover:bg-blue-100 rounded-md"
                      title="Edit"
                    >
                      <MdEdit />
                    </button>
                    <button
                      onClick={() => onDelete(item._id)}
                      className="p-2 text-red-600 hover:bg-red-100 rounded-md"
                      title="Delete"
                    >
                      <MdDelete />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <MdChevronLeft />
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <MdChevronRight />
            </button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && storeItems.length === 0 && (
        <div className="p-12 text-center">
          <MdImage className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No store items found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || availabilityFilter ? 'Try adjusting your search or filters.' : 'Get started by creating a new store item.'}
          </p>
        </div>
      )}
    </div>
  );
}
