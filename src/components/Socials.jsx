import { settings } from '@/lib/settings'
import React, { useState } from 'react'
import ImageWrapperResponsive from './ImageWrapperResponsive'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useContextAudio } from '@/contexts/useContextAudio'


function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    const {disptachExperience}=useContextAudio()
    
    const handleClick = () => {
      // console.log('BtnLandingpageComponent:',data)
      {data?.name==='youtube' ? window.open('https://www.youtube.com/@ElephantIslandBotswana') : window.open(data?.url)}
    }
    // console.log('BtnLandingpageComponent:',data)
    return(
        <div 
            onClick={handleClick}
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.off}/>
            </div>
        </div>
    )
}

function BtnSoundComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    const {pauseAudio, setPauseAuido}=useContextAudio()
    
    const handleClick = () => {
      setPauseAuido(!pauseAudio)
      // console.log('BtnSoundComponent:',pauseAudio)
    }
    // console.log('BtnLandingpageComponent:',data)
    return(
        <div 
            onClick={handleClick}
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${pauseAudio ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${pauseAudio ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.off}/>
            </div>
        </div>
    )
}

export default function Socials() {
  const {isPlaying}=useContextAudio()
  // console.log('Socials:',isPlaying)
  return (
    <div className='absolute right-5 bottom-20 z-20 flex-col gap-3 flex w-fit h-fit items-center justify-center'>
      {settings.socials.filter(item => item?.name !== 'sound').map((item,index)=>{
        return(
          <div key={index} className='flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'>
            <div className='flex relative items-center justify-center'>
              <BtnLandingpageComponent
                index={index}
                data={item}
              />
            </div>
          </div>
        )
      })}   
      {isPlaying && settings.socials.filter(item => item?.name == 'sound').map((item,index)=>{
        return(
          <div key={index} className='flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'>
            <div className='flex relative items-center justify-center'>
              <BtnSoundComponent
                index={index}
                data={item}
              />
            </div>
          </div>
        )
      })}   
    </div>
  )
}
