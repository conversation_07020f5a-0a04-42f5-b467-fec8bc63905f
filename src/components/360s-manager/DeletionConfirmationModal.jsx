'use client';

import { useState } from 'react';
import { Md<PERSON>arning, MdClose, MdImage, MdLocationOn, MdCameraAlt, MdSchedule, MdDeleteForever, MdImageNotSupported } from 'react-icons/md';

export default function DeletionConfirmationModal({ 
  isOpen, 
  onClose, 
  imageData, 
  onDeleteImageOnly, 
  onDeleteEverything 
}) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);

  if (!isOpen || !imageData) return null;

  const handleDeleteImageOnly = async () => {
    setIsProcessing(true);
    try {
      await onDeleteImageOnly(imageData);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteEverything = async () => {
    setIsProcessing(true);
    try {
      await onDeleteEverything(imageData);
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Unknown';
    }
  };

  const hasMarkers = imageData?.markerList && imageData.markerList.length > 0;
  const hasCameraSettings = imageData?.cameraPosition !== -0.0001 || imageData?._360Rotation !== -0.0001;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <MdWarning className="h-6 w-6 text-red-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900">
              Delete 360° Image
            </h3>
          </div>
          <button
            onClick={onClose}
            disabled={isProcessing}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <MdClose className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Image Info */}
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex items-start space-x-3">
              <MdImage className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-red-800">
                  Image: <span className="font-mono">{imageData?.name || 'Unknown'}</span>
                </p>
                <p className="text-sm text-red-700 mt-1">
                  Original file: {imageData?.originalFileName || 'Unknown'}
                </p>
              </div>
            </div>
          </div>

          {/* Image Details */}
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Image Details:</h4>
            <div className="space-y-2 text-sm text-gray-600">
              {hasMarkers && (
                <div className="flex items-center space-x-2">
                  <MdLocationOn className="h-4 w-4 text-blue-500" />
                  <span className="text-blue-600 font-medium">
                    Contains {imageData.markerList.length} marker{imageData.markerList.length !== 1 ? 's' : ''}
                  </span>
                </div>
              )}
              
              {hasCameraSettings && (
                <div className="flex items-center space-x-2">
                  <MdCameraAlt className="h-4 w-4 text-green-500" />
                  <span className="text-green-600 font-medium">Has custom camera settings</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <MdSchedule className="h-4 w-4 text-gray-400" />
                <span>Created: {formatDate(imageData?.createdAt)}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                  Priority: {imageData?.priority || 0}
                </span>
              </div>
            </div>
          </div>

          {/* Action Question */}
          <div className="pt-2">
            <p className="text-sm text-gray-700 font-medium mb-4">
              What would you like to delete?
            </p>

            {/* Option 1: Delete Image Only */}
            <div className="space-y-3">
              <div 
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedOption === 'imageOnly' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedOption('imageOnly')}
              >
                <div className="flex items-start space-x-3">
                  <MdImageNotSupported className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h5 className="text-sm font-medium text-gray-900">
                      Delete image only (keep markers and settings)
                    </h5>
                    <p className="text-xs text-gray-600 mt-1">
                      Removes the image file but preserves all marker positions and camera settings. 
                      You'll be prompted to upload a replacement image.
                    </p>
                  </div>
                </div>
              </div>

              {/* Option 2: Delete Everything */}
              <div 
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedOption === 'everything' 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedOption('everything')}
              >
                <div className="flex items-start space-x-3">
                  <MdDeleteForever className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h5 className="text-sm font-medium text-gray-900">
                      Delete everything (image, markers, and all settings)
                    </h5>
                    <p className="text-xs text-gray-600 mt-1">
                      Permanently removes the entire 360° object including the image file, 
                      all markers, and camera settings. This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Warning for data loss */}
          {selectedOption === 'everything' && (hasMarkers || hasCameraSettings) && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-start space-x-3">
                <MdWarning className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-800">
                  <p className="font-medium">Warning: Data will be permanently lost</p>
                  <p className="mt-1">
                    This will permanently delete {hasMarkers ? `${imageData.markerList.length} marker${imageData.markerList.length !== 1 ? 's' : ''}` : ''}{hasMarkers && hasCameraSettings ? ' and ' : ''}{hasCameraSettings ? 'camera settings' : ''}. This action cannot be undone.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          
          {selectedOption === 'imageOnly' && (
            <button
              onClick={handleDeleteImageOnly}
              disabled={isProcessing}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Deleting...' : 'Delete Image Only'}
            </button>
          )}
          
          {selectedOption === 'everything' && (
            <button
              onClick={handleDeleteEverything}
              disabled={isProcessing}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Deleting...' : 'Delete Everything'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
