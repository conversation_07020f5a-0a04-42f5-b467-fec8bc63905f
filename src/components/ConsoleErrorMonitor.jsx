'use client'

import React, { useState, useEffect } from 'react'

/**
 * ConsoleErrorMonitor - Development tool to capture and display console errors
 * This component intercepts console.error calls and displays them in a UI panel
 * Only active in development mode
 */
export default function ConsoleErrorMonitor() {
  const [errors, setErrors] = useState([])
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') {
      return
    }

    // Store original console methods
    const originalError = console.error
    const originalWarn = console.warn

    // Override console.error to capture errors
    console.error = (...args) => {
      // Call original console.error
      originalError.apply(console, args)

      // Capture error for display
      const errorMessage = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ')

      const timestamp = new Date().toLocaleTimeString()

      setErrors(prev => [...prev.slice(-9), { // Keep last 10 errors
        id: Date.now(),
        message: errorMessage,
        timestamp,
        type: 'error',
        args
      }])
    }

    // Also capture warnings for React issues
    console.warn = (...args) => {
      // Call original console.warn
      originalWarn.apply(console, args)

      // Only capture React-related warnings
      const warningMessage = args.join(' ')
      if (warningMessage.includes('React') || warningMessage.includes('Warning')) {
        const timestamp = new Date().toLocaleTimeString()

        setErrors(prev => [...prev.slice(-9), {
          id: Date.now(),
          message: warningMessage,
          timestamp,
          type: 'warning',
          args
        }])
      }
    }

    // Capture unhandled promise rejections
    const handleUnhandledRejection = (event) => {
      const timestamp = new Date().toLocaleTimeString()
      setErrors(prev => [...prev.slice(-9), {
        id: Date.now(),
        message: `Unhandled Promise Rejection: ${event.reason}`,
        timestamp,
        type: 'promise',
        args: [event.reason]
      }])
    }

    // Capture global errors
    const handleGlobalError = (event) => {
      const timestamp = new Date().toLocaleTimeString()
      setErrors(prev => [...prev.slice(-9), {
        id: Date.now(),
        message: `Global Error: ${event.error?.message || event.message}`,
        timestamp,
        type: 'global',
        args: [event.error || event]
      }])
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleGlobalError)

    // Cleanup on unmount
    return () => {
      console.error = originalError
      console.warn = originalWarn
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleGlobalError)
    }
  }, [])

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`fixed bottom-4 right-4 z-50 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          errors.length > 0
            ? 'bg-red-600 text-white animate-pulse'
            : 'bg-gray-600 text-white'
        }`}
      >
        Issues ({errors.length})
      </button>

      {/* Error Panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 w-96 max-h-96 bg-black/90 text-white rounded-lg border border-red-500 z-50 overflow-hidden">
          <div className="flex items-center justify-between p-3 border-b border-red-500">
            <h3 className="font-semibold text-red-400">Console Issues</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setErrors([])}
                className="px-2 py-1 text-xs bg-red-600 rounded hover:bg-red-700"
              >
                Clear
              </button>
              <button
                onClick={() => setIsVisible(false)}
                className="px-2 py-1 text-xs bg-gray-600 rounded hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
          
          <div className="max-h-80 overflow-y-auto p-3 space-y-2">
            {errors.length === 0 ? (
              <p className="text-gray-400 text-sm">No issues captured</p>
            ) : (
              errors.map(error => {
                const typeColors = {
                  error: 'border-red-700 bg-red-900/20 text-red-200',
                  warning: 'border-yellow-700 bg-yellow-900/20 text-yellow-200',
                  promise: 'border-purple-700 bg-purple-900/20 text-purple-200',
                  global: 'border-orange-700 bg-orange-900/20 text-orange-200'
                }
                const colorClass = typeColors[error.type] || typeColors.error

                return (
                  <div key={error.id} className={`border rounded p-2 ${colorClass}`}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs opacity-75">{error.timestamp}</span>
                      <span className="text-xs px-1 rounded bg-black/30">
                        {error.type || 'error'}
                      </span>
                    </div>
                    <pre className="text-xs whitespace-pre-wrap break-words">
                      {error.message}
                    </pre>
                  </div>
                )
              })
            )}
          </div>
        </div>
      )}
    </>
  )
}
