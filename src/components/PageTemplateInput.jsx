'use client';

import React, { useState, useCallback, useRef } from 'react';
import TextEditor from '@/components/TextEditor';
import HtmlContentDisplay from '@/components/HtmlContentDisplay';

/**
 * PageTemplateInput Component
 * 
 * A form component for creating page content with rich text editing and image upload.
 * Generates an islandPage object with title, body1, and image fields.
 * 
 * Features:
 * - Rich text editing with TextEditor component for title and body1 fields
 * - Image upload functionality with Firebase storage integration
 * - Form validation and error handling
 * - Preview functionality for formatted content
 * - Follows existing UI/UX patterns from the codebase
 */

const PageTemplateInput = ({
  initialData = null,
  onSave,
  onCancel,
  onAddContent = null,
  isLoading = false,
  editMode = false
}) => {
  // Form state
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    body1: initialData?.body1 || '',
    image: initialData?.image || ''
  });

  // Update form data when initialData changes (for edit mode)
  React.useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title || '',
        body1: initialData.body1 || '',
        image: initialData.image || ''
      });
      setImagePreview(initialData.image || '');
    }
  }, [initialData]);

  // UI state
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(initialData?.image || '');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);

  // Refs
  const fileInputRef = useRef(null);

  // Handle input changes for TextEditor fields
  const handleInputChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  }, [errors]);

  // Handle image file selection
  const handleImageSelect = useCallback((e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        image: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)'
      }));
      return;
    }

    // Validate file size (15MB max)
    const maxSize = 15 * 1024 * 1024;
    if (file.size > maxSize) {
      setErrors(prev => ({
        ...prev,
        image: 'Image file size must be less than 15MB'
      }));
      return;
    }

    setImageFile(file);
    setErrors(prev => ({ ...prev, image: '' }));

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
  }, []);

  // Upload image to Firebase
  const uploadImage = async () => {
    if (!imageFile) return formData.image;

    try {
      setUploadProgress(10);
      
      const uploadFormData = new FormData();
      uploadFormData.append('file', imageFile);

      setUploadProgress(50);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: uploadFormData,
      });

      setUploadProgress(80);

      const result = await response.json();

      if (result.success && result.files && result.files.length > 0) {
        setUploadProgress(100);
        return result.files[0].url;
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw new Error(`Image upload failed: ${error.message}`);
    } finally {
      setUploadProgress(0);
    }
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.body1.trim()) {
      newErrors.body1 = 'Body content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload image if new file selected
      let imageUrl = formData.image;
      if (imageFile) {
        imageUrl = await uploadImage();
      }

      // Create islandPage object
      const islandPage = {
        title: formData.title,
        body1: formData.body1,
        image: imageUrl
      };

      console.log('Generated islandPage object:', islandPage);

      // Call onSave callback if provided
      if (onSave) {
        await onSave(islandPage);
      }

      // Show success message
      alert('Page content saved successfully!');

    } catch (error) {
      console.error('Save error:', error);
      setErrors(prev => ({
        ...prev,
        submit: error.message || 'Failed to save page content'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form reset
  const handleReset = () => {
    if (confirm('Are you sure you want to reset the form? All unsaved changes will be lost.')) {
      setFormData({
        title: initialData?.title || '',
        body1: initialData?.body1 || '',
        image: initialData?.image || ''
      });
      setErrors({});
      setImageFile(null);
      setImagePreview(initialData?.image || '');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle image removal
  const handleImageRemove = () => {
    if (confirm('Are you sure you want to remove the current image?')) {
      setImageFile(null);
      setImagePreview('');
      setFormData(prev => ({ ...prev, image: '' }));
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle add content to additional content array
  const handleAddContent = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Upload image if new file selected
      let imageUrl = formData.image;
      if (imageFile) {
        imageUrl = await uploadImage();
      }

      // Create content object
      const contentData = {
        title: formData.title,
        body1: formData.body1,
        image: imageUrl
      };

      // Call the onAddContent callback
      if (onAddContent) {
        onAddContent(contentData);

        // Clear the form after successful addition
        setFormData({
          title: '',
          body1: '',
          image: ''
        });
        setImageFile(null);
        setImagePreview('');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        alert('Content added successfully!');
      }
    } catch (error) {
      console.error('Add content error:', error);
      setErrors(prev => ({
        ...prev,
        submit: error.message || 'Failed to add content'
      }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {editMode ? 'Edit Content Item' : 'Page Template Input'}
            </h2>
            <p className="text-gray-600">
              {editMode
                ? 'Edit existing content with rich text editing and image upload functionality.'
                : 'Create page content with rich text editing and image upload functionality.'
              }
            </p>
          </div>
          {editMode && (
            <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
              Edit Mode
            </div>
          )}
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <div className={`${errors.title ? 'border-red-500 rounded-md' : ''}`}>
            <TextEditor
              value={formData.title}
              onChange={(content) => handleInputChange('title', content)}
              placeholder="Enter page title..."
              className="w-full"
              minHeight="120px"
              disabled={isLoading || isSubmitting}
            />
          </div>
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* Body1 Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Body Content *
          </label>
          <div className={`${errors.body1 ? 'border-red-500 rounded-md' : ''}`}>
            <TextEditor
              value={formData.body1}
              onChange={(content) => handleInputChange('body1', content)}
              placeholder="Enter body content..."
              className="w-full"
              minHeight="200px"
              disabled={isLoading || isSubmitting}
            />
          </div>
          {errors.body1 && (
            <p className="mt-1 text-sm text-red-600">{errors.body1}</p>
          )}
        </div>

        {/* Image Upload Field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Image
          </label>
          <div className="space-y-4">
            {/* File Input */}
            <div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                onChange={handleImageSelect}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                disabled={isLoading || isSubmitting}
              />
              <p className="mt-1 text-xs text-gray-500">
                Supported formats: JPEG, PNG, GIF, WebP. Max size: 15MB
              </p>
            </div>

            {/* Upload Progress */}
            {uploadProgress > 0 && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            )}

            {/* Image Preview */}
            {imagePreview && (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="max-w-xs h-32 object-cover rounded-md border border-gray-300"
                />
                <button
                  type="button"
                  onClick={handleImageRemove}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                  disabled={isLoading || isSubmitting}
                >
                  ×
                </button>
              </div>
            )}
          </div>
          {errors.image && (
            <p className="mt-1 text-sm text-red-600">{errors.image}</p>
          )}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}



        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 pt-4">
          <button
            type="submit"
            disabled={isLoading || isSubmitting}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            <span>{isSubmitting ? (editMode ? 'Saving Changes...' : 'Saving...') : (editMode ? 'Save Changes' : 'Save Page Content')}</span>
          </button>

          {/* Add Content Button - only show when not in edit mode and onAddContent is provided */}
          {!editMode && onAddContent && (
            <button
              type="button"
              onClick={handleAddContent}
              disabled={isLoading || isSubmitting}
              className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <span>Add Content</span>
            </button>
          )}

          <button
            type="button"
            onClick={handleReset}
            disabled={isLoading || isSubmitting}
            className="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Reset Form
          </button>

          <button
            type="button"
            onClick={() => setShowPreview(!showPreview)}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>

          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={isLoading || isSubmitting}
              className="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {editMode ? 'Cancel Edit' : 'Cancel'}
            </button>
          )}
        </div>
      </form>

      {/* Preview Section */}
      {showPreview && (
        <div className="mt-8 border-t pt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">
            Content Preview
          </h3>
          <div className="space-y-6">
            {/* Title Preview */}
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Title:</h4>
              <div className="bg-gray-50 p-4 rounded border min-h-[60px]">
                {formData.title ? (
                  <HtmlContentDisplay htmlString={formData.title} />
                ) : (
                  <p className="text-gray-500 italic">No title content</p>
                )}
              </div>
            </div>

            {/* Body1 Preview */}
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Body Content:</h4>
              <div className="bg-gray-50 p-4 rounded border min-h-[100px]">
                {formData.body1 ? (
                  <HtmlContentDisplay htmlString={formData.body1} />
                ) : (
                  <p className="text-gray-500 italic">No body content</p>
                )}
              </div>
            </div>

            {/* Image Preview */}
            {(imagePreview || formData.image) && (
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Image:</h4>
                <img
                  src={imagePreview || formData.image}
                  alt="Page image"
                  className="max-w-md h-48 object-cover rounded border"
                />
              </div>
            )}

            {/* Generated Object Preview */}
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Generated islandPage Object:</h4>
              <div className="bg-gray-100 p-4 rounded text-sm font-mono overflow-x-auto">
                <pre>{JSON.stringify({
                  title: formData.title,
                  body1: formData.body1,
                  image: imagePreview || formData.image
                }, null, 2)}</pre>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PageTemplateInput;
