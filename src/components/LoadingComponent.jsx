import React from 'react'

const Spinner = ({ size = 'h-6 w-6', color = 'text-gray-50', borderWidth = 'border-2', className = '' }) => {
  return (
    <div className={`animate-spin rounded-full ${borderWidth} border-t-transparent ${color} ${size} ${className}`}></div>
  );
};

export default function LoadingComponent({ size = "h-10 w-10", color = "text-white", borderWidth = "border-4" }) {
  return (
    // <div className="flex absolute m-auto top-0 left-0 right-0 bottom-0 z-50 items-center justify-center h-fit w-fit p-4 rounded-full">
    //   <Spinner
    //     size={size}
    //     color={color}
    //     borderWidth={borderWidth}
    //     className="items-center justify-center"
    //   />
    // </div>
    <div className="spiner fixed top-0 z-50 left-0 w-full h-full flex flex-col justify-center items-center bg-black/70 text-white">
      <div className="w-[50px] h-[50px] border-[5px] border-white/30 rounded-full border-t-white animate-spin mb-5"></div>
    </div>
  )
}
