'use client';

import { useState } from 'react';
import {
  MdEdit,
  MdDelete,
  MdImage,
  MdCheckBox,
  MdCheckBoxOutlineBlank
} from 'react-icons/md';

export default function HeroImageList({
  heroImages = [],
  onEdit,
  onDelete,
  isLoading = false
}) {
  const [selectedItems, setSelectedItems] = useState(new Set());
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('desc');

  const handleSelectAll = () => {
    if (selectedItems.size === heroImages.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(heroImages.map(item => item._id)));
    }
  };

  const handleSelectItem = (id) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedItems(newSelected);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleBulkDelete = () => {
    if (selectedItems.size > 0 && onDelete) {
      const confirmed = window.confirm(
        `Are you sure you want to delete ${selectedItems.size} hero image(s)?`
      );
      if (confirmed) {
        onDelete(Array.from(selectedItems));
        setSelectedItems(new Set());
      }
    }
  };



  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">
            Hero Images ({heroImages.length})
          </h2>
          
          {/* Bulk Actions */}
          {selectedItems.size > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {selectedItems.size} selected
              </span>

              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <button
                  onClick={handleSelectAll}
                  className="flex items-center text-gray-500 hover:text-gray-700"
                >
                  {selectedItems.size === heroImages.length ? (
                    <MdCheckBox className="text-blue-600" />
                  ) : (
                    <MdCheckBoxOutlineBlank />
                  )}
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700"
                onClick={() => handleSort('name')}
              >
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700"
                onClick={() => handleSort('createdAt')}
              >
                Created
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {heroImages.map((heroImage) => (
              <tr key={heroImage._id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <button
                    onClick={() => handleSelectItem(heroImage._id)}
                    className="flex items-center text-gray-500 hover:text-gray-700"
                  >
                    {selectedItems.has(heroImage._id) ? (
                      <MdCheckBox className="text-blue-600" />
                    ) : (
                      <MdCheckBoxOutlineBlank />
                    )}
                  </button>
                </td>
                <td className="px-6 py-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                    {heroImage.url ? (
                      <img
                        src={heroImage.url}
                        alt={heroImage.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <MdImage className="text-gray-400 text-2xl" />
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-gray-900">
                    {heroImage.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    {heroImage.contentType}
                  </div>
                </td>

                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatFileSize(heroImage.size || 0)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {formatDate(heroImage.createdAt)}
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEdit(heroImage)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit"
                    >
                      <MdEdit />
                    </button>
                    <button
                      onClick={() => onDelete([heroImage._id])}
                      className="text-red-600 hover:text-red-800"
                      title="Delete"
                    >
                      <MdDelete />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {heroImages.length === 0 && (
        <div className="p-12 text-center">
          <MdImage className="mx-auto text-gray-400 text-6xl mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hero images</h3>
          <p className="text-gray-500">Get started by creating your first hero image.</p>
        </div>
      )}
    </div>
  );
}
