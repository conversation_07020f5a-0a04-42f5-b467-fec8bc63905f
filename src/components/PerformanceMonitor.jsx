'use client'

import React, { useState, useEffect, useRef } from 'react'

/**
 * PerformanceMonitor - Development tool to monitor application performance
 * Tracks FPS, memory usage, and long-running tasks
 */
export default function PerformanceMonitor() {
  const [isVisible, setIsVisible] = useState(false)
  const [metrics, setMetrics] = useState({
    fps: 0,
    memory: { used: 0, total: 0 },
    longTasks: 0,
    renderTime: 0
  })

  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  const longTaskCount = useRef(0)

  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') {
      return
    }

    let animationFrame

    // FPS and render time monitoring
    const measurePerformance = () => {
      const now = performance.now()
      frameCount.current++

      // Calculate FPS every second
      if (now - lastTime.current >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / (now - lastTime.current))
        
        // Get memory info if available
        const memoryInfo = performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(performance.memory.totalJSHeapSize / 1048576) // MB
        } : { used: 0, total: 0 }

        setMetrics(prev => ({
          ...prev,
          fps,
          memory: memoryInfo,
          longTasks: longTaskCount.current,
          renderTime: now - lastTime.current
        }))

        frameCount.current = 0
        lastTime.current = now
      }

      animationFrame = requestAnimationFrame(measurePerformance)
    }

    // Long task monitoring
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 50) { // Tasks longer than 50ms
          longTaskCount.current++
        }
      }
    })

    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] })
    } catch (e) {
      // Long task API not supported
    }

    measurePerformance()

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
      longTaskObserver.disconnect()
    }
  }, [])

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const getFPSColor = (fps) => {
    if (fps >= 55) return 'text-green-400'
    if (fps >= 30) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getMemoryColor = (used, total) => {
    const percentage = (used / total) * 100
    if (percentage < 50) return 'text-green-400'
    if (percentage < 80) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`fixed bottom-4 left-4 z-50 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          metrics.fps < 30 || metrics.longTasks > 10
            ? 'bg-red-600 text-white animate-pulse' 
            : 'bg-blue-600 text-white'
        }`}
      >
        Perf
      </button>

      {/* Performance Panel */}
      {isVisible && (
        <div className="fixed bottom-16 left-4 w-64 bg-black/90 text-white rounded-lg border border-blue-500 z-50 p-3">
          <div className="flex items-center justify-between mb-3 border-b border-blue-500 pb-2">
            <h3 className="font-semibold text-blue-400">Performance</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="px-2 py-1 text-xs bg-gray-600 rounded hover:bg-gray-700"
            >
              Close
            </button>
          </div>
          
          <div className="space-y-2 text-sm">
            {/* FPS */}
            <div className="flex justify-between">
              <span className="text-gray-300">FPS:</span>
              <span className={getFPSColor(metrics.fps)}>
                {metrics.fps}
              </span>
            </div>

            {/* Memory */}
            {metrics.memory.total > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-300">Memory:</span>
                <span className={getMemoryColor(metrics.memory.used, metrics.memory.total)}>
                  {metrics.memory.used}MB / {metrics.memory.total}MB
                </span>
              </div>
            )}

            {/* Long Tasks */}
            <div className="flex justify-between">
              <span className="text-gray-300">Long Tasks:</span>
              <span className={metrics.longTasks > 5 ? 'text-red-400' : 'text-green-400'}>
                {metrics.longTasks}
              </span>
            </div>

            {/* Render Time */}
            <div className="flex justify-between">
              <span className="text-gray-300">Render:</span>
              <span className={metrics.renderTime > 16 ? 'text-yellow-400' : 'text-green-400'}>
                {metrics.renderTime.toFixed(1)}ms
              </span>
            </div>

            {/* Performance Tips */}
            {(metrics.fps < 30 || metrics.longTasks > 10) && (
              <div className="mt-3 p-2 bg-red-900/20 border border-red-700 rounded text-xs">
                <div className="text-red-400 font-medium mb-1">Performance Issues:</div>
                {metrics.fps < 30 && <div>• Low FPS detected</div>}
                {metrics.longTasks > 10 && <div>• Many long tasks detected</div>}
                {metrics.memory.used / metrics.memory.total > 0.8 && <div>• High memory usage</div>}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}
