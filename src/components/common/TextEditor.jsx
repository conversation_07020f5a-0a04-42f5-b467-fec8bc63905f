'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

const TextEditor = ({
  value = '',
  onChange,
  placeholder = 'Start typing...',
  className = '',
  style = {},
  theme = 'snow',
  modules = {},
  formats = [],
  disabled = false,
  ...props
}) => {
  const [content, setContent] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const [showEmailInput, setShowEmailInput] = useState(false);
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [emailInput, setEmailInput] = useState('');
  const [urlInput, setUrlInput] = useState('');
  const [urlText, setUrlText] = useState('');
  const [selectedText, setSelectedText] = useState('');
  const editorRef = useRef(null);
  const isUpdatingRef = useRef(false);

  // Initialize content when component mounts or value changes
  useEffect(() => {
    if (value !== content && !isUpdatingRef.current) {
      setContent(value);
      if (editorRef.current && editorRef.current.innerHTML !== value) {
        editorRef.current.innerHTML = value || '';
      }
    }
  }, [value, content]);

  // Handle content changes
  const handleInput = useCallback(() => {
    if (isUpdatingRef.current || !editorRef.current) return;

    const newContent = editorRef.current.innerHTML;
    
    // Clean up empty paragraphs that might be created
    const cleanedContent = newContent.replace(/<p><br><\/p>/g, '').replace(/<p><\/p>/g, '');
    
    setContent(cleanedContent);
    if (onChange) {
      onChange(cleanedContent);
    }
  }, [onChange]);

  // Apply formatting commands
  const execCommand = useCallback((command, value = null) => {
    if (disabled) return;

    try {
      document.execCommand(command, false, value);
      
      // Update content after formatting
      isUpdatingRef.current = true;
      setTimeout(() => {
        if (editorRef.current) {
          const newContent = editorRef.current.innerHTML;
          const cleanedContent = newContent.replace(/<p><br><\/p>/g, '').replace(/<p><\/p>/g, '');
          setContent(cleanedContent);
          if (onChange) {
            onChange(cleanedContent);
          }
        }
        isUpdatingRef.current = false;
      }, 10);
    } catch (error) {
      console.warn('Command execution failed:', command, error);
    }
  }, [disabled, onChange]);

  // Handle paste events to clean up content
  const handlePaste = useCallback((e) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
    handleInput();
  }, [handleInput]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          execCommand('bold');
          break;
        case 'i':
          e.preventDefault();
          execCommand('italic');
          break;
        case 'u':
          e.preventDefault();
          execCommand('underline');
          break;
        default:
          break;
      }
    }
  }, [execCommand]);

  // Handle text selection changes
  const handleSelectionChange = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim());
    } else {
      setSelectedText('');
    }
  }, []);

  // Get current selection (real-time, not from state)
  const getCurrentSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      return selection.toString().trim();
    }
    return '';
  }, []);

  // Handle email insertion
  const handleInsertEmail = useCallback(() => {
    if (emailInput.trim()) {
      const emailHtml = `<a href="mailto:${emailInput.trim()}" style="color: white; text-decoration: underline;">${emailInput.trim()}</a>`;
      document.execCommand('insertHTML', false, emailHtml);
      setEmailInput('');
      setShowEmailInput(false);
      handleInput();
    }
  }, [emailInput, handleInput]);

  // Handle email button click (with selected text support)
  const handleEmailButtonClick = useCallback(() => {
    const currentSelection = getCurrentSelection();
    if (currentSelection) {
      // Convert selected text to email link directly
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        if (!range.collapsed) {
          try {
            // Get the selected content with its formatting
            const selectedContent = range.extractContents();

            // Create email link element
            const emailLink = document.createElement('a');
            emailLink.href = `mailto:${currentSelection}`;
            emailLink.style.color = 'white';
            emailLink.style.textDecoration = 'underline';

            // Preserve the original formatting by appending the selected content
            emailLink.appendChild(selectedContent);

            // Insert the email link
            range.insertNode(emailLink);

            // Clear selection and update
            selection.removeAllRanges();
            setSelectedText('');
            handleInput();
          } catch (error) {
            console.warn('Email conversion failed, using fallback method:', error);
            // Fallback: simple HTML insertion
            const emailHtml = `<a href="mailto:${currentSelection}" style="color: white; text-decoration: underline;">${currentSelection}</a>`;
            document.execCommand('insertHTML', false, emailHtml);
            setSelectedText('');
            handleInput();
          }
        }
      }
    } else {
      // Show email input form
      setShowEmailInput(!showEmailInput);
      setShowUrlInput(false);
    }
  }, [getCurrentSelection, showEmailInput, handleInput]);

  // Handle URL insertion
  const handleInsertUrl = useCallback(() => {
    if (urlInput.trim()) {
      const linkText = urlText.trim() || urlInput.trim();

      // Check if we have selected text that matches the link text
      const selection = window.getSelection();
      const currentSelection = getCurrentSelection();

      if (currentSelection && currentSelection === urlText.trim() && selection.rangeCount > 0) {
        // We have selected text that matches our link text - preserve formatting
        const range = selection.getRangeAt(0);
        if (!range.collapsed) {
          try {
            // Get the selected content with its formatting
            const selectedContent = range.extractContents();

            // Create link element
            const link = document.createElement('a');
            link.href = urlInput.trim();
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            link.style.color = 'white';
            link.style.textDecoration = 'underline';

            // Preserve the original formatting by appending the selected content
            link.appendChild(selectedContent);

            // Insert the link
            range.insertNode(link);

            // Clear selection and update
            selection.removeAllRanges();
          } catch (error) {
            console.warn('Link creation failed, using fallback method:', error);
            // Fallback: simple HTML insertion
            const urlHtml = `<a href="${urlInput.trim()}" target="_blank" rel="noopener noreferrer" style="color: white; text-decoration: underline;">${linkText}</a>`;
            document.execCommand('insertHTML', false, urlHtml);
          }
        }
      } else {
        // No matching selected text - use simple HTML insertion
        const urlHtml = `<a href="${urlInput.trim()}" target="_blank" rel="noopener noreferrer" style="color: white; text-decoration: underline;">${linkText}</a>`;
        document.execCommand('insertHTML', false, urlHtml);
      }

      setUrlInput('');
      setUrlText('');
      setShowUrlInput(false);
      handleInput();
    }
  }, [urlInput, urlText, getCurrentSelection, handleInput]);

  // Handle URL button click (with selected text support)
  const handleUrlButtonClick = useCallback(() => {
    const currentSelection = getCurrentSelection();
    if (currentSelection) {
      // Use selected text as default link text
      setUrlText(currentSelection);
      setShowUrlInput(true);
      setShowEmailInput(false);
    } else {
      // Show URL input form
      setShowUrlInput(!showUrlInput);
      setShowEmailInput(false);
    }
  }, [getCurrentSelection, showUrlInput]);

  // Detect current font size of selection
  const detectCurrentFontSize = useCallback(() => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      let element = range.commonAncestorContainer;

      if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      while (element && element !== editorRef.current) {
        const computedStyle = window.getComputedStyle(element);
        const fontSize = computedStyle.fontSize;
        if (fontSize && fontSize !== 'inherit') {
          console.log('🔍 Detected font size:', fontSize, 'on element:', element.tagName);
          return fontSize;
        }
        element = element.parentElement;
      }
    }
    return null;
  }, []);

  // Handle font size changes (robust implementation with debugging)
  const handleFontSizeChange = useCallback((e) => {
    const fontSize = e.target.value;
    console.log('🎨 Font size change requested:', fontSize);

    // Detect current font size for debugging
    const currentFontSize = detectCurrentFontSize();
    console.log('🔍 Current font size of selection:', currentFontSize);

    if (!fontSize) return;

    const selection = window.getSelection();
    console.log('📝 Selection info:', {
      rangeCount: selection.rangeCount,
      hasSelection: selection.toString().length > 0,
      selectedText: selection.toString().substring(0, 50)
    });

    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      if (!range.collapsed) {
        // Text is selected - apply font size to selection
        console.log('✅ Applying font size to selected text:', fontSize);

        // Method 1: Try insertHTML first (most reliable)
        try {
          const selectedText = selection.toString();
          if (selectedText) {
            const styledHtml = `<span style="font-size: ${fontSize}; display: inline;">${selectedText}</span>`;
            document.execCommand('insertHTML', false, styledHtml);
            console.log('✅ Font size applied successfully via insertHTML');
            handleInput();
            e.target.value = '';
            return;
          }
        } catch (error) {
          console.warn('⚠️ insertHTML method failed:', error);
        }

        // Method 2: Try DOM manipulation as fallback
        try {
          const span = document.createElement('span');
          span.style.fontSize = fontSize;
          span.style.display = 'inline';

          const selectedContent = range.extractContents();
          if (selectedContent.textContent) {
            span.appendChild(selectedContent);
            range.insertNode(span);
            selection.removeAllRanges();
            console.log('✅ Font size applied successfully via DOM manipulation');
            handleInput();
            e.target.value = '';
            return;
          }
        } catch (error) {
          console.warn('⚠️ DOM manipulation method failed:', error);
        }

        // Method 3: Try execCommand fontSize as last resort
        try {
          const sizeMap = {
            '12px': '1',
            '16px': '2',
            '20px': '3',
            '24px': '4',
            '28px': '4',
            '32px': '5',
            '40px': '5',
            '48px': '6',
            '60px': '7',
            '72px': '7'
          };

          const execSize = sizeMap[fontSize] || '3';
          document.execCommand('fontSize', false, execSize);
          console.log('✅ Font size applied via execCommand fontSize');
          handleInput();
        } catch (error) {
          console.error('❌ All font size application methods failed:', error);
        }

      } else {
        // No text selected - set up for future typing
        console.log('🔮 Setting up font size for future typing:', fontSize);
        try {
          editorRef.current?.focus();

          const span = document.createElement('span');
          span.style.fontSize = fontSize;
          span.style.display = 'inline';
          span.innerHTML = '&nbsp;';

          range.insertNode(span);

          const newRange = document.createRange();
          newRange.setStart(span.firstChild, 1);
          newRange.setEnd(span.firstChild, 1);
          selection.removeAllRanges();
          selection.addRange(newRange);

          console.log('✅ Font size setup for future typing');
          handleInput();
        } catch (error) {
          console.warn('⚠️ Could not set font size for future typing:', error);
        }
      }
    } else {
      // No selection at all - just focus the editor
      console.log('🎯 No selection, focusing editor');
      editorRef.current?.focus();
    }

    // Reset the dropdown to default after applying
    e.target.value = '';
  }, [handleInput, detectCurrentFontSize]);

  // Toolbar button component
  const ToolbarButton = ({ onClick, children, title, isActive = false }) => (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`px-3 py-1 text-sm font-medium rounded border transition-colors ${
        isActive
          ? 'bg-blue-100 border-blue-300 text-blue-700'
          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      {children}
    </button>
  );

  // Handle line height changes (improved and more robust)
  const handleLineHeightChange = useCallback((e) => {
    const lineHeight = e.target.value;
    if (!lineHeight) return;

    const selection = window.getSelection();

    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      if (!range.collapsed) {
        // Text is selected - apply line height to selection
        try {
          // Create a new span with the line height
          const span = document.createElement('span');
          span.style.lineHeight = lineHeight;
          span.style.display = 'inline-block';
          span.style.width = '100%';
          span.style.minHeight = '1em';

          // Extract the selected content
          const selectedContent = range.extractContents();

          if (selectedContent.textContent) {
            span.appendChild(selectedContent);
            range.insertNode(span);

            // Select the newly styled content
            const newRange = document.createRange();
            newRange.selectNodeContents(span);
            selection.removeAllRanges();
            selection.addRange(newRange);

            handleInput();
          }
        } catch (error) {
          console.warn('Line height application failed, trying alternative method:', error);

          // Alternative method: use execCommand with insertHTML
          try {
            const selectedText = selection.toString();
            if (selectedText) {
              const styledHtml = `<span style="line-height: ${lineHeight}; display: inline-block; width: 100%; min-height: 1em;">${selectedText}</span>`;
              document.execCommand('insertHTML', false, styledHtml);
              handleInput();
            }
          } catch (fallbackError) {
            console.warn('All line height application methods failed:', fallbackError);
          }
        }
      } else {
        // No text selected - set up for future typing
        try {
          editorRef.current?.focus();

          // Create a temporary span for future typing with better line height support
          const span = document.createElement('span');
          span.style.lineHeight = lineHeight;
          span.style.display = 'inline-block';
          span.style.width = '100%';
          span.style.minHeight = '1em';
          span.innerHTML = '&nbsp;'; // Non-breaking space

          range.insertNode(span);

          // Position cursor inside the span
          const newRange = document.createRange();
          newRange.setStart(span.firstChild, 1);
          newRange.setEnd(span.firstChild, 1);
          selection.removeAllRanges();
          selection.addRange(newRange);

          handleInput();
        } catch (error) {
          console.warn('Could not set line height for future typing:', error);
        }
      }
    } else {
      // No selection at all - just focus the editor
      editorRef.current?.focus();
    }

    // Reset the dropdown to default after applying
    e.target.value = '';
  }, [handleInput]);

  // Get minimum height from style prop or default
  const minHeight = style?.minHeight || '80px';

  return (
    <div className={`border rounded-md ${className}`} style={style} {...props}>
      {/* Simplified Toolbar */}
      <div className="flex items-center gap-2 p-3 border-b bg-gray-50 rounded-t-md flex-wrap">
        {/* Basic formatting buttons */}
        <ToolbarButton
          onClick={() => execCommand('bold')}
          title="Bold (Ctrl+B)"
        >
          <strong>B</strong>
        </ToolbarButton>

        <ToolbarButton
          onClick={() => execCommand('italic')}
          title="Italic (Ctrl+I)"
        >
          <em>I</em>
        </ToolbarButton>

        <ToolbarButton
          onClick={() => execCommand('underline')}
          title="Underline (Ctrl+U)"
        >
          <u>U</u>
        </ToolbarButton>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Text color picker */}
        <div className="flex items-center gap-1">
          <label className="text-sm text-gray-600">Color:</label>
          <input
            type="color"
            onChange={(e) => execCommand('foreColor', e.target.value)}
            disabled={disabled}
            className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
            title="Text Color"
            defaultValue="#000000"
          />
        </div>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Font size dropdown */}
        <div className="flex items-center gap-1">
          <label className="text-sm text-gray-600">Size:</label>
          <select
            onChange={handleFontSizeChange}
            disabled={disabled}
            className="px-2 py-1 text-sm border border-gray-300 rounded bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            title="Font Size"
            defaultValue=""
          >
            <option value="">Select Size</option>
            <option value="12px">12px (Tiny)</option>
            <option value="16px">16px (Small)</option>
            <option value="20px">20px (Normal)</option>
            <option value="24px">24px (Medium)</option>
            <option value="28px">28px (Large)</option>
            <option value="32px">32px (X-Large)</option>
            <option value="40px">40px (XX-Large)</option>
            <option value="48px">48px (Huge)</option>
            <option value="60px">60px (Giant)</option>
            <option value="72px">72px (Massive)</option>
          </select>
        </div>

        {/* Line height dropdown */}
        <div className="flex items-center gap-1">
          <label className="text-sm text-gray-600">Line:</label>
          <select
            onChange={handleLineHeightChange}
            disabled={disabled}
            className="px-2 py-1 text-sm border border-gray-300 rounded bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            title="Line Height"
            defaultValue=""
          >
            <option value="">Select Line Height</option>
            <option value="1.0">1.0 (Tight)</option>
            <option value="1.2">1.2 (Normal)</option>
            <option value="1.5">1.5 (Relaxed)</option>
            <option value="2.0">2.0 (Double)</option>
          </select>
        </div>

        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Email insertion */}
        <ToolbarButton
          onClick={handleEmailButtonClick}
          title={selectedText ? `Convert "${selectedText}" to email link` : "Insert Email"}
        >
          Email
        </ToolbarButton>

        {/* URL insertion */}
        <ToolbarButton
          onClick={handleUrlButtonClick}
          title={selectedText ? `Create link with "${selectedText}" as text` : "Insert Link"}
        >
          Link
        </ToolbarButton>
      </div>

      {/* Email input form */}
      {showEmailInput && (
        <div className="p-3 bg-blue-50 border-b">
          <div className="flex items-center gap-2">
            <input
              type="email"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              placeholder="Enter email address"
              className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleInsertEmail();
                } else if (e.key === 'Escape') {
                  setShowEmailInput(false);
                  setEmailInput('');
                }
              }}
            />
            <button
              onClick={handleInsertEmail}
              disabled={!emailInput.trim()}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Insert
            </button>
            <button
              onClick={() => {
                setShowEmailInput(false);
                setEmailInput('');
              }}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* URL input form */}
      {showUrlInput && (
        <div className="p-3 bg-green-50 border-b">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <input
                type="url"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                placeholder="Enter URL (https://...)"
                className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={urlText}
                onChange={(e) => setUrlText(e.target.value)}
                placeholder="Link text (optional - will use URL if empty)"
                className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleInsertUrl();
                  } else if (e.key === 'Escape') {
                    setShowUrlInput(false);
                    setUrlInput('');
                    setUrlText('');
                  }
                }}
              />
              <button
                onClick={handleInsertUrl}
                disabled={!urlInput.trim()}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Insert
              </button>
              <button
                onClick={() => {
                  setShowUrlInput(false);
                  setUrlInput('');
                  setUrlText('');
                }}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => {
          setIsFocused(false);
          setSelectedText('');
        }}
        onPaste={handlePaste}
        onKeyDown={handleKeyDown}
        onMouseUp={handleSelectionChange}
        onKeyUp={handleSelectionChange}
        onClick={handleSelectionChange}
        onSelect={handleSelectionChange}
        className={`p-4 outline-none transition-all ${
          isFocused ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
        } ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
        style={{
          minHeight,
          maxHeight: '300px',
          overflowY: 'auto'
        }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* Placeholder styling */}
      <style jsx>{`
        [contenteditable][data-placeholder]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
          font-style: italic;
        }

        [contenteditable] a {
          color: white;
          text-decoration: underline;
        }

        [contenteditable] a:hover {
          color: #f3f4f6;
        }

        /* Ensure font sizes and line heights are preserved and displayed correctly */
        [contenteditable] span[style*="font-size"],
        [contenteditable] span[style*="line-height"] {
          display: inline !important;
        }

        /* Font size specific styles for better rendering */
        [contenteditable] span[style*="font-size: 20px"] {
          font-size: 20px !important;
        }

        [contenteditable] span[style*="font-size: 28px"] {
          font-size: 28px !important;
        }

        [contenteditable] span[style*="font-size: 40px"] {
          font-size: 40px !important;
        }

        [contenteditable] span[style*="font-size: 60px"] {
          font-size: 60px !important;
        }

        /* Line height specific styles with better visual feedback */
        [contenteditable] span[style*="line-height: 1.0"] {
          line-height: 1.0 !important;
          display: inline-block !important;
          width: 100% !important;
          min-height: 1em !important;
        }

        [contenteditable] span[style*="line-height: 1.2"] {
          line-height: 1.2 !important;
          display: inline-block !important;
          width: 100% !important;
          min-height: 1em !important;
        }

        [contenteditable] span[style*="line-height: 1.5"] {
          line-height: 1.5 !important;
          display: inline-block !important;
          width: 100% !important;
          min-height: 1em !important;
        }

        [contenteditable] span[style*="line-height: 2.0"] {
          line-height: 2.0 !important;
          display: inline-block !important;
          width: 100% !important;
          min-height: 1em !important;
        }

        /* Ensure spans with multiple styles work correctly */
        [contenteditable] span[style*="line-height"] {
          display: inline-block !important;
          width: 100% !important;
          min-height: 1em !important;
        }

        /* Default spans without line-height should remain inline */
        [contenteditable] span[style]:not([style*="line-height"]) {
          display: inline !important;
        }
      `}</style>
    </div>
  );
};

export default TextEditor;
