'use client';

import { useState } from 'react';

export default function BookingActions({
  booking,
  onBookingUpdate,
  onBookingDelete,
  onBookingSelect
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteResult, setDeleteResult] = useState(null);

  const handleAction = async (action, data = {}) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${booking._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data }),
      });

      const result = await response.json();
      
      if (result.success) {
        onBookingUpdate(result.data);
        setShowDropdown(false);
      } else {
        alert(result.message || 'Action failed');
      }
    } catch (error) {
      alert('Failed to perform action');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (reason = 'Cancelled by administrator', sendEmail = true) => {
    setIsLoading(true);
    setDeleteResult(null);

    try {
      const response = await fetch(`/api/bookings/${booking._id}?reason=${encodeURIComponent(reason)}&sendEmail=${sendEmail}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setDeleteResult({
          success: true,
          message: 'Booking deleted successfully',
          emailSent: result.data.emailSent,
          emailError: result.data.emailError
        });

        // Close dropdown and confirmation dialog
        setShowDropdown(false);
        setShowDeleteConfirm(false);

        // Call parent handler
        onBookingDelete(booking._id, result.data);
      } else {
        setDeleteResult({
          success: false,
          message: result.message || 'Failed to delete booking'
        });
      }
    } catch (error) {
      setDeleteResult({
        success: false,
        message: 'Failed to delete booking: ' + error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
    setShowDropdown(false);
  };

  const handleStatusChange = (newStatus) => {
    const statusMessages = {
      confirmed: 'Confirm this booking?',
      checked_in: 'Check in this guest?',
      checked_out: 'Check out this guest?',
      cancelled: 'Cancel this booking?',
      no_show: 'Mark as no-show?',
    };

    if (confirm(statusMessages[newStatus] || `Change status to ${newStatus}?`)) {
      handleAction('change_status', { status: newStatus });
    }
  };

  const getAvailableActions = () => {
    const actions = [];
    
    switch (booking.status) {
      case 'pending':
        actions.push(
          { action: 'confirmed', label: 'Confirm Booking', color: 'text-blue-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
      case 'confirmed':
        actions.push(
          { action: 'checked_in', label: 'Check In', color: 'text-green-700' },
          { action: 'no_show', label: 'Mark No-Show', color: 'text-purple-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
      case 'checked_in':
        actions.push(
          { action: 'checked_out', label: 'Check Out', color: 'text-gray-700' }
        );
        break;
      case 'checked_out':
        // No status changes available for checked out bookings
        break;
      case 'cancelled':
        actions.push(
          { action: 'confirmed', label: 'Reactivate Booking', color: 'text-blue-700' }
        );
        break;
      case 'no_show':
        actions.push(
          { action: 'confirmed', label: 'Reactivate Booking', color: 'text-blue-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
    }
    
    return actions;
  };

  const availableActions = getAvailableActions();

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Quick View Button */}
        <button
          onClick={() => onBookingSelect(booking)}
          className="text-blue-600 hover:text-blue-900 text-sm"
          disabled={isLoading}
        >
          View
        </button>

        {/* Quick Actions Dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
              <div className="py-1">
                {/* Status Actions */}
                {availableActions.length > 0 && (
                  <>
                    <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      Status Actions
                    </div>
                    {availableActions.map((action) => (
                      <button
                        key={action.action}
                        onClick={() => handleStatusChange(action.action)}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${action.color}`}
                      >
                        {action.label}
                      </button>
                    ))}
                  </>
                )}

                {/* Communication Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Communication
                </div>
                
                <a
                  href={`mailto:${booking.customer?.email}`}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Send Email
                </a>
                
                {booking.customer?.phone && (
                  <a
                    href={`tel:${booking.customer.phone}`}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Call Guest
                  </a>
                )}

                {/* Management Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Management
                </div>
                
                <button
                  onClick={() => {
                    // This would open payment management
                    alert('Payment management coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Manage Payment
                </button>

                <button
                  onClick={() => {
                    // This would open modification dialog
                    alert('Booking modification coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Modify Booking
                </button>

                <button
                  onClick={() => {
                    // This would generate invoice
                    alert('Invoice generation coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Generate Invoice
                </button>

                {/* Dangerous Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Danger Zone
                </div>
                
                <button
                  onClick={handleDeleteClick}
                  className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                >
                  Delete Booking
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Confirm Booking Deletion
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              Are you sure you want to delete booking <strong>{booking.bookingNumber}</strong>?
            </p>
            <p className="text-sm text-gray-600 mb-4">
              This action cannot be undone.
            </p>

            {booking.customer?.email && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <p className="text-sm text-blue-800">
                  A cancellation email will be sent to: <strong>{booking.customer.email}</strong>
                </p>
              </div>
            )}

            {deleteResult && (
              <div className={`border rounded-md p-3 mb-4 ${
                deleteResult.success
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}>
                <p className={`text-sm ${
                  deleteResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {deleteResult.message}
                </p>
                {deleteResult.success && deleteResult.emailSent && (
                  <p className="text-sm text-green-700 mt-1">
                    ✓ Cancellation email sent successfully
                  </p>
                )}
                {deleteResult.success && deleteResult.emailError && (
                  <p className="text-sm text-yellow-700 mt-1">
                    ⚠ Email failed to send: {deleteResult.emailError}
                  </p>
                )}
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeleteResult(null);
                }}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {deleteResult?.success ? 'Close' : 'Cancel'}
              </button>
              {!deleteResult?.success && (
                <button
                  onClick={() => handleDelete('Cancelled by administrator', true)}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Deleting...' : 'Delete Booking'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
