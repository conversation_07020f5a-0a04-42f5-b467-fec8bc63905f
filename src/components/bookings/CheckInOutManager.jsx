'use client';

import { useState, useMemo } from 'react';

export default function CheckInOutManager({ 
  bookings, 
  isLoading, 
  onBookingUpdate, 
  onRefresh 
}) {
  const [activeTab, setActiveTab] = useState('checkin'); // 'checkin' | 'checkout' | 'inhouse'
  const [processingBookings, setProcessingBookings] = useState(new Set());

  // Filter bookings based on current tab
  const filteredBookings = useMemo(() => {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    switch (activeTab) {
      case 'checkin':
        return bookings.filter(booking => {
          const checkInDate = new Date(booking.dates.checkIn).toISOString().split('T')[0];
          return booking.status === 'confirmed' && checkInDate <= todayStr;
        });
      case 'checkout':
        return bookings.filter(booking => {
          const checkOutDate = new Date(booking.dates.checkOut).toISOString().split('T')[0];
          return booking.status === 'checked_in' && checkOutDate <= todayStr;
        });
      case 'inhouse':
        return bookings.filter(booking => booking.status === 'checked_in');
      default:
        return [];
    }
  }, [bookings, activeTab]);

  const handleCheckIn = async (booking) => {
    if (processingBookings.has(booking._id)) return;
    
    setProcessingBookings(prev => new Set(prev).add(booking._id));
    
    try {
      const response = await fetch(`/api/bookings/${booking._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'change_status', 
          status: 'checked_in',
          checkInTime: new Date().toISOString(),
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        onBookingUpdate(result.data);
      } else {
        alert(result.message || 'Check-in failed');
      }
    } catch (error) {
      alert('Failed to check in guest');
    } finally {
      setProcessingBookings(prev => {
        const newSet = new Set(prev);
        newSet.delete(booking._id);
        return newSet;
      });
    }
  };

  const handleCheckOut = async (booking) => {
    if (processingBookings.has(booking._id)) return;
    
    setProcessingBookings(prev => new Set(prev).add(booking._id));
    
    try {
      const response = await fetch(`/api/bookings/${booking._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'change_status', 
          status: 'checked_out',
          checkOutTime: new Date().toISOString(),
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        onBookingUpdate(result.data);
      } else {
        alert(result.message || 'Check-out failed');
      }
    } catch (error) {
      alert('Failed to check out guest');
    } finally {
      setProcessingBookings(prev => {
        const newSet = new Set(prev);
        newSet.delete(booking._id);
        return newSet;
      });
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDaysFromToday = (date) => {
    const today = new Date();
    const targetDate = new Date(date);
    const diffTime = targetDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return `${Math.abs(diffDays)} days ago`;
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    return `In ${diffDays} days`;
  };

  const getTabCount = (tab) => {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    switch (tab) {
      case 'checkin':
        return bookings.filter(booking => {
          const checkInDate = new Date(booking.dates.checkIn).toISOString().split('T')[0];
          return booking.status === 'confirmed' && checkInDate <= todayStr;
        }).length;
      case 'checkout':
        return bookings.filter(booking => {
          const checkOutDate = new Date(booking.dates.checkOut).toISOString().split('T')[0];
          return booking.status === 'checked_in' && checkOutDate <= todayStr;
        }).length;
      case 'inhouse':
        return bookings.filter(booking => booking.status === 'checked_in').length;
      default:
        return 0;
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('checkin')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'checkin'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🏨 Check-In
              {getTabCount('checkin') > 0 && (
                <span className="ml-2 bg-green-100 text-green-800 py-0.5 px-2.5 rounded-full text-xs">
                  {getTabCount('checkin')}
                </span>
              )}
            </button>
            
            <button
              onClick={() => setActiveTab('checkout')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'checkout'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🚪 Check-Out
              {getTabCount('checkout') > 0 && (
                <span className="ml-2 bg-blue-100 text-blue-800 py-0.5 px-2.5 rounded-full text-xs">
                  {getTabCount('checkout')}
                </span>
              )}
            </button>
            
            <button
              onClick={() => setActiveTab('inhouse')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'inhouse'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🏠 In-House
              {getTabCount('inhouse') > 0 && (
                <span className="ml-2 bg-purple-100 text-purple-800 py-0.5 px-2.5 rounded-full text-xs">
                  {getTabCount('inhouse')}
                </span>
              )}
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {filteredBookings.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500">
                {activeTab === 'checkin' && 'No guests ready for check-in'}
                {activeTab === 'checkout' && 'No guests ready for check-out'}
                {activeTab === 'inhouse' && 'No guests currently in-house'}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredBookings.map((booking) => (
                <div key={booking._id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        {/* Guest Avatar */}
                        <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-lg font-medium text-gray-700">
                            {booking.customer?.name?.charAt(0)?.toUpperCase()}
                          </span>
                        </div>
                        
                        {/* Guest Info */}
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h3 className="text-lg font-medium text-gray-900">
                              {booking.customer?.name}
                            </h3>
                            <span className="text-sm text-gray-500">
                              {booking.bookingNumber}
                            </span>
                          </div>
                          
                          <div className="mt-1 text-sm text-gray-600">
                            <div className="flex items-center space-x-4">
                              <span>{booking.package?.name}</span>
                              <span>•</span>
                              <span>{booking.guests?.total} guests</span>
                              <span>•</span>
                              <span>
                                {formatDate(booking.dates.checkIn)} - {formatDate(booking.dates.checkOut)}
                              </span>
                            </div>
                          </div>
                          
                          {/* Contact Info */}
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                            <span>{booking.customer?.email}</span>
                            {booking.customer?.phone && (
                              <>
                                <span>•</span>
                                <span>{booking.customer.phone}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Special Requests */}
                      {booking.specialRequests && Object.keys(booking.specialRequests).length > 0 && (
                        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <h4 className="text-sm font-medium text-yellow-800 mb-1">Special Requests:</h4>
                          <div className="text-sm text-yellow-700">
                            {Object.entries(booking.specialRequests)
                              .filter(([_, value]) => value)
                              .map(([key, value]) => `${key}: ${value}`)
                              .join(', ')}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Action Button */}
                    <div className="ml-6">
                      {activeTab === 'checkin' && (
                        <button
                          onClick={() => handleCheckIn(booking)}
                          disabled={processingBookings.has(booking._id)}
                          className="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {processingBookings.has(booking._id) ? (
                            <div className="flex items-center space-x-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              <span>Processing...</span>
                            </div>
                          ) : (
                            'Check In'
                          )}
                        </button>
                      )}
                      
                      {activeTab === 'checkout' && (
                        <button
                          onClick={() => handleCheckOut(booking)}
                          disabled={processingBookings.has(booking._id)}
                          className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {processingBookings.has(booking._id) ? (
                            <div className="flex items-center space-x-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              <span>Processing...</span>
                            </div>
                          ) : (
                            'Check Out'
                          )}
                        </button>
                      )}
                      
                      {activeTab === 'inhouse' && (
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">
                            Checked in: {booking.checkInTime ? formatTime(booking.checkInTime) : 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            Check-out: {getDaysFromToday(booking.dates.checkOut)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
