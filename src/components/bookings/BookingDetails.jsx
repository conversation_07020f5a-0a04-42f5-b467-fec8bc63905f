'use client';

import { useState } from 'react';
import BookingEditModal from './BookingEditModal';

export default function BookingDetails({ 
  booking, 
  isLoading, 
  onBookingUpdate, 
  onBookingDelete, 
  onBack 
}) {
  const [activeTab, setActiveTab] = useState('overview');
  const [showEditModal, setShowEditModal] = useState(false);

  if (isLoading || !booking) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      confirmed: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Confirmed' },
      checked_in: { bg: 'bg-green-100', text: 'text-green-800', label: 'Checked In' },
      checked_out: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Checked Out' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Cancelled' },
      no_show: { bg: 'bg-purple-100', text: 'text-purple-800', label: 'No Show' },
    };

    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📋' },
    { id: 'guest', name: 'Guest Details', icon: '👤' },
    { id: 'payment', name: 'Payment', icon: '💳' },
    { id: 'timeline', name: 'Timeline', icon: '📅' },
  ];

  return (
    <div className="space-y-6">
      {/* Booking Header */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xl font-medium text-gray-700">
                  {booking.customer?.name?.charAt(0)?.toUpperCase()}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {booking.bookingNumber}
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  {getStatusBadge(booking.status)}
                  <span className="text-sm text-gray-500">
                    Created {formatDate(booking.createdAt)}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowEditModal(true)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                Edit Booking
              </button>
              <a
                href={`mailto:${booking.customer?.email}`}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                Email Guest
              </a>
              {booking.customer?.phone && (
                <a
                  href={`tel:${booking.customer.phone}`}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Call Guest
                </a>
              )}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="px-6 py-4 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {booking.guests?.total || 0}
              </div>
              <div className="text-sm text-gray-500">Guests</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {booking.dates?.duration || 0}
              </div>
              <div className="text-sm text-gray-500">Days</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(booking.pricing?.totalAmount || 0)}
              </div>
              <div className="text-sm text-gray-500">Total Amount</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(booking.payment?.paidAmount || 0)}
              </div>
              <div className="text-sm text-gray-500">Paid Amount</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Booking Details */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Booking Details</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Package</label>
                    <div className="text-sm text-gray-900">{booking.package?.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{booking.package?.category}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Dates</label>
                    <div className="text-sm text-gray-900">
                      {formatDate(booking.dates?.checkIn)} - {formatDate(booking.dates?.checkOut)}
                    </div>
                    <div className="text-xs text-gray-500">{booking.dates?.duration} days</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Guest Type</label>
                    <div className="text-sm text-gray-900 capitalize">{booking.guests?.guestType}</div>
                  </div>
                </div>
              </div>

              {/* Payment Summary */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Subtotal</span>
                    <span className="text-sm text-gray-900">{formatCurrency(booking.pricing?.subtotal || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Taxes</span>
                    <span className="text-sm text-gray-900">{formatCurrency(booking.pricing?.taxes || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Fees</span>
                    <span className="text-sm text-gray-900">{formatCurrency(booking.pricing?.fees || 0)}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-900">Total</span>
                      <span className="text-sm font-medium text-gray-900">{formatCurrency(booking.pricing?.totalAmount || 0)}</span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Paid</span>
                    <span className="text-sm text-green-600">{formatCurrency(booking.payment?.paidAmount || 0)}</span>
                  </div>
                  {booking.payment?.remainingAmount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Remaining</span>
                      <span className="text-sm text-red-600">{formatCurrency(booking.payment.remainingAmount)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'guest' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Primary Guest */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Primary Guest</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Name</label>
                    <div className="text-sm text-gray-900">{booking.customer?.name}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <div className="text-sm text-gray-900">{booking.customer?.email}</div>
                  </div>
                  {booking.customer?.phone && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <div className="text-sm text-gray-900">{booking.customer.phone}</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Guest Count */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Guest Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Total Guests</label>
                    <div className="text-sm text-gray-900">{booking.guests?.total}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Adults</label>
                    <div className="text-sm text-gray-900">{booking.guests?.adults}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Children</label>
                    <div className="text-sm text-gray-900">{booking.guests?.children || 0}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payment' && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">💳</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Management</h3>
              <p className="text-gray-600 mb-4">
                Detailed payment management interface coming soon.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
                <strong>Coming Soon:</strong> Payment processing, refunds, partial payments, and payment history.
              </div>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📅</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Booking Timeline</h3>
              <p className="text-gray-600 mb-4">
                Complete booking timeline and activity log coming soon.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
                <strong>Coming Soon:</strong> Activity timeline, status changes, communications, and audit trail.
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Special Requests */}
      {booking.specialRequests && Object.keys(booking.specialRequests).length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Special Requests</h3>
          <div className="space-y-2">
            {Object.entries(booking.specialRequests)
              .filter(([_, value]) => value)
              .map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-sm font-medium text-gray-700 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                  <span className="text-sm text-gray-900">{value}</span>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Edit Modal */}
      <BookingEditModal
        booking={booking}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onBookingUpdate={onBookingUpdate}
      />
    </div>
  );
}
