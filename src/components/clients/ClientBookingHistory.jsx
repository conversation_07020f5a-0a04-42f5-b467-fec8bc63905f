'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ClientBookingHistory({ bookings }) {
  const [sortBy, setSortBy] = useState('-createdAt');
  const [filterStatus, setFilterStatus] = useState('');

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      confirmed: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Confirmed' },
      checked_in: { bg: 'bg-green-100', text: 'text-green-800', label: 'Checked In' },
      checked_out: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Completed' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Cancelled' },
    };

    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getPaymentStatusBadge = (paymentStatus) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      paid: { bg: 'bg-green-100', text: 'text-green-800', label: 'Paid' },
      partial: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Partial' },
      failed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' },
      refunded: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Refunded' },
    };

    const config = statusConfig[paymentStatus] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // Filter and sort bookings
  const filteredBookings = bookings
    .filter(booking => !filterStatus || booking.status === filterStatus)
    .sort((a, b) => {
      switch (sortBy) {
        case '-createdAt':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'createdAt':
          return new Date(a.createdAt) - new Date(b.createdAt);
        case '-checkIn':
          return new Date(b.dates.checkIn) - new Date(a.dates.checkIn);
        case 'checkIn':
          return new Date(a.dates.checkIn) - new Date(b.dates.checkIn);
        case '-amount':
          return b.pricing.totalAmount - a.pricing.totalAmount;
        case 'amount':
          return a.pricing.totalAmount - b.pricing.totalAmount;
        default:
          return 0;
      }
    });

  // Calculate summary stats
  const stats = {
    total: bookings.length,
    completed: bookings.filter(b => b.status === 'checked_out').length,
    upcoming: bookings.filter(b => 
      ['pending', 'confirmed'].includes(b.status) && 
      new Date(b.dates.checkIn) > new Date()
    ).length,
    cancelled: bookings.filter(b => b.status === 'cancelled').length,
    totalSpent: bookings.reduce((sum, b) => sum + (b.pricing.totalAmount || 0), 0),
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-500">Total Bookings</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-gray-500">Completed</div>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.upcoming}</div>
          <div className="text-sm text-gray-500">Upcoming</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-red-600">{stats.cancelled}</div>
          <div className="text-sm text-gray-500">Cancelled</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-purple-600">{formatCurrency(stats.totalSpent)}</div>
          <div className="text-sm text-gray-500">Total Spent</div>
        </div>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="checked_in">Checked In</option>
              <option value="checked_out">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="-createdAt">Newest First</option>
              <option value="createdAt">Oldest First</option>
              <option value="-checkIn">Check-in Date (Latest)</option>
              <option value="checkIn">Check-in Date (Earliest)</option>
              <option value="-amount">Highest Amount</option>
              <option value="amount">Lowest Amount</option>
            </select>
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          Showing {filteredBookings.length} of {bookings.length} bookings
        </div>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-500">
            {filterStatus ? 'No bookings found with the selected status.' : 'No bookings found.'}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map((booking) => (
            <div key={booking._id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Link
                      href={`/admin/bookings/${booking._id}`}
                      className="text-lg font-medium text-blue-600 hover:text-blue-800"
                    >
                      {booking.bookingNumber}
                    </Link>
                    {getStatusBadge(booking.status)}
                    {getPaymentStatusBadge(booking.payment?.status || 'pending')}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900">{booking.package?.name}</div>
                      <div className="text-gray-500 capitalize">{booking.package?.category}</div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900">
                        {formatDate(booking.dates.checkIn)} - {formatDate(booking.dates.checkOut)}
                      </div>
                      <div className="text-gray-500">{booking.dates.duration} days</div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900">
                        {booking.guests.total} guests ({booking.guests.guestType})
                      </div>
                      <div className="text-gray-500">
                        {booking.guests.adults} adults
                        {booking.guests.children > 0 && `, ${booking.guests.children} children`}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right ml-6">
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(booking.pricing.totalAmount)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Paid: {formatCurrency(booking.payment?.paidAmount || 0)}
                  </div>
                  {booking.payment?.remainingAmount > 0 && (
                    <div className="text-sm text-red-600">
                      Due: {formatCurrency(booking.payment.remainingAmount)}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Special Requests */}
              {booking.specialRequests && Object.keys(booking.specialRequests).length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Special Requests: </span>
                    <span className="text-gray-600">
                      {Object.entries(booking.specialRequests)
                        .filter(([_, value]) => value)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(', ')}
                    </span>
                  </div>
                </div>
              )}
              
              {/* Booking Date */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  Booked on {formatDate(booking.createdAt)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
