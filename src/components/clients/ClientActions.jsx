'use client';

import { useState } from 'react';

export default function ClientActions({ 
  client, 
  onClientUpdate, 
  onClientDelete, 
  onClientSelect 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = async (action, data = {}) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/clients/${client._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data }),
      });

      const result = await response.json();
      
      if (result.success) {
        onClientUpdate(result.data);
        setShowDropdown(false);
      } else {
        alert(result.message || 'Action failed');
      }
    } catch (error) {
      alert('Failed to perform action');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${client.name}? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/clients/${client._id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        onClientDelete(client._id);
      } else {
        alert(result.message || 'Failed to delete client');
      }
    } catch (error) {
      alert('Failed to delete client');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = (newRole) => {
    if (confirm(`Change ${client.name}'s role to ${newRole}?`)) {
      handleAction('change_role', { role: newRole });
    }
  };

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Quick View Button */}
        <button
          onClick={() => onClientSelect(client)}
          className="text-blue-600 hover:text-blue-900 text-sm"
          disabled={isLoading}
        >
          View
        </button>

        {/* Quick Actions Dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
              <div className="py-1">
                {/* Account Status Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                  Account Status
                </div>
                
                {client.isBlocked ? (
                  <button
                    onClick={() => handleAction('unblock')}
                    className="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50"
                  >
                    Unblock Account
                  </button>
                ) : (
                  <button
                    onClick={() => handleAction('block')}
                    className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                  >
                    Block Account
                  </button>
                )}

                {client.isActive ? (
                  <button
                    onClick={() => handleAction('deactivate')}
                    className="block w-full text-left px-4 py-2 text-sm text-orange-700 hover:bg-orange-50"
                  >
                    Deactivate
                  </button>
                ) : (
                  <button
                    onClick={() => handleAction('activate')}
                    className="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50"
                  >
                    Activate
                  </button>
                )}

                {/* Role Management */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Change Role
                </div>
                
                {['guest', 'user', 'manager', 'admin'].map((role) => (
                  <button
                    key={role}
                    onClick={() => handleRoleChange(role)}
                    disabled={client.role === role}
                    className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                      client.role === role 
                        ? 'text-gray-400 cursor-not-allowed' 
                        : 'text-gray-700'
                    }`}
                  >
                    {role === client.role && '✓ '}
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </button>
                ))}

                {/* Communication */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Communication
                </div>
                
                <a
                  href={`mailto:${client.email}`}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Send Email
                </a>
                
                {client.phone && (
                  <a
                    href={`tel:${client.phone}`}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Call Phone
                  </a>
                )}

                {/* Dangerous Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Danger Zone
                </div>
                
                <button
                  onClick={handleDelete}
                  className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                >
                  Delete Client
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
}
