'use client';

import { useState } from 'react';

export default function ClientPaymentHistory({ payments }) {
  const [sortBy, setSortBy] = useState('-createdAt');
  const [filterStatus, setFilterStatus] = useState('');

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      succeeded: { bg: 'bg-green-100', text: 'text-green-800', label: 'Succeeded' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      failed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' },
      refunded: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Refunded' },
      partially_refunded: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Partial Refund' },
      disputed: { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Disputed' },
    };

    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // Filter and sort payments
  const filteredPayments = payments
    .filter(payment => !filterStatus || payment.status === filterStatus)
    .sort((a, b) => {
      switch (sortBy) {
        case '-createdAt':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'createdAt':
          return new Date(a.createdAt) - new Date(b.createdAt);
        case '-amount':
          return b.amount - a.amount;
        case 'amount':
          return a.amount - b.amount;
        default:
          return 0;
      }
    });

  // Calculate summary stats
  const stats = {
    total: payments.length,
    succeeded: payments.filter(p => p.status === 'succeeded').length,
    failed: payments.filter(p => p.status === 'failed').length,
    refunded: payments.filter(p => ['refunded', 'partially_refunded'].includes(p.status)).length,
    totalAmount: payments
      .filter(p => p.status === 'succeeded')
      .reduce((sum, p) => sum + p.amount, 0),
    totalRefunded: payments
      .filter(p => ['refunded', 'partially_refunded'].includes(p.status))
      .reduce((sum, p) => sum + (p.refund?.amount || 0), 0),
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-500">Total Payments</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-600">{stats.succeeded}</div>
          <div className="text-sm text-gray-500">Successful</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-gray-500">Failed</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-orange-600">{stats.refunded}</div>
          <div className="text-sm text-gray-500">Refunded</div>
        </div>
      </div>

      {/* Amount Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalAmount)}</div>
          <div className="text-sm text-gray-500">Total Paid</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold text-purple-600">{formatCurrency(stats.totalRefunded)}</div>
          <div className="text-sm text-gray-500">Total Refunded</div>
        </div>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="succeeded">Succeeded</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
              <option value="partially_refunded">Partially Refunded</option>
              <option value="disputed">Disputed</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="-createdAt">Newest First</option>
              <option value="createdAt">Oldest First</option>
              <option value="-amount">Highest Amount</option>
              <option value="amount">Lowest Amount</option>
            </select>
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          Showing {filteredPayments.length} of {payments.length} payments
        </div>
      </div>

      {/* Payments List */}
      {filteredPayments.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-500">
            {filterStatus ? 'No payments found with the selected status.' : 'No payments found.'}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredPayments.map((payment) => (
            <div key={payment._id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="text-lg font-medium text-gray-900">
                      {payment.paymentId}
                    </div>
                    {getStatusBadge(payment.status)}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900">Booking</div>
                      <div className="text-gray-500">{payment.booking?.bookingNumber || 'N/A'}</div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900">Payment Method</div>
                      <div className="text-gray-500 capitalize">
                        {payment.paymentMethod?.details?.brand} •••• {payment.paymentMethod?.details?.last4}
                      </div>
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900">Date</div>
                      <div className="text-gray-500">{formatDate(payment.createdAt)}</div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right ml-6">
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(payment.amount)}
                  </div>
                  {payment.fees?.totalFees > 0 && (
                    <div className="text-sm text-gray-500">
                      Fee: {formatCurrency(payment.fees.totalFees)}
                    </div>
                  )}
                  {payment.netAmount && (
                    <div className="text-sm text-green-600">
                      Net: {formatCurrency(payment.netAmount)}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Refund Information */}
              {payment.refund && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Refund: </span>
                    <span className="text-red-600">{formatCurrency(payment.refund.amount)}</span>
                    {payment.refund.reason && (
                      <span className="text-gray-600 ml-2">({payment.refund.reason})</span>
                    )}
                    {payment.refund.processedAt && (
                      <span className="text-gray-500 ml-2">
                        on {formatDate(payment.refund.processedAt)}
                      </span>
                    )}
                  </div>
                </div>
              )}
              
              {/* Failure Information */}
              {payment.failure && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="text-sm">
                    <span className="font-medium text-gray-700">Failure: </span>
                    <span className="text-red-600">{payment.failure.message}</span>
                    {payment.failure.code && (
                      <span className="text-gray-500 ml-2">({payment.failure.code})</span>
                    )}
                  </div>
                </div>
              )}
              
              {/* Processing Date */}
              {payment.processedAt && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="text-xs text-gray-500">
                    Processed on {formatDate(payment.processedAt)}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
