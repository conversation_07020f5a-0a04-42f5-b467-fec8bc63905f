# TextEditor Component

A React component that provides rich text editing capabilities using native contentEditable with custom toolbar.

## Features

- **Rich Text Editing**: Custom contentEditable-based editor with comprehensive toolbar
- **Real-time Preview**: Live display of formatted content
- **Font Family Support**: Arial, Times New Roman, Helvetica, Georgia, Verdana
- **Text Formatting**: Bold, italic, underline, color formatting, font sizes, line heights
- **Link Support**: Email and URL link creation with visual feedback
- **Responsive Design**: Tailwind CSS styling that adapts to different screen sizes

## Usage

```jsx
import TextEditor from '@/components/TextEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <TextEditor
      value={content}
      onChange={setContent}
      placeholder="Start typing..."
      className="w-full"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | string | `''` | The HTML content of the editor |
| `onChange` | function | - | Callback fired when content changes |
| `placeholder` | string | `'Start typing...'` | Placeholder text for empty editor |
| `className` | string | `''` | Additional CSS classes |
| `style` | object | `{}` | Inline styles |
| `disabled` | boolean | `false` | Whether the editor is disabled |

## Toolbar Features

### Text Formatting
- **Bold**: Make text bold
- **Italic**: Make text italic

### Font Family
- Arial
- Times New Roman
- Helvetica
- Georgia
- Verdana

### Color Picker
- Full color palette for text color

## Technical Details

### Dependencies
- No external dependencies - uses native browser APIs
- React hooks for state management
- Tailwind CSS for styling

### Implementation Details
The component uses native `contentEditable` with `document.execCommand` for formatting operations.

### Font Family Implementation
Font families are applied using inline styles and CSS font-family properties.

### Content Cleaning
The component automatically cleans up empty paragraphs and maintains clean HTML output.

## Example

See `/test-text-editor` page for a complete working example with sample content and controls.

## Styling

The component uses Tailwind CSS classes and follows the existing codebase patterns:
- Gray borders and backgrounds
- Hover states
- Focus states with blue accent
- Responsive design
- Loading states with skeleton animations

## Browser Compatibility

Works in all modern browsers that support:
- ES6 modules
- Dynamic imports
- CSS custom properties
- Flexbox
