'use client';

import { useState, useEffect } from 'react';

export default function PackagePricing({ package: pkg, onPackageUpdate, onHasChanges }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    pricing: pkg.pricing || 0,
    bookingRules: {
      cancellationPolicy: pkg.bookingRules?.cancellationPolicy || 'moderate',
      refundPolicy: pkg.bookingRules?.refundPolicy || '',
      modificationPolicy: pkg.bookingRules?.modificationPolicy || '',
      depositRequired: pkg.bookingRules?.depositRequired || 0,
    },
  });

  const [originalData, setOriginalData] = useState(formData);

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    onHasChanges(hasChanges);
  }, [formData, originalData, onHasChanges]);

  const handlePricingChange = (value) => {
    setFormData(prev => ({
      ...prev,
      pricing: Number(value),
    }));
  };

  const handleBookingRulesChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      bookingRules: {
        ...prev.bookingRules,
        [field]: field === 'depositRequired' ? Number(value) : value,
      },
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageUpdate(data.data);
        setOriginalData(formData);
      } else {
        setError(data.message || 'Failed to update pricing');
      }
    } catch (err) {
      setError('Failed to update pricing');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData(originalData);
    setError(null);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const calculatePricePerNight = (price) => {
    const nights = pkg.duration?.nights || 1;
    return price / nights;
  };

  return (
    <div className="space-y-6">
      {/* Pricing Overview */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-4">Package Pricing</h3>
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-900">
            {formatCurrency(formData.pricing)}
          </div>
          <div className="text-sm text-blue-700 mt-1">Base Package Price</div>
          <div className="text-xs text-blue-600 mt-1">
            {formatCurrency(calculatePricePerNight(formData.pricing))}/night
          </div>
        </div>
      </div>

      {/* Pricing Details */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Price Settings</h3>
        <div className="border border-gray-200 rounded-lg p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Package Price *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.pricing}
                onChange={(e) => handlePricingChange(e.target.value)}
                className="pl-8 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              This is the base price for the package. It will apply to all guests regardless of group size.
            </p>
          </div>
        </div>
      </div>

      {/* Booking Rules */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Booking Rules</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cancellation Policy
              </label>
              <select
                value={formData.bookingRules.cancellationPolicy}
                onChange={(e) => handleBookingRulesChange('cancellationPolicy', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="flexible">Flexible</option>
                <option value="moderate">Moderate</option>
                <option value="strict">Strict</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Deposit Required (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.bookingRules.depositRequired}
                onChange={(e) => handleBookingRulesChange('depositRequired', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Refund Policy
            </label>
            <textarea
              rows={3}
              value={formData.bookingRules.refundPolicy}
              onChange={(e) => handleBookingRulesChange('refundPolicy', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe your refund policy..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Modification Policy
            </label>
            <textarea
              rows={3}
              value={formData.bookingRules.modificationPolicy}
              onChange={(e) => handleBookingRulesChange('modificationPolicy', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe your modification policy..."
            />
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={handleReset}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={isLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Pricing'
          )}
        </button>
      </div>
    </div>
  );
}
