'use client';

import { useState } from 'react';

export default function CreatePackageModal({ onPackageCreate, onClose }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Basic Information
    name: '',
    shortDescription: '',
    description: '',
    category: 'accommodation',
    
    // Duration & Capacity
    duration: {
      nights: 1,
      days: 2,
    },
    maxGuests: 2,
    
    // Pricing
    pricing: 0,
    
    // Availability
    availability: {
      isActive: true,
      advanceBookingDays: 1,
      maxAdvanceBookingDays: 365,
    },
    
    // Features
    featured: false,
    priority: 0,
    
    // Basic inclusions
    inclusions: [
      { item: '', description: '' }
    ],
    exclusions: [
      { item: '', description: '' }
    ],
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value),
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value),
      }));
    }
  };

  const handlePricingChange = (value) => {
    setFormData(prev => ({
      ...prev,
      pricing: Number(value),
    }));
  };

  const handleInclusionChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addInclusion = () => {
    setFormData(prev => ({
      ...prev,
      inclusions: [...prev.inclusions, { item: '', description: '' }],
    }));
  };

  const removeInclusion = (index) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Clean up empty inclusions
      const cleanedData = {
        ...formData,
        inclusions: formData.inclusions.filter(item => item.item.trim()),
        exclusions: formData.exclusions.filter(item => item.item.trim()),
      };

      console.log('Sending package data:', cleanedData);

      const response = await fetch('/api/packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageCreate(data.data);
      } else {
        console.error('API Error:', data);
        setError(data.message || `Failed to create package (${response.status})`);
      }
    } catch (err) {
      setError('Failed to create package');
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.name && formData.description && formData.category;
      case 2:
        return formData.pricing > 0;
      case 3:
        return true; // Optional step
      default:
        return false;
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Create New Package</h3>
              <div className="flex items-center mt-2">
                {[1, 2, 3].map((step) => (
                  <div key={step} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step === currentStep 
                        ? 'bg-blue-600 text-white' 
                        : step < currentStep 
                          ? 'bg-green-600 text-white' 
                          : 'bg-gray-200 text-gray-600'
                    }`}>
                      {step < currentStep ? '✓' : step}
                    </div>
                    {step < 3 && (
                      <div className={`w-12 h-1 mx-2 ${
                        step < currentStep ? 'bg-green-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {currentStep === 1 && 'Basic Information'}
                {currentStep === 2 && 'Pricing & Duration'}
                {currentStep === 3 && 'Features & Inclusions'}
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Package Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Romantic Getaway Package"
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    Category *
                  </label>
                  <select
                    id="category"
                    name="category"
                    required
                    value={formData.category}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="accommodation">🏠 Accommodation</option>
                    <option value="experience">🎯 Experience</option>
                    <option value="combo">📦 Combo</option>
                    <option value="seasonal">🌟 Seasonal</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700">
                    Short Description
                  </label>
                  <input
                    type="text"
                    id="shortDescription"
                    name="shortDescription"
                    value={formData.shortDescription}
                    onChange={handleInputChange}
                    maxLength={200}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Brief description for listings"
                  />
                  <p className="text-xs text-gray-500 mt-1">{formData.shortDescription.length}/200 characters</p>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Full Description *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    required
                    rows={4}
                    value={formData.description}
                    onChange={handleInputChange}
                    maxLength={2000}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Detailed description of the package..."
                  />
                  <p className="text-xs text-gray-500 mt-1">{formData.description.length}/2000 characters</p>
                </div>
              </div>
            )}

            {/* Step 2: Pricing & Duration */}
            {currentStep === 2 && (
              <div className="space-y-6">
                {/* Duration */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Duration</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="duration.nights" className="block text-sm font-medium text-gray-700">
                        Nights *
                      </label>
                      <input
                        type="number"
                        id="duration.nights"
                        name="duration.nights"
                        required
                        min="1"
                        value={formData.duration.nights}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="duration.days" className="block text-sm font-medium text-gray-700">
                        Days *
                      </label>
                      <input
                        type="number"
                        id="duration.days"
                        name="duration.days"
                        required
                        min="1"
                        value={formData.duration.days}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Max Guests */}
                <div>
                  <label htmlFor="maxGuests" className="block text-sm font-medium text-gray-700">
                    Maximum Guests *
                  </label>
                  <input
                    type="number"
                    id="maxGuests"
                    name="maxGuests"
                    required
                    min="1"
                    max="20"
                    value={formData.maxGuests}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Pricing */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Package Price *</h4>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.pricing}
                      onChange={(e) => handlePricingChange(e.target.value)}
                      className="pl-8 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Base price for the package. This will be the standard rate for all guests.
                  </p>
                </div>
              </div>
            )}

            {/* Step 3: Features & Inclusions */}
            {currentStep === 3 && (
              <div className="space-y-6">
                {/* Package Features */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Package Features</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="featured"
                        name="featured"
                        checked={formData.featured}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                        Featured package (appears prominently on website)
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="availability.isActive"
                        name="availability.isActive"
                        checked={formData.availability.isActive}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="availability.isActive" className="ml-2 block text-sm text-gray-900">
                        Package is active and bookable
                      </label>
                    </div>
                  </div>
                </div>

                {/* Inclusions */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">What's Included</h4>
                  <div className="space-y-3">
                    {formData.inclusions.map((inclusion, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={inclusion.item}
                          onChange={(e) => handleInclusionChange(index, 'item', e.target.value)}
                          className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="e.g., Accommodation, Meals, Activities"
                        />
                        {formData.inclusions.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeInclusion(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={addInclusion}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      + Add inclusion
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <div>
                {currentStep > 1 && (
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    Previous
                  </button>
                )}
              </div>
              
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                
                {currentStep < 3 ? (
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={!isStepValid()}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isLoading || !formData.name || !formData.description}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Creating...</span>
                      </div>
                    ) : (
                      'Create Package'
                    )}
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
