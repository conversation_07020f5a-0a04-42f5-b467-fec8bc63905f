'use client';

import { useState } from 'react';
import PackageBasicInfo from './PackageBasicInfo';
import PackagePricing from './PackagePricing';
import PackageAvailability from './PackageAvailability';
import PackageMedia from './PackageMedia';
import PackageInclusions from './PackageInclusions';
import PackageAnalytics from './PackageAnalytics';

export default function PackageEditor({ 
  package: pkg, 
  isLoading, 
  onPackageUpdate, 
  onPackageDelete, 
  onBack 
}) {
  const [activeTab, setActiveTab] = useState('basic');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  if (isLoading || !pkg) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (pkg) => {
    if (!pkg.availability?.isActive) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>;
    }
    return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>;
  };

  const getCategoryBadge = (category) => {
    const categoryConfig = {
      individual: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '👤' },
      couples: { bg: 'bg-pink-100', text: 'text-pink-800', icon: '💑' },
      families: { bg: 'bg-green-100', text: 'text-green-800', icon: '👨‍👩‍👧‍👦' },
      // Legacy categories (for backward compatibility)
      accommodation: { bg: 'bg-blue-100', text: 'text-blue-800', icon: '🏠' },
      experience: { bg: 'bg-green-100', text: 'text-green-800', icon: '🎯' },
      combo: { bg: 'bg-purple-100', text: 'text-purple-800', icon: '📦' },
      seasonal: { bg: 'bg-orange-100', text: 'text-orange-800', icon: '🌟' },
    };

    const config = categoryConfig[category] || categoryConfig.individual;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.icon} {category.charAt(0).toUpperCase() + category.slice(1)}
      </span>
    );
  };

  const handlePackageUpdate = (updatedPackage) => {
    onPackageUpdate(updatedPackage);
    setHasChanges(false);
  };

  const tabs = [
    { id: 'basic', name: 'Basic Info', icon: '📝' },
    { id: 'pricing', name: 'Pricing', icon: '💰' },
    { id: 'availability', name: 'Availability', icon: '📅' },
    { id: 'media', name: 'Media', icon: '🖼️' },
    { id: 'inclusions', name: 'Inclusions', icon: '✅' },
    { id: 'analytics', name: 'Analytics', icon: '📊' },
  ];

  return (
    <div className="space-y-6">
      {/* Package Header */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 rounded-lg bg-gray-300 flex items-center justify-center overflow-hidden">
                {pkg.primaryImage ? (
                  <img
                    src={pkg.primaryImage.url}
                    alt={pkg.primaryImage.alt || pkg.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{pkg.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  {getStatusBadge(pkg)}
                  {getCategoryBadge(pkg.category)}
                  {pkg.featured && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      ⭐ Featured
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex space-x-3">
              {hasChanges && (
                <span className="text-sm text-orange-600 bg-orange-50 px-3 py-1 rounded-md">
                  Unsaved changes
                </span>
              )}
              <a
                href={`/packages/${pkg.slug}`}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Preview
              </a>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="px-6 py-4 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {pkg.stats?.totalBookings || 0}
              </div>
              <div className="text-sm text-gray-500">Total Bookings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(pkg.stats?.totalRevenue || 0)}
              </div>
              <div className="text-sm text-gray-500">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(pkg.pricing || 0)}
              </div>
              <div className="text-sm text-gray-500">Package Price</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {pkg.duration?.nights || 0}N/{pkg.duration?.days || 0}D
              </div>
              <div className="text-sm text-gray-500">Duration</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'basic' && (
            <PackageBasicInfo
              package={pkg}
              onPackageUpdate={handlePackageUpdate}
              onHasChanges={setHasChanges}
            />
          )}

          {activeTab === 'pricing' && (
            <PackagePricing
              package={pkg}
              onPackageUpdate={handlePackageUpdate}
              onHasChanges={setHasChanges}
            />
          )}

          {activeTab === 'availability' && (
            <PackageAvailability
              package={pkg}
              onPackageUpdate={handlePackageUpdate}
              onHasChanges={setHasChanges}
            />
          )}

          {activeTab === 'media' && (
            <PackageMedia
              package={pkg}
              onPackageUpdate={handlePackageUpdate}
              onHasChanges={setHasChanges}
            />
          )}

          {activeTab === 'inclusions' && (
            <PackageInclusions
              package={pkg}
              onPackageUpdate={handlePackageUpdate}
              onHasChanges={setHasChanges}
            />
          )}

          {activeTab === 'analytics' && (
            <PackageAnalytics
              package={pkg}
            />
          )}
        </div>
      </div>
    </div>
  );
}
