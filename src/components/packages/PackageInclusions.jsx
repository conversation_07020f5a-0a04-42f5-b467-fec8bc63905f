'use client';

import { useState, useEffect } from 'react';

export default function PackageInclusions({ package: pkg, onPackageUpdate, onHasChanges }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    inclusions: pkg.inclusions || [],
    exclusions: pkg.exclusions || [],
    location: {
      name: pkg.location?.name || '',
      address: pkg.location?.address || '',
      meetingPoint: pkg.location?.meetingPoint || '',
      coordinates: {
        latitude: pkg.location?.coordinates?.latitude || '',
        longitude: pkg.location?.coordinates?.longitude || '',
      },
      transportation: {
        included: pkg.location?.transportation?.included || false,
        details: pkg.location?.transportation?.details || '',
      },
    },
  });

  const [originalData, setOriginalData] = useState(formData);
  const [newInclusion, setNewInclusion] = useState({ item: '', description: '' });
  const [newExclusion, setNewExclusion] = useState({ item: '', description: '' });

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    onHasChanges(hasChanges);
  }, [formData, originalData, onHasChanges]);

  const handleLocationChange = (field, value) => {
    if (field.includes('.')) {
      const [section, subfield] = field.split('.');
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          [section]: {
            ...prev.location[section],
            [subfield]: value,
          },
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          [field]: value,
        },
      }));
    }
  };

  const addInclusion = () => {
    if (!newInclusion.item.trim()) {
      setError('Please enter an inclusion item');
      return;
    }

    setFormData(prev => ({
      ...prev,
      inclusions: [
        ...prev.inclusions,
        {
          item: newInclusion.item.trim(),
          description: newInclusion.description.trim(),
        },
      ],
    }));

    setNewInclusion({ item: '', description: '' });
    setError(null);
  };

  const removeInclusion = (index) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.filter((_, i) => i !== index),
    }));
  };

  const updateInclusion = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      inclusions: prev.inclusions.map((inclusion, i) =>
        i === index ? { ...inclusion, [field]: value } : inclusion
      ),
    }));
  };

  const addExclusion = () => {
    if (!newExclusion.item.trim()) {
      setError('Please enter an exclusion item');
      return;
    }

    setFormData(prev => ({
      ...prev,
      exclusions: [
        ...prev.exclusions,
        {
          item: newExclusion.item.trim(),
          description: newExclusion.description.trim(),
        },
      ],
    }));

    setNewExclusion({ item: '', description: '' });
    setError(null);
  };

  const removeExclusion = (index) => {
    setFormData(prev => ({
      ...prev,
      exclusions: prev.exclusions.filter((_, i) => i !== index),
    }));
  };

  const updateExclusion = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      exclusions: prev.exclusions.map((exclusion, i) =>
        i === index ? { ...exclusion, [field]: value } : exclusion
      ),
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageUpdate(data.data);
        setOriginalData(formData);
      } else {
        setError(data.message || 'Failed to update inclusions');
      }
    } catch (err) {
      setError('Failed to update inclusions');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData(originalData);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Package Inclusions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">What's Included</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          {/* Add New Inclusion */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Inclusion</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Item *</label>
                <input
                  type="text"
                  value={newInclusion.item}
                  onChange={(e) => setNewInclusion(prev => ({ ...prev, item: e.target.value }))}
                  placeholder="e.g., Accommodation"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={newInclusion.description}
                  onChange={(e) => setNewInclusion(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="e.g., 2 nights in luxury lodge"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <div className="flex items-end">
                <button
                  type="button"
                  onClick={addInclusion}
                  className="bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  Add Inclusion
                </button>
              </div>
            </div>
          </div>

          {/* Existing Inclusions */}
          {formData.inclusions.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Current Inclusions</h4>
              {formData.inclusions.map((inclusion, index) => (
                <div key={index} className="p-4 bg-green-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Item</label>
                      <input
                        type="text"
                        value={inclusion.item}
                        onChange={(e) => updateInclusion(index, 'item', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                      <input
                        type="text"
                        value={inclusion.description || ''}
                        onChange={(e) => updateInclusion(index, 'description', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                  </div>
                  <div className="mt-3 flex justify-end">
                    <button
                      type="button"
                      onClick={() => removeInclusion(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500 text-center py-4">No inclusions added yet</p>
          )}
        </div>
      </div>

      {/* Package Exclusions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">What's Not Included</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          {/* Add New Exclusion */}
          <div className="mb-6 p-4 bg-red-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Exclusion</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Item *</label>
                <input
                  type="text"
                  value={newExclusion.item}
                  onChange={(e) => setNewExclusion(prev => ({ ...prev, item: e.target.value }))}
                  placeholder="e.g., Airfare"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={newExclusion.description}
                  onChange={(e) => setNewExclusion(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="e.g., International flights"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                />
              </div>
              <div className="flex items-end">
                <button
                  type="button"
                  onClick={addExclusion}
                  className="bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Add Exclusion
                </button>
              </div>
            </div>
          </div>

          {/* Existing Exclusions */}
          {formData.exclusions.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Current Exclusions</h4>
              {formData.exclusions.map((exclusion, index) => (
                <div key={index} className="p-4 bg-red-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Item</label>
                      <input
                        type="text"
                        value={exclusion.item}
                        onChange={(e) => updateExclusion(index, 'item', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                      <input
                        type="text"
                        value={exclusion.description || ''}
                        onChange={(e) => updateExclusion(index, 'description', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      />
                    </div>
                  </div>
                  <div className="mt-3 flex justify-end">
                    <button
                      type="button"
                      onClick={() => removeExclusion(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500 text-center py-4">No exclusions added yet</p>
          )}
        </div>
      </div>

      {/* Location & Transportation */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Location & Transportation</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location Name
              </label>
              <input
                type="text"
                value={formData.location.name}
                onChange={(e) => handleLocationChange('name', e.target.value)}
                placeholder="e.g., Elephant Island Lodge"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meeting Point
              </label>
              <input
                type="text"
                value={formData.location.meetingPoint}
                onChange={(e) => handleLocationChange('meetingPoint', e.target.value)}
                placeholder="e.g., Lodge Reception"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <textarea
                rows={3}
                value={formData.location.address}
                onChange={(e) => handleLocationChange('address', e.target.value)}
                placeholder="Full address of the location"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude
              </label>
              <input
                type="number"
                step="any"
                value={formData.location.coordinates.latitude}
                onChange={(e) => handleLocationChange('coordinates.latitude', e.target.value)}
                placeholder="e.g., -1.2921"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude
              </label>
              <input
                type="number"
                step="any"
                value={formData.location.coordinates.longitude}
                onChange={(e) => handleLocationChange('coordinates.longitude', e.target.value)}
                placeholder="e.g., 36.8219"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Transportation */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Transportation</h4>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="transportationIncluded"
                  checked={formData.location.transportation.included}
                  onChange={(e) => handleLocationChange('transportation.included', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="transportationIncluded" className="ml-2 block text-sm text-gray-900">
                  Transportation is included in this package
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Transportation Details
                </label>
                <textarea
                  rows={3}
                  value={formData.location.transportation.details}
                  onChange={(e) => handleLocationChange('transportation.details', e.target.value)}
                  placeholder="Describe transportation arrangements, pickup points, etc."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={handleReset}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={isLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Inclusions'
          )}
        </button>
      </div>
    </div>
  );
}
