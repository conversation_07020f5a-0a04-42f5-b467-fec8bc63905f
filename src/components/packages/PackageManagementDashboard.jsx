'use client';

import { useState, useEffect } from 'react';
import PackageList from './PackageList';
import PackageEditor from './PackageEditor';
import PackageFilters from './PackageFilters';
import PackageStats from './PackageStats';
// Remove server-side import that causes client-side errors

export default function PackageManagementDashboard() {
  const [packages, setPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('list'); // 'list' | 'editor' (create removed)
  
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    status: '',
    featured: '',
    sort: '-createdAt',
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    fetchPackages();
  }, [filters, pagination.page]);

  const fetchPackages = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch only the predefined packages
      const params = new URLSearchParams({
        category: 'individual,couples,families', // Only fetch predefined package types
        active: 'all', // Include both active and inactive
        limit: '10', // We only have 3 packages
        sort: 'priority', // Sort by priority
      });

      const response = await fetch(`/api/packages?${params}`);
      const data = await response.json();

      if (data.success) {
        // Filter to ensure we only show the 3 predefined packages
        const predefinedPackages = data.data.filter(pkg =>
          ['individual', 'couples', 'families'].includes(pkg.category)
        );
        setPackages(predefinedPackages);
        setPagination(prev => ({
          ...prev,
          total: predefinedPackages.length,
          pages: 1, // Always 1 page since we have only 3 packages
        }));
      } else {
        setError(data.message || 'Failed to fetch packages');
      }
    } catch (err) {
      setError('Failed to load packages');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePackageSelect = async (packageData) => {
    setSelectedPackage(packageData);
    setView('editor');
  };

  const handlePackageUpdate = (updatedPackage) => {
    // Update package in the list
    setPackages(prev => 
      prev.map(pkg => 
        pkg._id === updatedPackage._id ? updatedPackage : pkg
      )
    );
    
    // Update selected package if it's the same one
    if (selectedPackage && selectedPackage._id === updatedPackage._id) {
      setSelectedPackage(updatedPackage);
    }
  };

  // Package creation is no longer allowed - only editing of predefined packages

  const handlePackageDelete = (deletedPackageId) => {
    setPackages(prev => prev.filter(pkg => pkg._id !== deletedPackageId));
    if (selectedPackage && selectedPackage._id === deletedPackageId) {
      setSelectedPackage(null);
      setView('list');
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleBackToList = () => {
    setSelectedPackage(null);
    setView('list');
  };

  // Create new functionality removed - only predefined packages can be edited

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {view === 'editor' ? 'Package Editor' : 'Package Management'}
          </h2>
          <p className="text-gray-600">
            {view === 'editor'
              ? `Editing: ${selectedPackage?.name}`
              : 'Manage the 3 predefined package types: Individual, Couples, and Families'
            }
          </p>
        </div>
        
        <div className="flex space-x-3">
          {view === 'editor' && (
            <button
              onClick={handleBackToList}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ← Back to List
            </button>
          )}
          
          {view === 'list' && (
            <div className="text-sm text-gray-500 px-4 py-2">
              3 Predefined Package Types
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
          <button
            onClick={fetchPackages}
            className="ml-4 underline hover:no-underline"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Content */}
      {view === 'list' ? (
        <>
          {/* Package Statistics */}
          <PackageStats packages={packages} />

          {/* Filters */}
          <PackageFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onRefresh={fetchPackages}
          />

          {/* Package List */}
          <PackageList
            packages={packages}
            isLoading={isLoading}
            pagination={pagination}
            onPackageSelect={handlePackageSelect}
            onPackageUpdate={handlePackageUpdate}
            onPackageDelete={handlePackageDelete}
            onPageChange={handlePageChange}
            onRefresh={fetchPackages}
          />
        </>
      ) : (
        /* Package Editor */
        <PackageEditor
          package={selectedPackage}
          isLoading={isLoading}
          onPackageUpdate={handlePackageUpdate}
          onPackageDelete={handlePackageDelete}
          onBack={handleBackToList}
        />
      )}

      {/* Create Package Modal removed - only predefined packages can be edited */}
    </div>
  );
}
