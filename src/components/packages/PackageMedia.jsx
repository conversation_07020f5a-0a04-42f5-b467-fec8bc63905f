'use client';

import { useState, useEffect, useRef } from 'react';

export default function PackageMedia({ package: pkg, onPackageUpdate, onHasChanges }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [formData, setFormData] = useState({
    images: pkg.images || [],
  });

  const [originalData, setOriginalData] = useState(formData);
  const fileInputRef = useRef(null);

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    onHasChanges(hasChanges);
  }, [formData, originalData, onHasChanges]);

  const handleFileSelect = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    setIsLoading(true);
    setError(null);
    setUploadProgress(0);

    try {
      const uploadedImages = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Validate file type
        if (!file.type.startsWith('image/')) {
          setError(`File ${file.name} is not an image`);
          continue;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          setError(`File ${file.name} is too large (max 5MB)`);
          continue;
        }

        // Create FormData for upload
        const uploadFormData = new FormData();
        uploadFormData.append('file', file);
        uploadFormData.append('type', 'package-image');

        try {
          // Upload to your file upload endpoint
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: uploadFormData,
          });

          if (!response.ok) {
            throw new Error(`Failed to upload ${file.name}`);
          }

          const uploadResult = await response.json();

          if (uploadResult.success) {
            uploadedImages.push({
              url: uploadResult.url,
              alt: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
              caption: '',
              isPrimary: formData.images.length === 0 && uploadedImages.length === 0, // First image is primary
            });
          }
        } catch (uploadError) {
          console.error(`Upload error for ${file.name}:`, uploadError);
          setError(`Failed to upload ${file.name}: ${uploadError.message}`);
        }

        // Update progress
        setUploadProgress(((i + 1) / files.length) * 100);
      }

      if (uploadedImages.length > 0) {
        setFormData(prev => ({
          ...prev,
          images: [...prev.images, ...uploadedImages],
        }));
      }

    } catch (err) {
      setError('Failed to upload images');
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const updateImage = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((image, i) =>
        i === index ? { ...image, [field]: value } : image
      ),
    }));
  };

  const setPrimaryImage = (index) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((image, i) => ({
        ...image,
        isPrimary: i === index,
      })),
    }));
  };

  const removeImage = (index) => {
    setFormData(prev => {
      const newImages = prev.images.filter((_, i) => i !== index);

      // If we removed the primary image, make the first remaining image primary
      if (newImages.length > 0 && !newImages.some(img => img.isPrimary)) {
        newImages[0].isPrimary = true;
      }

      return {
        ...prev,
        images: newImages,
      };
    });
  };

  const moveImage = (fromIndex, toIndex) => {
    setFormData(prev => {
      const newImages = [...prev.images];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);

      return {
        ...prev,
        images: newImages,
      };
    });
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageUpdate(data.data);
        setOriginalData(formData);
      } else {
        setError(data.message || 'Failed to update media');
      }
    } catch (err) {
      setError('Failed to update media');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData(originalData);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Images</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <div className="space-y-4">
              <div className="text-4xl">📸</div>
              <div>
                <h4 className="text-lg font-medium text-gray-900">Upload Package Images</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Drag and drop images here, or click to select files
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Supports: JPG, PNG, GIF (max 5MB each)
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />

              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Uploading...' : 'Select Images'}
              </button>

              {isLoading && uploadProgress > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Gallery */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Image Gallery</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          {formData.images.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {formData.images.map((image, index) => (
                <div key={index} className="relative group">
                  {/* Image Preview */}
                  <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={image.url}
                      alt={image.alt || `Package image ${index + 1}`}
                      className="w-full h-48 object-cover"
                    />

                    {/* Primary Badge */}
                    {image.isPrimary && (
                      <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                        Primary
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex space-x-1">
                        {!image.isPrimary && (
                          <button
                            type="button"
                            onClick={() => setPrimaryImage(index)}
                            className="bg-blue-600 text-white p-1 rounded text-xs hover:bg-blue-700"
                            title="Set as primary"
                          >
                            ⭐
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="bg-red-600 text-white p-1 rounded text-xs hover:bg-red-700"
                          title="Remove image"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>

                    {/* Move Buttons */}
                    <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex space-x-1">
                        {index > 0 && (
                          <button
                            type="button"
                            onClick={() => moveImage(index, index - 1)}
                            className="bg-gray-600 text-white p-1 rounded text-xs hover:bg-gray-700"
                            title="Move left"
                          >
                            ←
                          </button>
                        )}
                        {index < formData.images.length - 1 && (
                          <button
                            type="button"
                            onClick={() => moveImage(index, index + 1)}
                            className="bg-gray-600 text-white p-1 rounded text-xs hover:bg-gray-700"
                            title="Move right"
                          >
                            →
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Image Details */}
                  <div className="mt-3 space-y-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Alt Text
                      </label>
                      <input
                        type="text"
                        value={image.alt || ''}
                        onChange={(e) => updateImage(index, 'alt', e.target.value)}
                        placeholder="Describe the image"
                        className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Caption
                      </label>
                      <input
                        type="text"
                        value={image.caption || ''}
                        onChange={(e) => updateImage(index, 'caption', e.target.value)}
                        placeholder="Optional caption"
                        className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🖼️</div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">No Images Yet</h4>
              <p className="text-gray-600">
                Upload some images to showcase your package
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={handleReset}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={isLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Media'
          )}
        </button>
      </div>
    </div>
  );
}
