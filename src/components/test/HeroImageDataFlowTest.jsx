'use client';

import { useState } from 'react';

export default function HeroImageDataFlowTest() {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const runDataFlowTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    const results = [];
    
    try {
      // Test 1: Check upload API response structure
      results.push({ test: 'Upload API Structure', status: 'info', message: 'Testing upload API response format...' });
      
      // Test 2: Check hero images API response
      results.push({ test: 'Hero Images API', status: 'info', message: 'Testing /api/hero-images endpoint...' });
      
      const response = await fetch('/api/hero-images');
      const result = await response.json();
      
      if (result.success) {
        results.push({ test: 'Hero Images API', status: 'success', message: `Found ${result.data.length} hero images` });
        
        // Test 3: Validate URL structure
        if (result.data.length > 0) {
          const firstImage = result.data[0];
          const hasValidUrl = firstImage.url && typeof firstImage.url === 'string';
          const isFirebaseUrl = firstImage.url.includes('firebasestorage.googleapis.com') || firstImage.url.includes('mock-token');
          
          results.push({ 
            test: 'URL Structure', 
            status: hasValidUrl ? 'success' : 'error', 
            message: `URL field: ${hasValidUrl ? 'Valid string' : 'Invalid or missing'}`
          });
          
          results.push({ 
            test: 'Firebase URL Format', 
            status: isFirebaseUrl ? 'success' : 'warning', 
            message: `Firebase URL: ${isFirebaseUrl ? 'Valid format' : 'Not Firebase format'}`
          });
          
          // Test 4: Check required fields
          const hasName = firstImage.name && typeof firstImage.name === 'string';
          results.push({ 
            test: 'Required Fields', 
            status: hasName ? 'success' : 'error', 
            message: `Name field: ${hasName ? 'Valid' : 'Missing or invalid'}`
          });
          
          // Display sample data
          results.push({ 
            test: 'Sample Data', 
            status: 'info', 
            message: `Sample: ${JSON.stringify({
              name: firstImage.name,
              url: firstImage.url?.substring(0, 50) + '...',
              id: firstImage._id
            }, null, 2)}`
          });
        } else {
          results.push({ test: 'Data Validation', status: 'warning', message: 'No hero images found to validate' });
        }
      } else {
        results.push({ test: 'Hero Images API', status: 'error', message: result.message || 'API request failed' });
      }
      
    } catch (error) {
      results.push({ test: 'API Connection', status: 'error', message: `Error: ${error.message}` });
    }
    
    setTestResults(results);
    setIsLoading(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Hero Image Data Flow Test</h1>
      
      <div className="mb-6">
        <button
          onClick={runDataFlowTest}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? 'Running Tests...' : 'Run Data Flow Test'}
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Test Results:</h2>
          {testResults.map((result, index) => (
            <div
              key={index}
              className={`p-4 border rounded-md ${getStatusColor(result.status)}`}
            >
              <div className="font-medium">{result.test}</div>
              <div className="mt-1 text-sm whitespace-pre-wrap">{result.message}</div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-8 p-4 bg-gray-50 rounded-md">
        <h3 className="font-medium text-gray-800 mb-2">Expected Data Flow:</h3>
        <ol className="list-decimal list-inside text-sm text-gray-600 space-y-1">
          <li>User uploads files via HeroImageForm</li>
          <li>Files uploaded to Firebase Storage under 'hero-images' folder</li>
          <li>Upload API returns array of objects with Firebase URLs</li>
          <li>HeroImageForm creates individual records with single URL strings</li>
          <li>Records saved to MongoDB with url field as string</li>
          <li>LandingpageCarousell fetches all records and uses url field directly</li>
        </ol>
      </div>
    </div>
  );
}
