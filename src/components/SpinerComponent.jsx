import React from 'react'

const Spinner = ({ size = 'h-6 w-6', color = 'text-gray-50', borderWidth = 'border-2', className = '' }) => {
  return (
    <div className={`animate-spin rounded-full ${borderWidth} border-t-transparent ${color} ${size} ${className}`}></div>
  );
};

export default function LoadingComponent({ size = "h-10 w-10", color = "text-white", borderWidth = "border-4" }) {
  return (
    <div className="fixed top-0 z-50 left-0 w-full h-full bg-black/75 flex flex-col justify-center items-center text-white ">
      <div className="w-[50px] h-[50px] border-[5px] border-white/30 rounded-full border-t-white animate-spin mb-5"></div>
    </div>
  )
}
