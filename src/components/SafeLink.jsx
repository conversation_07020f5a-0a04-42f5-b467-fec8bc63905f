'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

/**
 * SafeLink - A wrapper around Next.js Link with error handling
 * Prevents navigation errors and provides fallback behavior
 */
export default function SafeLink({ 
  href, 
  children, 
  fallbackHref = '/', 
  onError,
  ...props 
}) {
  const router = useRouter()
  const [hasError, setHasError] = useState(false)

  const handleClick = (e) => {
    try {
      // Validate href
      if (!href || typeof href !== 'string') {
        console.warn('SafeLink: Invalid href provided:', href)
        e.preventDefault()
        
        if (onError) {
          onError(new Error('Invalid href'))
        }
        
        // Navigate to fallback
        router.push(fallbackHref)
        return
      }

      // Check if href is a valid URL format
      try {
        new URL(href, window.location.origin)
      } catch (urlError) {
        // If not a valid URL, treat as relative path
        if (!href.startsWith('/') && !href.startsWith('#') && !href.startsWith('?')) {
          console.warn('SafeLink: Potentially invalid relative path:', href)
        }
      }

      // Call original onClick if provided
      if (props.onClick) {
        props.onClick(e)
      }
    } catch (error) {
      console.error('SafeLink: Error during navigation:', error)
      e.preventDefault()
      setHasError(true)
      
      if (onError) {
        onError(error)
      }
      
      // Navigate to fallback after a short delay
      setTimeout(() => {
        router.push(fallbackHref)
      }, 100)
    }
  }

  // If there was an error, show a simple fallback
  if (hasError) {
    return (
      <span 
        className="text-red-500 cursor-not-allowed"
        title="Navigation error occurred"
      >
        {children}
      </span>
    )
  }

  return (
    <Link 
      href={href || fallbackHref}
      {...props}
      onClick={handleClick}
    >
      {children}
    </Link>
  )
}
