'use client'

import { useContextAudio } from '@/contexts/useContextAudio'
import { useEffect, useRef, useState } from 'react'

const AudioPlayer = () => {
  const { playAuido, setPlayAuido, pauseAudio, isPlaying, setIsPlaying } = useContextAudio()
  const audioRef = useRef(null)
  const fadeIntervalRef = useRef(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)

  // Preload audio immediately when component mounts
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    // Set up audio properties
    audio.loop = true
    audio.preload = 'auto'
    audio.volume = 0 // Start with volume at 0 for fade-in effect

    // Event listeners for audio loading states
    const handleCanPlayThrough = () => {
      setIsLoaded(true)
      setHasError(false)
    }

    const handleError = (e) => {
      console.error('Audio loading error:', e)
      setHasError(true)
      setIsLoaded(false)
    }

    const handleLoadStart = () => {
      // console.log('Audio loading started')
    }

    const handleLoadedData = () => {
      // console.log('Audio data loaded')
    }

    const handleProgress = () => {
      // console.log('Audio loading progress')
    }

    const handleCanPlay = () => {
      // console.log('Audio can start playing')
    }

    // Add event listeners
    audio.addEventListener('canplaythrough', handleCanPlayThrough)
    audio.addEventListener('canplay', handleCanPlay)
    audio.addEventListener('error', handleError)
    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('loadeddata', handleLoadedData)
    audio.addEventListener('progress', handleProgress)

    // Start loading the audio
    // console.log('Attempting to load audio from:', audio.src || 'no src set')
    // console.log('Audio element:', audio)
    audio.load()

    // Force a load attempt after a short delay
    setTimeout(() => {
      // console.log('Audio readyState after load():', audio.readyState)
      // console.log('Audio networkState:', audio.networkState)
      if (audio.readyState === 0) {
        console.log('Audio not loading, attempting manual load...')
        audio.load()
      }
    }, 1000)

    // Cleanup event listeners
    return () => {
      audio.removeEventListener('canplaythrough', handleCanPlayThrough)
      audio.removeEventListener('canplay', handleCanPlay)
      audio.removeEventListener('error', handleError)
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('loadeddata', handleLoadedData)
      audio.removeEventListener('progress', handleProgress)
    }
  }, [])

  // Handle playback triggers and pause/resume from context
  useEffect(() => {
    const audio = audioRef.current

    // Handle playAuido trigger
    if (playAuido && isLoaded && !hasError && !isPlaying) {
      console.log('Starting audio playback via playAuido trigger')
      startPlaybackWithFadeIn()
      setPlayAuido(false) // Reset flag after triggering
    } else if (playAuido && !isLoaded && !hasError) {
      console.log('Audio playback requested but audio not yet loaded, will start when ready')
    }

    // Handle pause/resume
    if (audio && isLoaded) {
      if (pauseAudio && isPlaying) {
        console.log('Pausing audio via pauseAudio trigger')
        audio.pause()
      } else if (!pauseAudio && isPlaying && audio.paused) {
        console.log('Resuming audio via pauseAudio trigger')
        audio.play().catch(error => {
          console.error('Error resuming audio:', error)
          setHasError(true)
        })
      }
    }

    // Cleanup on unmount - fade out and stop
    return () => {
      if (audioRef.current && isPlaying) {
        fadeOutAndStop()
      }
    }
  }, [playAuido, pauseAudio, isLoaded, hasError, isPlaying, setPlayAuido])

  // Fade-in function
  const startPlaybackWithFadeIn = async () => {
    const audio = audioRef.current
    if (!audio) return

    try {
      audio.volume = 0
      await audio.play()
      setIsPlaying(true)

      // Fade in over 2 seconds
      const fadeInDuration = 2000 // 2 seconds
      const fadeInSteps = 50
      const volumeIncrement = 1 / fadeInSteps
      const fadeInInterval = fadeInDuration / fadeInSteps

      let currentStep = 0
      fadeIntervalRef.current = setInterval(() => {
        if (currentStep < fadeInSteps) {
          const newVolume = Math.min(1, (currentStep + 1) * volumeIncrement)
          audio.volume = newVolume
          currentStep++
        } else {
          clearInterval(fadeIntervalRef.current)
          fadeIntervalRef.current = null
        }
      }, fadeInInterval)

    } catch (error) {
      if (error.name === 'NotAllowedError') {
        console.warn('Audio playback blocked by browser - user interaction required first')
        // Don't set hasError for this case, as it's expected behavior
        setIsPlaying(false)
      } else {
        console.error('Error playing audio:', error)
        setHasError(true)
      }
    }
  }

  // Fade-out function
  const fadeOutAndStop = () => {
    const audio = audioRef.current
    if (!audio || !isPlaying) return

    // Clear any existing fade interval
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current)
      fadeIntervalRef.current = null
    }

    // Fade out over 4 seconds
    const fadeOutDuration = 4000 // 4 seconds
    const fadeOutSteps = 50
    const currentVolume = audio.volume
    const volumeDecrement = currentVolume / fadeOutSteps
    const fadeOutInterval = fadeOutDuration / fadeOutSteps

    let currentStep = 0
    fadeIntervalRef.current = setInterval(() => {
      if (currentStep < fadeOutSteps) {
        const newVolume = Math.max(0, currentVolume - (currentStep + 1) * volumeDecrement)
        audio.volume = newVolume
        currentStep++
      } else {
        audio.pause()
        audio.currentTime = 0
        setIsPlaying(false)
        clearInterval(fadeIntervalRef.current)
        fadeIntervalRef.current = null
      }
    }, fadeOutInterval)
  }

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      if (fadeIntervalRef.current) {
        clearInterval(fadeIntervalRef.current)
      }
    }
  }, [])

  // console.log('AudioPlayer state:', { playAuido, pauseAudio, isPlaying, isLoaded, hasError })

  return (
    <>
      {/* Hidden audio element */}
      <audio
        ref={audioRef}
        preload="auto"
        className="hidden"
      >
        <source src="/assets/Ambient_Music.mp4" type="audio/mp4" />
        <source src="/assets/Ambient_Music.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      {/* Optional: Debug info (remove in production) */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs p-2 rounded z-50">
          <div>Audio Status:</div>
          <div>Loaded: {isLoaded ? '✓' : '✗'}</div>
          <div>Playing: {isPlaying ? '✓' : '✗'}</div>
          <div>Error: {hasError ? '✓' : '✗'}</div>
          <div>Volume: {audioRef.current?.volume?.toFixed(2) || '0.00'}</div>
        </div>
      )} */}
    </>
  )
}

export default AudioPlayer
