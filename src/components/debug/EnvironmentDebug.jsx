'use client';

import { useState } from 'react';

export default function EnvironmentDebug() {
  const [showDebug, setShowDebug] = useState(false);

  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'SET' : 'NOT SET',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    // Only show public env vars for security
  };

  if (!showDebug) {
    return (
      <button
        onClick={() => setShowDebug(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-md text-sm z-50"
      >
        Debug Env
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Environment Debug</h3>
        <button
          onClick={() => setShowDebug(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>
      <div className="space-y-1 text-xs">
        {Object.entries(envVars).map(([key, value]) => (
          <div key={key} className="flex justify-between">
            <span className="font-mono text-gray-600">{key}:</span>
            <span className={`font-mono ${value === 'NOT SET' ? 'text-red-500' : 'text-green-500'}`}>
              {value || 'undefined'}
            </span>
          </div>
        ))}
      </div>
      <div className="mt-2 pt-2 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Total public env vars: {Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_')).length}
        </p>
      </div>
    </div>
  );
}
