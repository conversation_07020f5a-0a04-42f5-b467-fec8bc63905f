'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdVideoLibrary } from 'react-icons/md';

export default function VideoGalleryForm({ 
  videoGallery = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    url: '',
    thumbnail: '',
  });
  const [errors, setErrors] = useState({});
  const [videoFile, setVideoFile] = useState(null);
  const [videoPreview, setVideoPreview] = useState('');
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize form data when videoGallery prop changes
  useEffect(() => {
    if (videoGallery) {
      setFormData({
        title: videoGallery.title || '',
        description: videoGallery.description || '',
        url: videoGallery.url || '',
        thumbnail: videoGallery.thumbnail || '',
      });
      setVideoPreview(videoGallery.url || '');
      setThumbnailPreview(videoGallery.thumbnail || '');
    } else {
      setFormData({
        title: '',
        description: '',
        url: '',
        thumbnail: '',
      });
      setVideoPreview('');
      setThumbnailPreview('');
    }
  }, [videoGallery]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Update video preview when URL changes
    if (name === 'url') {
      if (value && value.trim()) {
        setVideoPreview(value.trim());
        // Clear any selected file when URL is entered
        setVideoFile(null);
      } else {
        // If URL is cleared and no file is selected, clear preview
        if (!videoFile) {
          setVideoPreview('');
        }
      }
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setVideoFile(file);

      // Create preview URL
      const videoUrl = URL.createObjectURL(file);
      setVideoPreview(videoUrl);

      // Clear URL field when file is selected
      setFormData(prev => ({
        ...prev,
        url: ''
      }));
    }
  };

  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setThumbnailFile(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setThumbnailPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadVideo = async () => {
    if (!videoFile) return null;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', videoFile);

      const response = await fetch('/api/upload/video-gallery', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // Handle both single file format (result.url) and multiple file format (result.data[0].url)
        const videoUrl = result.url || (result.data && result.data.length > 0 ? result.data[0].url : null);
        if (videoUrl) {
          return videoUrl;
        } else {
          throw new Error('No video URL returned from upload');
        }
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Video upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const uploadThumbnail = async () => {
    if (!thumbnailFile) return null;

    try {
      const formData = new FormData();
      formData.append('files', thumbnailFile);

      const response = await fetch('/api/upload/video-gallery/thumbnails', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // Handle both single file format (result.url) and multiple file format (result.data[0].url)
        const thumbnailUrl = result.url || (result.data && result.data.length > 0 ? result.data[0].url : null);
        if (thumbnailUrl) {
          return thumbnailUrl;
        } else {
          throw new Error('No thumbnail URL returned from upload');
        }
      } else {
        throw new Error(result.error || 'Thumbnail upload failed');
      }
    } catch (error) {
      console.error('Thumbnail upload error:', error);
      throw error;
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    // Validate URL format if provided
    if (formData.url && formData.url.trim()) {
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(formData.url.trim())) {
        newErrors.url = 'Please enter a valid URL starting with http:// or https://';
      }
    }

    // For new videos, require either URL or file upload. For existing videos, allow keeping existing URL
    if (!videoGallery && !videoFile && !formData.url.trim()) {
      newErrors.video = 'Either video file upload or video URL is required';
    }

    if (!formData.thumbnail && !thumbnailFile) {
      newErrors.thumbnail = 'Thumbnail is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let videoUrl = formData.url;
      let thumbnailUrl = formData.thumbnail;

      // Upload new video if selected
      if (videoFile) {
        videoUrl = await uploadVideo();
      }

      // Upload new thumbnail if selected
      if (thumbnailFile) {
        thumbnailUrl = await uploadThumbnail();
      }

      const submitData = {
        ...formData,
        url: videoUrl,
        thumbnail: thumbnailUrl,
      };

      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save video. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {videoGallery ? 'Edit Video Gallery Item' : 'Create New Video Gallery Item'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter video title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter video description"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description}</p>
          )}
        </div>

        {/* Video URL Input */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
            Video URL
          </label>
          <input
            type="url"
            id="url"
            name="url"
            value={formData.url}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.url ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="https://example.com/video.mp4 or enter video URL"
          />
          {errors.url && (
            <p className="mt-1 text-sm text-red-600">{errors.url}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            Enter a direct video URL as an alternative to file upload. Must be a valid URL format.
          </p>
        </div>

        {/* Video Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Video File {!videoGallery && !formData.url ? '*' : '(Upload new file to replace)'}
          </label>

          {/* Video Preview */}
          {videoPreview && (
            <div className="mb-4">
              <video
                src={videoPreview}
                controls
                className="w-full max-w-md h-48 object-cover rounded-md border border-gray-300"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          )}

          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              Choose Video
              <input
                type="file"
                accept="video/*"
                onChange={handleVideoChange}
                className="hidden"
              />
            </label>

            {videoFile && (
              <span className="text-sm text-gray-600">
                {videoFile.name}
              </span>
            )}
          </div>

          {errors.video && (
            <p className="mt-1 text-sm text-red-600">{errors.video}</p>
          )}

          <p className="mt-1 text-sm text-gray-500">
            Upload a video file to Firebase storage. Supported formats: MP4, WebM, AVI. Maximum size: 100MB.
          </p>
        </div>



        {/* Thumbnail Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Thumbnail Image *
          </label>

          {/* Thumbnail Preview */}
          {thumbnailPreview && (
            <div className="mb-4">
              <img
                src={thumbnailPreview}
                alt="Thumbnail preview"
                className="w-full max-w-md h-32 object-cover rounded-md border border-gray-300"
              />
            </div>
          )}

          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              Choose Thumbnail
              <input
                type="file"
                accept="image/*"
                onChange={handleThumbnailChange}
                className="hidden"
              />
            </label>

            {thumbnailFile && (
              <span className="text-sm text-gray-600">
                {thumbnailFile.name}
              </span>
            )}
          </div>

          {errors.thumbnail && (
            <p className="mt-1 text-sm text-red-600">{errors.thumbnail}</p>
          )}

          <p className="mt-1 text-sm text-gray-500">
            Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 10MB.
          </p>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Video'}
          </button>
        </div>
      </form>
    </div>
  );
}
