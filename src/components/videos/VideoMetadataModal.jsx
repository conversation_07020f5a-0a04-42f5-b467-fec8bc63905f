'use client';

import { useState, useEffect } from 'react';
import { MdClose, MdCloudUpload, MdImage, MdSave, MdCancel } from 'react-icons/md';

export default function VideoMetadataModal({ 
  isOpen, 
  onClose, 
  videoFile, 
  onSave, 
  currentIndex = 0, 
  totalVideos = 1 
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
  });
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState('');
  const [errors, setErrors] = useState({});
  const [isUploading, setIsUploading] = useState(false);

  // Reset form when modal opens with new video
  useEffect(() => {
    if (isOpen && videoFile) {
      // Auto-generate title from filename
      const fileName = videoFile.name.replace(/\.[^/.]+$/, ''); // Remove extension
      setFormData({
        title: fileName,
        description: '',
      });
      setThumbnailFile(null);
      setThumbnailPreview('');
      setErrors({});
    }
  }, [isOpen, videoFile]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setThumbnailFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setThumbnailPreview(e.target.result);
      };
      reader.readAsDataURL(file);
      
      // Clear thumbnail error
      if (errors.thumbnail) {
        setErrors(prev => ({
          ...prev,
          thumbnail: ''
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    if (!thumbnailFile) {
      newErrors.thumbnail = 'Thumbnail image is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const uploadThumbnail = async () => {
    if (!thumbnailFile) return null;
    
    try {
      const formData = new FormData();
      formData.append('files', thumbnailFile);
      
      const response = await fetch('/api/upload/video-gallery/thumbnails', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success) {
        return result.url;
      } else {
        throw new Error(result.error || 'Thumbnail upload failed');
      }
    } catch (error) {
      console.error('Thumbnail upload error:', error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsUploading(true);
    try {
      // Upload thumbnail first
      const thumbnailUrl = await uploadThumbnail();
      
      // Prepare video metadata
      const videoMetadata = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        thumbnail: thumbnailUrl,
        videoFile: videoFile
      };
      
      await onSave(videoMetadata);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: error.message || 'Failed to save video metadata. Please try again.' });
    } finally {
      setIsUploading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Video Metadata
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Video {currentIndex + 1} of {totalVideos}: {videoFile?.name}
            </p>
          </div>
          <button
            onClick={onClose}
            disabled={isUploading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <MdClose className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.title ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter video title"
              disabled={isUploading}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter video description"
              disabled={isUploading}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* Thumbnail */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Thumbnail Image *
            </label>
            
            {/* Thumbnail Preview */}
            {thumbnailPreview && (
              <div className="mb-4">
                <img
                  src={thumbnailPreview}
                  alt="Thumbnail preview"
                  className="w-full h-32 object-cover rounded-md border border-gray-300"
                />
              </div>
            )}
            
            {/* File Input */}
            <div className="flex items-center space-x-4">
              <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                <MdCloudUpload className="mr-2" />
                Choose Thumbnail
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleThumbnailChange}
                  className="hidden"
                  disabled={isUploading}
                />
              </label>
              
              {thumbnailFile && (
                <span className="text-sm text-gray-600">
                  {thumbnailFile.name}
                </span>
              )}
            </div>
            
            {errors.thumbnail && (
              <p className="mt-1 text-sm text-red-600">{errors.thumbnail}</p>
            )}
            
            <p className="mt-1 text-sm text-gray-500">
              Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 10MB.
            </p>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              disabled={isUploading}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center disabled:opacity-50"
            >
              <MdCancel className="mr-2" />
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={isUploading}
              className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
                isUploading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <MdSave className="mr-2" />
              {isUploading ? 'Saving...' : 'Save & Continue'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
