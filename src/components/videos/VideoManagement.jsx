'use client';

import { useState } from 'react';
import { MdAdd, MdArrowBack, MdVideoLibrary, MdStar } from 'react-icons/md';
import VideoGalleryForm from './VideoGalleryForm';
import VideoGalleryList from './VideoGalleryList';
import HeroVideoForm from './HeroVideoForm';
import HeroVideoList from './HeroVideoList';
import MultipleVideoUpload from './MultipleVideoUpload';

export default function VideoManagement() {
  const [activeTab, setActiveTab] = useState('gallery'); // 'gallery' | 'hero'
  const [currentView, setCurrentView] = useState('list'); // 'list' | 'create' | 'edit' | 'multiple-upload'
  const [selectedItem, setSelectedItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState(null);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreate = () => {
    setSelectedItem(null);
    setCurrentView('create');
  };

  const handleMultipleUpload = () => {
    setCurrentView('multiple-upload');
  };

  const handleMultipleUploadComplete = (results) => {
    const { uploaded, failed, total } = results;

    if (uploaded.length > 0) {
      showNotification(
        `Successfully uploaded ${uploaded.length} of ${total} videos${failed.length > 0 ? ` (${failed.length} failed)` : ''}`
      );
    } else {
      showNotification('No videos were uploaded successfully', 'error');
    }

    setCurrentView('list');
    setRefreshTrigger(prev => prev + 1);
  };

  const handleEdit = (item) => {
    setSelectedItem(item);
    setCurrentView('edit');
  };

  const handleSave = async (formData) => {
    setIsLoading(true);
    try {
      const apiEndpoint = activeTab === 'gallery' ? 'video-gallery' : 'hero-videos';
      const url = selectedItem 
        ? `/api/${apiEndpoint}/${selectedItem._id}`
        : `/api/${apiEndpoint}`;
      
      const method = selectedItem ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        const itemType = activeTab === 'gallery' ? 'video' : 'hero video';
        showNotification(
          selectedItem 
            ? `${itemType} updated successfully` 
            : `${itemType} created successfully`
        );
        setCurrentView('list');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || `Failed to save ${activeTab === 'gallery' ? 'video' : 'hero video'}`);
      }
    } catch (error) {
      console.error('Save error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const itemType = activeTab === 'gallery' ? 'video' : 'hero video';
    if (!window.confirm(`Are you sure you want to delete this ${itemType}?`)) {
      return;
    }

    try {
      const apiEndpoint = activeTab === 'gallery' ? 'video-gallery' : 'hero-videos';
      const response = await fetch(`/api/${apiEndpoint}/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification(`${itemType} deleted successfully`);
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || `Failed to delete ${itemType}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleBulkDelete = async (ids) => {
    try {
      const apiEndpoint = activeTab === 'gallery' ? 'video-gallery' : 'hero-videos';
      const response = await fetch(`/api/${apiEndpoint}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      const data = await response.json();

      if (data.success) {
        const itemType = activeTab === 'gallery' ? 'videos' : 'hero videos';
        showNotification(`${data.data.deletedCount} ${itemType} deleted successfully`);
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || `Failed to delete ${activeTab === 'gallery' ? 'videos' : 'hero videos'}`);
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleCancel = () => {
    setCurrentView('list');
    setSelectedItem(null);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setCurrentView('list');
    setSelectedItem(null);
  };

  const getTitle = () => {
    if (currentView === 'list') {
      return 'Video Management';
    }
    if (currentView === 'create') {
      return activeTab === 'gallery' ? 'Create New Video' : 'Create New Hero Video';
    }
    if (currentView === 'edit') {
      return activeTab === 'gallery' ? 'Edit Video' : 'Edit Hero Video';
    }
    if (currentView === 'multiple-upload') {
      return 'Upload Multiple Videos';
    }
    return 'Video Management';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {currentView !== 'list' && (
            <button
              onClick={handleCancel}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
            >
              <MdArrowBack className="w-5 h-5" />
            </button>
          )}
          <h1 className="text-2xl font-bold text-gray-900">
            {getTitle()}
          </h1>
        </div>

        {currentView === 'list' && (
          <div className="flex items-center space-x-3">
            {activeTab === 'gallery' && (
              <button
                onClick={handleMultipleUpload}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
              >
                <MdVideoLibrary className="mr-2" />
                Upload Multiple
              </button>
            )}
            <button
              onClick={handleCreate}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
            >
              <MdAdd className="mr-2" />
              Add {activeTab === 'gallery' ? 'Video' : 'Hero Video'}
            </button>
          </div>
        )}
      </div>

      {/* Notification */}
      {notification && (
        <div className={`p-4 rounded-md ${
          notification.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-700'
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Tabs (only show in list view) */}
      {currentView === 'list' && (
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => handleTabChange('gallery')}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === 'gallery'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <MdVideoLibrary className="mr-2" />
              Video Gallery
            </button>
            <button
              onClick={() => handleTabChange('hero')}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === 'hero'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <MdStar className="mr-2" />
              Hero Videos
            </button>
          </nav>
        </div>
      )}

      {/* Content */}
      {currentView === 'list' && activeTab === 'gallery' && (
        <VideoGalleryList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBulkDelete={handleBulkDelete}
          refreshTrigger={refreshTrigger}
        />
      )}

      {currentView === 'list' && activeTab === 'hero' && (
        <HeroVideoList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBulkDelete={handleBulkDelete}
          refreshTrigger={refreshTrigger}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && activeTab === 'gallery' && (
        <VideoGalleryForm
          videoGallery={selectedItem}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && activeTab === 'hero' && (
        <HeroVideoForm
          heroVideo={selectedItem}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      )}

      {currentView === 'multiple-upload' && activeTab === 'gallery' && (
        <MultipleVideoUpload
          onUploadComplete={handleMultipleUploadComplete}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
}
