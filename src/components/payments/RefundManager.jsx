'use client';

import { useState } from 'react';

export default function RefundManager({ payment, onRefundComplete, onClose }) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [refundAmount, setRefundAmount] = useState(payment.amount);
  const [refundReason, setRefundReason] = useState('');
  const [refundType, setRefundType] = useState('full');

  const handleRefund = async (e) => {
    e.preventDefault();
    setIsProcessing(true);
    setError(null);

    try {
      const response = await fetch('/api/payments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId: payment._id,
          refundAmount: refundType === 'full' ? payment.amount : refundAmount,
          reason: refundReason,
        }),
      });

      const data = await response.json();

      if (data.success) {
        onRefundComplete?.(data.data);
        onClose?.();
      } else {
        setError(data.message || 'Refund failed');
      }
    } catch (err) {
      setError('Failed to process refund');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const maxRefundAmount = payment.amount - (payment.refund?.amount || 0);

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Process Refund</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Payment Information */}
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <h4 className="font-medium text-gray-900 mb-2">Payment Details</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Payment ID:</span>
                <span className="font-mono">{payment.paymentId}</span>
              </div>
              <div className="flex justify-between">
                <span>Original Amount:</span>
                <span>{formatAmount(payment.amount)}</span>
              </div>
              <div className="flex justify-between">
                <span>Already Refunded:</span>
                <span>{formatAmount(payment.refund?.amount || 0)}</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Available for Refund:</span>
                <span>{formatAmount(maxRefundAmount)}</span>
              </div>
            </div>
          </div>

          {/* Refund Form */}
          <form onSubmit={handleRefund} className="space-y-4">
            {/* Refund Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Refund Type
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="refundType"
                    value="full"
                    checked={refundType === 'full'}
                    onChange={(e) => {
                      setRefundType(e.target.value);
                      setRefundAmount(maxRefundAmount);
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Full Refund ({formatAmount(maxRefundAmount)})
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="refundType"
                    value="partial"
                    checked={refundType === 'partial'}
                    onChange={(e) => setRefundType(e.target.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Partial Refund</span>
                </label>
              </div>
            </div>

            {/* Refund Amount (for partial refunds) */}
            {refundType === 'partial' && (
              <div>
                <label htmlFor="refundAmount" className="block text-sm font-medium text-gray-700">
                  Refund Amount
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    id="refundAmount"
                    value={refundAmount}
                    onChange={(e) => setRefundAmount(parseFloat(e.target.value) || 0)}
                    min="0.01"
                    max={maxRefundAmount}
                    step="0.01"
                    className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                    placeholder="0.00"
                  />
                </div>
                {refundAmount > maxRefundAmount && (
                  <p className="mt-1 text-sm text-red-600">
                    Amount cannot exceed {formatAmount(maxRefundAmount)}
                  </p>
                )}
              </div>
            )}

            {/* Refund Reason */}
            <div>
              <label htmlFor="refundReason" className="block text-sm font-medium text-gray-700">
                Reason for Refund
              </label>
              <select
                id="refundReason"
                value={refundReason}
                onChange={(e) => setRefundReason(e.target.value)}
                required
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Select a reason</option>
                <option value="customer_request">Customer Request</option>
                <option value="cancellation">Booking Cancellation</option>
                <option value="service_issue">Service Issue</option>
                <option value="weather">Weather Related</option>
                <option value="emergency">Emergency</option>
                <option value="duplicate_payment">Duplicate Payment</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={
                  isProcessing || 
                  !refundReason || 
                  refundAmount <= 0 || 
                  refundAmount > maxRefundAmount
                }
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  `Refund ${formatAmount(refundType === 'full' ? maxRefundAmount : refundAmount)}`
                )}
              </button>
            </div>
          </form>

          {/* Warning */}
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex">
              <svg className="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Important</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Refunds typically take 5-10 business days to appear on the customer's statement. 
                  This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
