'use client';

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      color: '#424770',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#9e2146',
      iconColor: '#9e2146',
    },
  },
  hidePostalCode: false,
};

function PaymentFormContent({ 
  booking, 
  onSuccess, 
  onError, 
  onProcessing,
  savePaymentMethod = false 
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [clientSecret, setClientSecret] = useState('');
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [useNewCard, setUseNewCard] = useState(true);

  useEffect(() => {
    // Create payment intent when component mounts
    createPaymentIntent();
    // Load saved payment methods if user is authenticated
    loadPaymentMethods();
  }, [booking]);

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookingId: booking._id,
          amount: booking.pricing.totalAmount,
          currency: booking.pricing.currency || 'usd',
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setClientSecret(data.data.clientSecret);
      } else {
        setError(data.message || 'Failed to create payment intent');
      }
    } catch (err) {
      setError('Failed to initialize payment');
    }
  };

  const loadPaymentMethods = async () => {
    try {
      const response = await fetch('/api/payments/methods');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setPaymentMethods(data.data.paymentMethods);
        }
      }
    } catch (err) {
      console.error('Failed to load payment methods:', err);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setError(null);
    onProcessing?.(true);

    try {
      let result;

      if (useNewCard) {
        const cardElement = elements.getElement(CardElement);
        
        result = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: booking.customer.name,
              email: booking.customer.email,
              phone: booking.customer.phone,
            },
          },
          setup_future_usage: savePaymentMethod ? 'off_session' : undefined,
        });
      } else {
        // Use saved payment method
        result = await stripe.confirmCardPayment(clientSecret, {
          payment_method: selectedPaymentMethod,
        });
      }

      if (result.error) {
        setError(result.error.message);
        onError?.(result.error);
      } else {
        // Payment succeeded
        onSuccess?.(result.paymentIntent);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      onError?.(err);
    } finally {
      setIsProcessing(false);
      onProcessing?.(false);
    }
  };

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Summary */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Package:</span>
            <span>{booking.package?.name}</span>
          </div>
          <div className="flex justify-between">
            <span>Guests:</span>
            <span>{booking.guests.total} ({booking.guests.guestType})</span>
          </div>
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatAmount(booking.pricing.basePrice)}</span>
          </div>
          {booking.pricing.taxes > 0 && (
            <div className="flex justify-between">
              <span>Taxes:</span>
              <span>{formatAmount(booking.pricing.taxes)}</span>
            </div>
          )}
          {booking.pricing.fees > 0 && (
            <div className="flex justify-between">
              <span>Fees:</span>
              <span>{formatAmount(booking.pricing.fees)}</span>
            </div>
          )}
          <div className="flex justify-between font-medium text-lg border-t pt-2">
            <span>Total:</span>
            <span>{formatAmount(booking.pricing.totalAmount)}</span>
          </div>
        </div>
      </div>

      {/* Payment Method Selection */}
      {paymentMethods.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Payment Method</h3>
          
          {/* Saved Payment Methods */}
          <div className="space-y-2">
            {paymentMethods.map((method) => (
              <label key={method.id} className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="paymentMethod"
                  value={method.id}
                  checked={selectedPaymentMethod === method.id && !useNewCard}
                  onChange={(e) => {
                    setSelectedPaymentMethod(e.target.value);
                    setUseNewCard(false);
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="flex items-center space-x-2">
                  <span className="capitalize">{method.card.brand}</span>
                  <span>•••• {method.card.last4}</span>
                  <span className="text-sm text-gray-500">
                    {method.card.exp_month}/{method.card.exp_year}
                  </span>
                </div>
              </label>
            ))}
          </div>

          {/* New Card Option */}
          <label className="flex items-center space-x-3">
            <input
              type="radio"
              name="paymentMethod"
              value="new"
              checked={useNewCard}
              onChange={() => setUseNewCard(true)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span>Use a new card</span>
          </label>
        </div>
      )}

      {/* Card Element */}
      {useNewCard && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            {paymentMethods.length > 0 ? 'New Card Details' : 'Card Details'}
          </h3>
          <div className="p-4 border border-gray-300 rounded-md">
            <CardElement options={CARD_ELEMENT_OPTIONS} />
          </div>
          
          {/* Save Payment Method Option */}
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={savePaymentMethod}
              onChange={(e) => setSavePaymentMethod?.(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">
              Save this card for future bookings
            </span>
          </label>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!stripe || isProcessing || !clientSecret}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Processing...</span>
          </div>
        ) : (
          `Pay ${formatAmount(booking.pricing.totalAmount)}`
        )}
      </button>

      {/* Security Notice */}
      <div className="text-xs text-gray-500 text-center">
        <div className="flex items-center justify-center space-x-1">
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Your payment information is secure and encrypted</span>
        </div>
      </div>
    </form>
  );
}

export default function PaymentForm(props) {
  return (
    <Elements stripe={stripePromise}>
      <PaymentFormContent {...props} />
    </Elements>
  );
}
