/**
 * Utility functions for text processing and HTML cleanup
 */

/**
 * Strips HTML tags, CSS classes, inline styles, and other markup from text
 * while preserving the actual text content
 * @param {string} htmlString - The HTML string to clean
 * @returns {string} - Plain text without HTML markup
 */
export function stripHtmlTags(htmlString) {
  if (!htmlString || typeof htmlString !== 'string') {
    return '';
  }

  return htmlString
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove HTML entities and decode common ones
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    // Remove extra whitespace and normalize spaces
    .replace(/\s+/g, ' ')
    // Trim leading and trailing whitespace
    .trim();
}

/**
 * Cleans up text content from server data by removing HTML tags and markup
 * from specified fields
 * @param {Object} data - The data object to clean
 * @param {Array} fields - Array of field names to clean (default: ['title', 'body1', 'body2'])
 * @returns {Object} - New object with cleaned text fields
 */
export function cleanTextFields(data, fields = ['title', 'body1', 'body2']) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const cleanedData = { ...data };

  fields.forEach(field => {
    if (cleanedData[field]) {
      cleanedData[field] = stripHtmlTags(cleanedData[field]);
    }
  });

  // Handle secondary entries if they exist
  if (cleanedData.secondaryEntries && Array.isArray(cleanedData.secondaryEntries)) {
    cleanedData.secondaryEntries = cleanedData.secondaryEntries.map(entry => {
      const cleanedEntry = { ...entry };
      // Clean title, body, and body2 fields in secondary entries
      ['title', 'body', 'body2'].forEach(field => {
        if (cleanedEntry[field]) {
          cleanedEntry[field] = stripHtmlTags(cleanedEntry[field]);
        }
      });
      return cleanedEntry;
    });
  }

  return cleanedData;
}

/**
 * Cleans an array of data objects
 * @param {Array} dataArray - Array of data objects to clean
 * @param {Array} fields - Array of field names to clean
 * @returns {Array} - Array of cleaned data objects
 */
export function cleanTextFieldsArray(dataArray, fields = ['title', 'body1', 'body2']) {
  if (!Array.isArray(dataArray)) {
    return dataArray;
  }

  return dataArray.map(item => cleanTextFields(item, fields));
}
