'use client'

import { createContext, useContext, useReducer, useState, useRef } from "react"
import { experienceReducer,INITIAL_EXPERIENCE_STATE } from "./reducerExperience"

const ExperienceContext=createContext()

export function ExperienceContextProvider({children}) {
    const [menuToggle, setMenuToggle] = useState(false);
    const [experienceState,originalDispatch]=useReducer(experienceReducer,INITIAL_EXPERIENCE_STATE)

    // Add debouncing to prevent rapid successive dispatches
    const lastDispatchRef = useRef(null);
    const dispatchTimeoutRef = useRef(null);

    // Wrap dispatch with logging and debouncing to prevent race conditions
    const disptachExperience = (action) => {
        const now = Date.now();
        const timeSinceLastDispatch = lastDispatchRef.current ? now - lastDispatchRef.current : Infinity;

        console.log('🚀 DISPATCH CALLED:', {
            action,
            timestamp: new Date().toISOString(),
            timeSinceLastDispatch,
            stackTrace: new Error().stack?.split('\n').slice(1, 4).join('\n')
        });

        // Clear any pending dispatch
        if (dispatchTimeoutRef.current) {
            clearTimeout(dispatchTimeoutRef.current);
        }

        // For popup actions, add a small delay to prevent race conditions
        if (action.type.includes('POPUP')) {
            dispatchTimeoutRef.current = setTimeout(() => {
                lastDispatchRef.current = Date.now();
                originalDispatch(action);
            }, 10); // 10ms delay to prevent race conditions
        } else {
            // Non-popup actions dispatch immediately
            lastDispatchRef.current = now;
            originalDispatch(action);
        }
    };

  return <ExperienceContext.Provider value={{
        experienceState,disptachExperience,
        menuToggle, setMenuToggle
    }}>
        {children}
    </ExperienceContext.Provider>
}

export function useContextExperience(){
    const context=useContext(ExperienceContext)
    if(!context){
        throw new Error('useExperienceContext must be used within a ExperienceContextProvider');
    }
    return context
}
