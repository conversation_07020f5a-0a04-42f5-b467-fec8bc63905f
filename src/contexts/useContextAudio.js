'use client'

import { createContext, useContext, useEffect, useState } from "react"

const AudioContext=createContext()

export function AudioContextProvider({children}) {
    useEffect(() => {
        console.log('AudioContext loaded')
        return () => {
            console.log('AudioContext unloaded')
        }
    }, [])
    const [playAuido, setPlayAuido] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);
    const [pauseAudio, setPauseAuido] = useState(false);
  return <AudioContext.Provider value={{
        playAuido, setPlayAuido,
        isPlaying, setIsPlaying,
        pauseAudio, setPauseAuido
    }}>
        {children}
    </AudioContext.Provider>
}

export function useContextAudio(){
    const context=useContext(AudioContext)
    if(!context){
        throw new Error('useContextAudio must be used within a AudioContextProvider');
    }
    return context
}
