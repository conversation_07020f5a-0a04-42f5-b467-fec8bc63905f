'use client'

import { createContext, useContext, useState, useCallback } from 'react'

// Create the Marker Context
const MarkerContext = createContext()

// Initial state for marker context
const INITIAL_MARKER_STATE = {
  selectedMarker: null,
  activeMarkerId: null,
  markerData: {},
  isMarkerPopupOpen: false,
}

/**
 * MarkerContextProvider - Provides marker-related state and actions
 * This context handles marker selection, data sharing, and popup states
 * specifically for 360° viewer markers
 */
export function MarkerContextProvider({ children }) {
  const [markerState, setMarkerState] = useState(INITIAL_MARKER_STATE)

  // Set the currently selected marker
  const setSelectedMarker = useCallback((marker) => {
    try {
      if (!marker || typeof marker !== 'object') {
        console.warn('setSelectedMarker: Invalid marker provided', marker)
        return
      }

      setMarkerState(prev => ({
        ...prev,
        selectedMarker: marker,
        activeMarkerId: marker?.id || marker?._360Name || null,
      }))
    } catch (error) {
      console.error('Error in setSelectedMarker:', error)
    }
  }, [])

  // Set marker data for a specific marker ID
  const setMarkerData = useCallback((markerId, data) => {
    try {
      if (!markerId) {
        console.warn('setMarkerData: Invalid markerId provided', markerId)
        return
      }

      setMarkerState(prev => ({
        ...prev,
        markerData: {
          ...prev.markerData,
          [markerId]: data
        }
      }))
    } catch (error) {
      console.error('Error in setMarkerData:', error)
    }
  }, [])

  // Get marker data for a specific marker ID
  const getMarkerData = useCallback((markerId) => {
    return markerState.markerData[markerId] || null
  }, [markerState.markerData])

  // Clear all marker data
  const clearMarkerData = useCallback(() => {
    setMarkerState(prev => ({
      ...prev,
      markerData: {},
      selectedMarker: null,
      activeMarkerId: null,
    }))
  }, [])

  // Toggle marker popup state
  const toggleMarkerPopup = useCallback((isOpen) => {
    setMarkerState(prev => ({
      ...prev,
      isMarkerPopupOpen: typeof isOpen === 'boolean' ? isOpen : !prev.isMarkerPopupOpen
    }))
  }, [])

  // Handle marker click with automatic popup management
  const handleMarkerClick = useCallback((marker, shouldOpenPopup = true) => {
    setSelectedMarker(marker)
    
    if (shouldOpenPopup) {
      toggleMarkerPopup(true)
    }
  }, [setSelectedMarker, toggleMarkerPopup])

  // Reset marker state
  const resetMarkerState = useCallback(() => {
    setMarkerState(INITIAL_MARKER_STATE)
  }, [])

  // Context value object
  const contextValue = {
    // State
    markerState,
    selectedMarker: markerState.selectedMarker,
    activeMarkerId: markerState.activeMarkerId,
    isMarkerPopupOpen: markerState.isMarkerPopupOpen,
    
    // Actions
    setSelectedMarker,
    setMarkerData,
    getMarkerData,
    clearMarkerData,
    toggleMarkerPopup,
    handleMarkerClick,
    resetMarkerState,
  }

  return (
    <MarkerContext.Provider value={contextValue}>
      {children}
    </MarkerContext.Provider>
  )
}

/**
 * useMarkerContext - Hook to access marker context
 * Throws error if used outside of MarkerContextProvider
 */
export function useMarkerContext() {
  try {
    const context = useContext(MarkerContext)

    if (!context) {
      console.error('useMarkerContext must be used within a MarkerContextProvider')
      // Return safe fallback instead of throwing
      return {
        markerState: {
          selectedMarker: null,
          activeMarkerId: null,
          markerData: {},
          isMarkerPopupOpen: false,
        },
        selectedMarker: null,
        activeMarkerId: null,
        isMarkerPopupOpen: false,
        setSelectedMarker: () => console.warn('MarkerContext not available'),
        setMarkerData: () => console.warn('MarkerContext not available'),
        getMarkerData: () => null,
        clearMarkerData: () => console.warn('MarkerContext not available'),
        toggleMarkerPopup: () => console.warn('MarkerContext not available'),
        handleMarkerClick: () => console.warn('MarkerContext not available'),
        resetMarkerState: () => console.warn('MarkerContext not available'),
      }
    }

    return context
  } catch (error) {
    console.error('Error in useMarkerContext:', error);
    // Return safe fallback
    return {
      markerState: {
        selectedMarker: null,
        activeMarkerId: null,
        markerData: {},
        isMarkerPopupOpen: false,
      },
      selectedMarker: null,
      activeMarkerId: null,
      isMarkerPopupOpen: false,
      setSelectedMarker: () => console.warn('MarkerContext error fallback'),
      setMarkerData: () => console.warn('MarkerContext error fallback'),
      getMarkerData: () => null,
      clearMarkerData: () => console.warn('MarkerContext error fallback'),
      toggleMarkerPopup: () => console.warn('MarkerContext error fallback'),
      handleMarkerClick: () => console.warn('MarkerContext error fallback'),
      resetMarkerState: () => console.warn('MarkerContext error fallback'),
    }
  }
}

/**
 * withMarkerContext - HOC to wrap components with MarkerContextProvider
 * Useful for components that need marker context but aren't wrapped at app level
 */
export function withMarkerContext(Component) {
  return function WrappedComponent(props) {
    return (
      <MarkerContextProvider>
        <Component {...props} />
      </MarkerContextProvider>
    )
  }
}

// Export context for advanced usage
export { MarkerContext }
