export const INITIAL_EXPERIENCE_STATE = {
    showNavbar: false,
    showMenu: false,
    showPopup: false,
    showPopupVideo: false,
    showInfoDoc: false,
    showPopupGeneral: false,
    showBookingPopup: false,
    showGalleryStore: false,
    showVideoGallery: false,
    showVideo: false,
    showSingleVideoGallery: false,
    showItemInfo: {},
    showVideoInfo: {},
    showTheIslandPage:false,
    showExperiencePage:false,
    toggleSound:false,
    showTestimonials:false,
    showLocationAndContacts:false,
    landingPageVideo:true,
};

export const ACTIONS_EXPERIENCE_STATE = {
    LANDING_PAGE_VIDEO_TOGGLE: 'LANDING_PAGE_VIDEO_TOGGLE',
    TOOGLE_SOUND: 'SHOW_NAVBAR',
    SHOW_NAVBAR: 'SHOW_NAVBAR',
    SHOW_ISLAND_PAGE: 'SHOW_ISLAND_PAGE',
    SHOW_EXPERIENCE_PAGE: 'SHOW_EXPERIENCE_PAGE',
    SHOW_TESTIMONIALS_PAGE: 'SHOW_TESTIMONIALS_PAGE',
    SHOW_LOCATION_AND_CONTACTS_PAGE: 'SHOW_LOCATION_AND_CONTACTS_PAGE',
    MENU_TOGGLE: 'MENU_TOGGLE',
    POPUP_TOGGLE: 'POPUP_TOGGLE',
    POPUP_BOOKING_TOGGLE: 'POPUP_BOOKING_TOGGLE',
    POPUP_STORE_TOGGLE: 'POPUP_STORE_TOGGLE',
    POPUP_VIDOE_GALLERY_TOGGLE: 'POPUP_VIDOE_GALLERY_TOGGLE',
    POPUP_ITEM_ARTICLE_TOGGLE: 'POPUP_ITEM_ARTICLE_TOGGLE',
    POPUP_SINGLE_VIDOE_GALLERY_TOGGLE: 'POPUP_SINGLE_VIDOE_GALLERY_TOGGLE',
    CLOSE_PAGES: 'CLOSE_PAGES',
    CLOSE_POPUP: 'CLOSE_POPUP',
    RESET: 'RESET',
};

export const experienceReducer = (state, action) => {
    // Add comprehensive logging for all actions that affect popup states
    if (action.type.includes('POPUP') || action.type === 'RESET' || action.type === 'CLOSE_POPUP') {
        console.log(`🔄 REDUCER ACTION: ${action.type}`, {
            before: {
                showPopupVideo: state.showPopupVideo,
                showPopupGeneral: state.showPopupGeneral,
                showVideoGallery: state.showVideoGallery,
                showSingleVideoGallery: state.showSingleVideoGallery,
                showVideo: state.showVideo
            },
            payload: action.payload
        });
    }

    switch (action.type) {
      case 'SHOW_NAVBAR':
        return { 
          ...state, 
          showNavbar: true,
        };
      case 'LANDING_PAGE_VIDEO_TOGGLE':
        return { 
          ...state, 
          landingPageVideo: false,
        };
      case 'TOOGLE_SOUND':
        return {
          ...state,
          toggleSound: !state.toggleSound,
        };
      case 'SHOW_ISLAND_PAGE':
        return { 
          ...state, 
          showTheIslandPage: !state.showTheIslandPage,
          showExperiencePage:false,
          showTestimonials:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_EXPERIENCE_PAGE':
        return { 
          ...state, 
          showExperiencePage: !state.showExperiencePage,
          showTheIslandPage:false,
          showTestimonials:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_TESTIMONIALS_PAGE':
        return { 
          ...state, 
          showTestimonials: !state.showTestimonials,
          showTheIslandPage:false,
          showExperiencePage:false,
          showLocationAndContacts:false,  
        };
      case 'SHOW_LOCATION_AND_CONTACTS_PAGE':
        return { 
          ...state, 
          showLocationAndContacts: !state.showLocationAndContacts,
          showTheIslandPage:false,
          showExperiencePage:false,
          showTestimonials:false, 
        };
      case 'CLOSE_PAGES':
        return { 
          ...state, 
          showTheIslandPage:false,
          showExperiencePage:false,
          showTestimonials:false,
          showLocationAndContacts:false, 
        };
      case 'CLOSE_POPUP':
        return { 
          ...state, 
          showPopupVideo: false,
          showPopupGeneral: false,
          showSingleVideoGallery:false,    
        };
      case 'MENU_TOGGLE':
        return { 
          ...state, 
          showMenu: !state.showMenu,
          showPopup:false
        };
      case 'POPUP_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup  
        };
      case 'POPUP_BOOKING_TOGGLE':
        return { 
          ...state, 
          // showBookingPopup: true,
          showBookingPopup: !state.showBookingPopup,
        };
      case 'POPUP_STORE_TOGGLE':
        return { 
          ...state, 
          showPopup: !state.showPopup,
          showPopupGeneral:true,
          showGalleryStore: true,
          showVideoGallery: false,
          showVideo: false,
          showSingleVideoGallery: false,   
          // showItemInfo: false, 
          showMenu:false   
        };
      case 'POPUP_VIDOE_GALLERY_TOGGLE':
        const videoGalleryState = {
          ...state,
          // showPopup: !state.showPopup,
          showVideoGallery: true,
          showGalleryStore: false,
          showPopupVideo:true,
          showPopupGeneral:false,
          // showItemInfo: false,
          showSingleVideoGallery: false,
          showVideo: true,
          showMenu:false
        };
        console.log('🎥 POPUP_VIDOE_GALLERY_TOGGLE - After:', {
          showPopupVideo: videoGalleryState.showPopupVideo,
          showPopupGeneral: videoGalleryState.showPopupGeneral,
          showVideoGallery: videoGalleryState.showVideoGallery,
          showVideo: videoGalleryState.showVideo
        });
        return videoGalleryState;
      case 'POPUP_SINGLE_VIDOE_GALLERY_TOGGLE':
        const singleVideoState = {
          ...state,
          showPopup: !state.showPopup,
          showVideoGallery: false,
          showGalleryStore: false,
          showPopupVideo:true,
          showPopupGeneral:false,
          // showItemInfo: false,
          showSingleVideoGallery: true,
          showVideoInfo: action.payload,
          showVideo: true,
          showMenu:false
        };
        console.log('🎬 POPUP_SINGLE_VIDOE_GALLERY_TOGGLE - After:', {
          showPopupVideo: singleVideoState.showPopupVideo,
          showPopupGeneral: singleVideoState.showPopupGeneral,
          showSingleVideoGallery: singleVideoState.showSingleVideoGallery,
          showVideo: singleVideoState.showVideo
        });
        return singleVideoState;
      case 'POPUP_ITEM_ARTICLE_TOGGLE':
        console.log('POPUP_ITEM_ARTICLE_TOGGLE - Before:', {
          showPopupGeneral: state.showPopupGeneral,
          showPopupVideo: state.showPopupVideo,
          showBookingPopup: state.showBookingPopup,
          showItemInfo: state.showItemInfo,
          payload: action.payload
        });
        const newArticleState = {
          ...state,
          showPopup: !state.showPopup,
          showPopupGeneral: true,
          showPopupVideo: false,
          showBookingPopup: false, // Reset booking popup to prevent conflicts
          showItemInfo: {showItem: true, id: action.payload},
          showGalleryStore: false,
          showVideoGallery: false,
          showSingleVideoGallery: false,
          showVideo: false,
          showMenu: false
        };
        console.log('POPUP_ITEM_ARTICLE_TOGGLE - After:', {
          showPopupGeneral: newArticleState.showPopupGeneral,
          showPopupVideo: newArticleState.showPopupVideo,
          showBookingPopup: newArticleState.showBookingPopup,
          showItemInfo: newArticleState.showItemInfo
        });
        return newArticleState;
      case 'RESET':
        return { 
          ...state, 
          // showNavbar: false,
          // showMenu: false,
          // // showPopup: false,
          // showBookingPopup: false,
          // showGalleryStore: false,
          // showVideoGallery: false,
          // showSingleVideoGallery: false,
          // showItemInfo: {}, 
          // showVideoInfo: false,
          // showVideo:false
        };
      default:
        return state;
    }
  };