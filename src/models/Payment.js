import mongoose from 'mongoose';

const PaymentSchema = new mongoose.Schema({
  // Payment Reference
  paymentId: {
    type: String,
    required: true,
    unique: true,
  },
  
  // Related Booking
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true,
  },
  
  // Customer Information
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  
  // Payment Details
  amount: {
    type: Number,
    required: true,
    min: [0, 'Amount cannot be negative'],
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true,
  },
  
  // Payment Method
  paymentMethod: {
    type: {
      type: String,
      enum: ['card', 'bank_transfer', 'cash', 'check', 'other'],
      required: true,
    },
    details: {
      // For card payments
      last4: String,
      brand: String, // visa, mastercard, etc.
      
      // For bank transfers
      bankName: String,
      accountLast4: String,
      
      // For other methods
      description: String,
    },
  },
  
  // Stripe Integration
  stripe: {
    paymentIntentId: String,
    chargeId: String,
    customerId: String,
    paymentMethodId: String,
    setupIntentId: String,
  },
  
  // Payment Status
  status: {
    type: String,
    enum: [
      'pending',
      'processing',
      'succeeded',
      'failed',
      'cancelled',
      'refunded',
      'partially_refunded',
      'disputed',
      'chargeback'
    ],
    default: 'pending',
  },
  
  // Payment Type
  type: {
    type: String,
    enum: ['payment', 'refund', 'partial_refund', 'chargeback', 'fee'],
    default: 'payment',
  },
  
  // Timing
  processedAt: Date,
  failedAt: Date,
  refundedAt: Date,
  
  // Fees and Charges
  fees: {
    stripeFee: {
      type: Number,
      default: 0,
    },
    processingFee: {
      type: Number,
      default: 0,
    },
    totalFees: {
      type: Number,
      default: 0,
    },
  },
  
  // Net Amount (amount minus fees)
  netAmount: {
    type: Number,
    required: true,
  },
  
  // Refund Information
  refund: {
    amount: {
      type: Number,
      default: 0,
    },
    reason: String,
    refundedAt: Date,
    refundId: String, // Stripe refund ID
    status: {
      type: String,
      enum: ['pending', 'succeeded', 'failed', 'cancelled'],
    },
  },
  
  // Dispute Information
  dispute: {
    amount: Number,
    reason: String,
    status: {
      type: String,
      enum: ['warning_needs_response', 'warning_under_review', 'warning_closed', 'needs_response', 'under_review', 'charge_refunded', 'won', 'lost'],
    },
    evidence: {
      customerCommunication: String,
      receipt: String,
      shippingDocumentation: String,
      uncategorizedText: String,
    },
    disputeId: String,
    createdAt: Date,
  },
  
  // Failure Information
  failure: {
    code: String,
    message: String,
    declineCode: String,
    networkStatus: String,
    reason: String,
    riskLevel: String,
    riskScore: Number,
    sellerMessage: String,
    type: String,
  },
  
  // Billing Address
  billingAddress: {
    name: String,
    line1: String,
    line2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String,
  },
  
  // Receipt Information
  receipt: {
    email: String,
    url: String,
    number: String,
  },
  
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: String, // website, mobile_app, admin_panel
    notes: String,
  },
  
  // Audit Trail
  auditTrail: [{
    action: {
      type: String,
      enum: ['created', 'processed', 'failed', 'refunded', 'disputed', 'updated'],
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    details: String,
    previousStatus: String,
    newStatus: String,
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
// Note: paymentId index is automatically created by unique: true in schema definition
PaymentSchema.index({ booking: 1 });
PaymentSchema.index({ customer: 1 });
PaymentSchema.index({ status: 1 });
PaymentSchema.index({ type: 1 });
PaymentSchema.index({ 'stripe.paymentIntentId': 1 });
PaymentSchema.index({ 'stripe.chargeId': 1 });
PaymentSchema.index({ createdAt: -1 });
PaymentSchema.index({ processedAt: -1 });

// Virtual for payment success
PaymentSchema.virtual('isSuccessful').get(function() {
  return this.status === 'succeeded';
});

// Virtual for payment failed
PaymentSchema.virtual('isFailed').get(function() {
  return ['failed', 'cancelled'].includes(this.status);
});

// Virtual for refund amount
PaymentSchema.virtual('refundAmount').get(function() {
  return this.refund.amount || 0;
});

// Pre-save middleware to generate payment ID
PaymentSchema.pre('save', async function(next) {
  if (this.isNew && !this.paymentId) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last payment ID for today
    const lastPayment = await this.constructor.findOne({
      paymentId: new RegExp(`^PAY${year}${month}${day}`),
    }).sort({ paymentId: -1 });
    
    let sequence = 1;
    if (lastPayment) {
      const lastSequence = parseInt(lastPayment.paymentId.slice(-4));
      sequence = lastSequence + 1;
    }
    
    this.paymentId = `PAY${year}${month}${day}${sequence.toString().padStart(4, '0')}`;
  }
  
  // Calculate net amount
  if (this.isModified('amount') || this.isModified('fees')) {
    this.netAmount = this.amount - (this.fees.totalFees || 0);
  }
  
  // Add audit trail entry
  if (this.isModified('status')) {
    const auditEntry = {
      action: this.isNew ? 'created' : 'updated',
      previousStatus: this.isModified('status') ? this._original?.status : undefined,
      newStatus: this.status,
      details: `Payment status changed to ${this.status}`,
    };
    
    this.auditTrail.push(auditEntry);
  }
  
  next();
});

// Pre-save middleware to store original values for audit trail
PaymentSchema.pre('save', function(next) {
  if (!this.isNew) {
    this._original = this.toObject();
  }
  next();
});

// Instance method to process payment
PaymentSchema.methods.markAsProcessed = function() {
  this.status = 'succeeded';
  this.processedAt = new Date();
  return this.save();
};

// Instance method to mark as failed
PaymentSchema.methods.markAsFailed = function(failureDetails) {
  this.status = 'failed';
  this.failedAt = new Date();
  if (failureDetails) {
    this.failure = failureDetails;
  }
  return this.save();
};

// Instance method to process refund
PaymentSchema.methods.processRefund = function(refundAmount, reason) {
  this.refund = {
    amount: refundAmount,
    reason: reason,
    refundedAt: new Date(),
    status: 'succeeded',
  };
  
  if (refundAmount >= this.amount) {
    this.status = 'refunded';
  } else {
    this.status = 'partially_refunded';
  }
  
  this.refundedAt = new Date();
  return this.save();
};

// Static method to find payments by date range
PaymentSchema.statics.findByDateRange = function(startDate, endDate) {
  return this.find({
    createdAt: {
      $gte: startDate,
      $lte: endDate,
    },
  });
};

// Static method to get payment statistics
PaymentSchema.statics.getStats = async function(startDate, endDate) {
  const pipeline = [
    {
      $match: {
        createdAt: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalFees: { $sum: '$fees.totalFees' },
        totalNet: { $sum: '$netAmount' },
      },
    },
  ];
  
  return this.aggregate(pipeline);
};

export const Payment = mongoose.models.Payment || mongoose.model('Payment', PaymentSchema);
