import mongoose from 'mongoose';

const ContactSchema = new mongoose.Schema({
  // Contact Information
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
  },
  message: {
    type: String,
    required: [true, 'Message is required'],
    trim: true,
    minlength: [10, 'Message must be at least 10 characters long'],
    maxlength: [5000, 'Message cannot exceed 5000 characters'],
  },
  
  // Email Status
  emailSent: {
    type: Boolean,
    default: false,
  },
  emailSentAt: {
    type: Date,
    default: null,
  },
  emailMessageId: {
    type: String,
    default: null,
  },
  emailError: {
    type: String,
    default: null,
  },
  
  // Status and Processing
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'resolved', 'archived'],
    default: 'new',
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
  },
  
  // Response Information
  respondedAt: {
    type: Date,
    default: null,
  },
  respondedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  response: {
    type: String,
    default: null,
  },
  
  // Internal Notes
  internalNotes: [{
    content: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    referrer: String,
    source: {
      type: String,
      default: 'website_contact_form',
    },
  },
  
  // Tags for categorization
  tags: [{
    type: String,
    trim: true,
  }],
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better query performance
ContactSchema.index({ status: 1, createdAt: -1 });
ContactSchema.index({ email: 1 });
ContactSchema.index({ emailSent: 1 });
ContactSchema.index({ priority: 1, status: 1 });

// Virtual for full contact info
ContactSchema.virtual('fullContactInfo').get(function() {
  return `${this.name} <${this.email}>`;
});

// Method to mark as read
ContactSchema.methods.markAsRead = function() {
  this.status = 'read';
  return this.save();
};

// Method to mark as replied
ContactSchema.methods.markAsReplied = function(respondedBy, response) {
  this.status = 'replied';
  this.respondedAt = new Date();
  this.respondedBy = respondedBy;
  this.response = response;
  return this.save();
};

// Method to add internal note
ContactSchema.methods.addInternalNote = function(content, author) {
  this.internalNotes.push({
    content,
    author,
    createdAt: new Date(),
  });
  return this.save();
};

// Static method to get unread count
ContactSchema.statics.getUnreadCount = function() {
  return this.countDocuments({ status: 'new' });
};

// Static method to get pending email count
ContactSchema.statics.getPendingEmailCount = function() {
  return this.countDocuments({ emailSent: false });
};

export default mongoose.models.Contact || mongoose.model('Contact', ContactSchema);
