import mongoose from 'mongoose';
const { Schema } = mongoose;

const videoGallerySchema = new Schema({
    title: { type: String, required: true, default: '' },
    description: { type: String, required: true, default: '' },
    url: { type: String, required: true, default: '' },
    thumbnail: { type: String, required: true, default: '' },
}, { timestamps: true });

// Use existing model if it exists, otherwise create a new one
export const VideoGallery = mongoose.models.VideoGallery || mongoose.model('VideoGallery', videoGallerySchema);