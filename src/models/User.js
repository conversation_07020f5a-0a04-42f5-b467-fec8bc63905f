import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  // Name fields - supporting both new and legacy structure
  firstname: {
    type: String,
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters'],
  },
  surname: {
    type: String,
    trim: true,
    maxlength: [50, 'Surname cannot exceed 50 characters'],
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
  },
  // Password field removed - no authentication required
  image: {
    type: String,
    default: null,
  },
  role: {
    type: String,
    enum: ['guest', 'user', 'manager', 'admin'],
    default: 'user',
  },
  // Provider field removed - no authentication required
  isGuest: {
    type: <PERSON>olean,
    default: false,
  },
  // Email verification and password reset removed - no authentication required
  // Contact Information
  phone: {
    type: String,
    trim: true,
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
  },
  // Emergency Contact
  emergencyContact: {
    firstname: String,
    surname: String,
    name: String, // Legacy support
    phone: String,
    relationship: String,
  },
  // Preferences
  preferences: {
    newsletter: {
      type: Boolean,
      default: true,
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      sms: {
        type: Boolean,
        default: false,
      },
    },
    dietary: [String], // dietary restrictions
    accessibility: [String], // accessibility needs
  },
  // Authentication tracking removed - no authentication required
  // Account Status
  isActive: {
    type: Boolean,
    default: true,
  },
  isBlocked: {
    type: Boolean,
    default: false,
  },
  // Notes (for staff use)
  notes: [{
    content: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
// Note: email index is automatically created by unique: true in schema definition
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ lastLogin: -1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return this.name;
});

// Virtual for role level (for permission checking)
UserSchema.virtual('roleLevel').get(function() {
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  return roleLevels[this.role] || 0;
});

// Password-related methods removed - no authentication required

// Instance method to check role permission
UserSchema.methods.hasRole = function(requiredRole) {
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  
  const userLevel = roleLevels[this.role] || 0;
  const requiredLevel = roleLevels[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
};

// Static method to create guest user (simplified - no authentication)
UserSchema.statics.createGuestUser = async function(guestData) {
  // Handle both new firstname/surname structure and legacy name field
  const userData = {
    email: guestData.email,
    phone: guestData.phone,
    role: 'guest',
    isGuest: true,
    address: guestData.address,
    emergencyContact: guestData.emergencyContact,
  };

  // Set name fields based on available data
  if (guestData.firstname && guestData.surname) {
    userData.firstname = guestData.firstname;
    userData.surname = guestData.surname;
    userData.name = `${guestData.firstname} ${guestData.surname}`;
  } else if (guestData.name) {
    userData.name = guestData.name;
    // Try to split name into firstname and surname
    const nameParts = guestData.name.trim().split(' ');
    if (nameParts.length >= 2) {
      userData.firstname = nameParts[0];
      userData.surname = nameParts.slice(1).join(' ');
    } else {
      userData.firstname = nameParts[0];
      userData.surname = '';
    }
  }

  const guestUser = new this(userData);
  return await guestUser.save();
};

// Guest promotion method removed - no authentication required

export const User = mongoose.models.User || mongoose.model('User', UserSchema);
