import mongoose from 'mongoose';
const { Schema } = mongoose;

const HeroVideoSchema = new Schema({
    name:{type:String,required:true},
    url:{type:String,required:true},
    fullPath:{type:String,default:''},
    contentType:{type:String,default:'video/mp4'},
    size:{type:Number,default:0},
    uploadedAt:{type:String,default:''},
    isActive:{type:Boolean,default:false}
},{timestamps:true});

export const HeroVideo = mongoose.models.HeroVideo||mongoose.model('HeroVideo', HeroVideoSchema)