import mongoose from 'mongoose';
const { Schema } = mongoose;

// Define marker schema for proper validation
const MarkerSchema = new Schema({
    name: { type: String, required: true },
    markerType: {
        type: String,
        required: true,
        enum: ['landingPage', 'guide', 'upstairs', 'downstairs', 'infoVideo', 'infoDoc', 'infoImage']
    },
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 },
    // Navigation markers use _360Name
    _360Name: { type: String, default: '' },
    // Content markers use id
    id: { type: String, default: '' },
    // InfoVideo markers additional properties
    videoName: { type: String, default: '' },
    infoVideo: { type: Object, default: null } // Store complete video object
}, { _id: false }); // Disable _id for subdocuments

const _360Schema = new Schema({
    name:{type:String,required:true}, // Stores filename without extension
    title:{type:String,default:''}, // Display title for the 360° image
    originalFileName:{type:String,default:''}, // Stores the original filename with extension
    url:{type:String,required:true}, // Firebase Storage URL
    priority:{type:Number,default:0},
    markerList:{type:[MarkerSchema],default:[]}, // Use proper schema validation
    cameraPosition:{type:Number,default:-0.0001},
    _360Rotation:{type:Number,default:-0.0001},
},{timestamps:true});

// Clear any existing model cache to ensure we use the latest schema
if (mongoose.models._360Settings) {
  delete mongoose.models._360Settings;
}

export const _360Settings = mongoose.model('_360Settings', _360Schema)