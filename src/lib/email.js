import nodemailer from 'nodemailer';

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_SERVER_HOST,
    port: parseInt(process.env.EMAIL_SERVER_PORT),
    secure: process.env.EMAIL_SERVER_PORT === '465', // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
    tls: {
      // Do not fail on invalid certs
      rejectUnauthorized: false,
    },
  });
};

/**
 * Send an email using the configured SMTP settings
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email address
 * @param {string} options.subject - Email subject
 * @param {string} options.text - Plain text content (optional)
 * @param {string} options.html - HTML content (optional)
 * @param {string} options.from - Sender email (optional, uses default)
 * @returns {Promise} - Promise that resolves when email is sent
 */
export async function sendEmail({ to, subject, text, html, from }) {
  try {
    const transporter = createTransporter();

    // Verify transporter configuration
    await transporter.verify();

    const mailOptions = {
      from: from || `${process.env.EMAIL_FROM_NAME || 'Elephant Island Lodge'} <${process.env.EMAIL_FROM}>`,
      to,
      subject,
      text,
      html,
    };

    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', {
      messageId: info.messageId,
      to,
      subject,
    });

    return {
      success: true,
      messageId: info.messageId,
    };

  } catch (error) {
    console.error('Email sending failed:', error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
}

/**
 * Send welcome email to new users
 * @param {Object} user - User object
 * @returns {Promise}
 */
export async function sendWelcomeEmail(user) {
  const subject = 'Welcome to Elephant Island Lodge!';
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #3B82F6; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">Welcome to Elephant Island Lodge!</h1>
      </div>
      
      <div style="padding: 30px 20px;">
        <h2 style="color: #333;">Hello ${user.name}!</h2>
        
        <p>Thank you for creating an account with Elephant Island Lodge. We're excited to have you join our community!</p>
        
        <p>Your account has been successfully created with the following details:</p>
        <ul style="background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
          <li><strong>Name:</strong> ${user.name}</li>
          <li><strong>Email:</strong> ${user.email}</li>
          <li><strong>Account Type:</strong> ${user.role}</li>
        </ul>
        
        <p>You can now:</p>
        <ul>
          <li>Browse our 360° virtual tours</li>
          <li>Make bookings for your stay</li>
          <li>Manage your profile and preferences</li>
          <li>Receive updates about our services</li>
        </ul>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/auth/signin" 
             style="background-color: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Sign In to Your Account
          </a>
        </div>
        
        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
        
        <p>Welcome aboard!</p>
        <p><strong>The Elephant Island Lodge Team</strong></p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
        <p>This email was sent from Elephant Island Lodge.</p>
        <p>If you did not create this account, please contact our support team immediately.</p>
      </div>
    </div>
  `;

  const text = `
    Welcome to Elephant Island Lodge!
    
    Hello ${user.name}!
    
    Thank you for creating an account with Elephant Island Lodge. We're excited to have you join our community!
    
    Your account has been successfully created with the following details:
    - Name: ${user.name}
    - Email: ${user.email}
    - Account Type: ${user.role}
    
    You can now:
    - Browse our 360° virtual tours
    - Make bookings for your stay
    - Manage your profile and preferences
    - Receive updates about our services
    
    Sign in to your account: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/auth/signin
    
    If you have any questions or need assistance, please don't hesitate to contact our support team.
    
    Welcome aboard!
    The Elephant Island Lodge Team
  `;

  return await sendEmail({
    to: user.email,
    subject,
    text,
    html,
  });
}

/**
 * Send booking confirmation email
 * @param {Object} booking - Booking object
 * @param {Object} user - User object
 * @returns {Promise}
 */
export async function sendBookingConfirmationEmail(booking, user) {
  const subject = `Booking Confirmation - ${booking.bookingNumber}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #10B981; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">Booking Confirmed!</h1>
      </div>
      
      <div style="padding: 30px 20px;">
        <h2 style="color: #333;">Hello ${user.name}!</h2>
        
        <p>Your booking has been confirmed! Here are your booking details:</p>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #333;">Booking Details</h3>
          <p><strong>Booking Number:</strong> ${booking.bookingNumber}</p>
          <p><strong>Package:</strong> ${booking.package?.name || 'N/A'}</p>
          <p><strong>Check-in:</strong> ${new Date(booking.dates.checkIn).toLocaleDateString()}</p>
          <p><strong>Check-out:</strong> ${new Date(booking.dates.checkOut).toLocaleDateString()}</p>
          <p><strong>Total Amount:</strong> $${booking.pricing.totalAmount}</p>
          <p><strong>Status:</strong> ${booking.status}</p>
        </div>
        
        <p>We look forward to welcoming you to Elephant Island Lodge!</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/booking-confirmation/${booking._id}" 
             style="background-color: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            View Booking Details
          </a>
        </div>
        
        <p>If you have any questions about your booking, please contact us.</p>
        
        <p><strong>The Elephant Island Lodge Team</strong></p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
        <p>This email was sent from Elephant Island Lodge.</p>
        <p>Booking Reference: ${booking.bookingNumber}</p>
      </div>
    </div>
  `;

  return await sendEmail({
    to: user.email,
    subject,
    html,
  });
}

/**
 * Test email configuration
 * @returns {Promise}
 */
export async function testEmailConfiguration() {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    return { success: true, message: 'Email configuration is valid' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}
