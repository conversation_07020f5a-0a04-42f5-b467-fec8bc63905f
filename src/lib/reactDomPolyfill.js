/**
 * React DOM findDOMNode polyfill for React 19 compatibility
 * This polyfill provides a findDOMNode implementation for libraries that still depend on it
 */

let polyfillApplied = false;

export function applyReactDomPolyfill() {
  if (polyfillApplied || typeof window === 'undefined') {
    return;
  }

  try {
    // Import ReactDOM dynamically
    import('react-dom').then((ReactDOM) => {
      // Check if findDOMNode already exists
      if (ReactDOM.findDOMNode) {
        console.log('ReactDOM.findDOMNode already exists, skipping polyfill');
        return;
      }

      console.log('Applying ReactDOM.findDOMNode polyfill for React 19 compatibility');

      // Create a comprehensive findDOMNode polyfill
      const findDOMNodePolyfill = (instance) => {
        // Handle null/undefined
        if (instance == null) {
          return null;
        }

        // If it's already a DOM element, return it
        if (instance.nodeType === 1) {
          return instance;
        }

        // Handle React 19 refs
        if (instance.current) {
          if (instance.current.nodeType === 1) {
            return instance.current;
          }
          // Try to get the DOM node from the current ref
          if (instance.current._owner && instance.current._owner.stateNode) {
            return instance.current._owner.stateNode;
          }
        }

        // Handle React Fiber (React 16+)
        if (instance._reactInternalFiber) {
          const fiber = instance._reactInternalFiber;
          if (fiber.stateNode && fiber.stateNode.nodeType === 1) {
            return fiber.stateNode;
          }
          // Walk up the fiber tree to find a DOM node
          let currentFiber = fiber;
          while (currentFiber) {
            if (currentFiber.stateNode && currentFiber.stateNode.nodeType === 1) {
              return currentFiber.stateNode;
            }
            currentFiber = currentFiber.child || currentFiber.sibling || currentFiber.return;
          }
        }

        // Handle React 18+ internal instance
        if (instance._reactInternalInstance) {
          const internalInstance = instance._reactInternalInstance;
          if (internalInstance._renderedComponent && internalInstance._renderedComponent._hostNode) {
            return internalInstance._renderedComponent._hostNode;
          }
          if (internalInstance._hostNode) {
            return internalInstance._hostNode;
          }
        }

        // Try to find DOM node through React DevTools fiber
        if (instance._reactInternals) {
          const internals = instance._reactInternals;
          if (internals.stateNode && internals.stateNode.nodeType === 1) {
            return internals.stateNode;
          }
        }

        // Handle class components with render method
        if (instance.render && typeof instance.render === 'function') {
          // Try to get the DOM node from the component's state
          if (instance.state && instance.state._domNode) {
            return instance.state._domNode;
          }
          // Try to get from refs
          if (instance.refs) {
            const refKeys = Object.keys(instance.refs);
            for (const key of refKeys) {
              const ref = instance.refs[key];
              if (ref && ref.nodeType === 1) {
                return ref;
              }
            }
          }
        }

        // Handle function components or hooks
        if (typeof instance === 'object' && instance.constructor) {
          // Try to find DOM element in the instance properties
          const keys = Object.keys(instance);
          for (const key of keys) {
            const value = instance[key];
            if (value && value.nodeType === 1) {
              return value;
            }
          }
        }

        // Last resort: try to find any DOM element in the instance
        if (typeof instance === 'object') {
          for (const key in instance) {
            try {
              const value = instance[key];
              if (value && typeof value === 'object' && value.nodeType === 1) {
                return value;
              }
            } catch (e) {
              // Ignore errors when accessing properties
            }
          }
        }

        // If all else fails, return null
        console.warn('findDOMNode polyfill: Could not find DOM node for instance:', instance);
        return null;
      };

      // Apply the polyfill
      try {
        // Method 1: Try Object.defineProperty
        Object.defineProperty(ReactDOM, 'findDOMNode', {
          value: findDOMNodePolyfill,
          writable: true,
          configurable: true,
          enumerable: false
        });
        console.log('ReactDOM.findDOMNode polyfill applied successfully via defineProperty');
      } catch (defineError) {
        try {
          // Method 2: Direct assignment
          ReactDOM.findDOMNode = findDOMNodePolyfill;
          console.log('ReactDOM.findDOMNode polyfill applied successfully via direct assignment');
        } catch (assignError) {
          try {
            // Method 3: Try to modify the default export
            ReactDOM.default.findDOMNode = findDOMNodePolyfill;
            console.log('ReactDOM.findDOMNode polyfill applied successfully via default export');
          } catch (defaultError) {
            console.error('Failed to apply ReactDOM.findDOMNode polyfill:', {
              defineError,
              assignError,
              defaultError
            });
          }
        }
      }

      polyfillApplied = true;
    }).catch((importError) => {
      console.error('Failed to import ReactDOM for polyfill:', importError);
    });
  } catch (error) {
    console.error('Error applying ReactDOM polyfill:', error);
  }
}

// Auto-apply the polyfill when this module is imported
if (typeof window !== 'undefined') {
  applyReactDomPolyfill();
}

export default applyReactDomPolyfill;
