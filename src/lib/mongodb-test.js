import { MongoClient } from 'mongodb';
import mongoose from 'mongoose';

// Test MongoDB connection with different strategies
export async function testMongoDBConnection() {
  const MONGODB_URI = process.env.MONGODB_URI;
  
  if (!MONGODB_URI) {
    throw new Error('MONGODB_URI environment variable is not defined');
  }

  console.log('Testing MongoDB connection...');
  console.log('URI:', MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')); // Hide credentials

  const tests = [];

  // Test 1: Direct MongoClient connection
  try {
    console.log('\n1. Testing direct MongoClient connection...');
    const client = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      family: 4, // Force IPv4
    });
    
    await client.connect();
    await client.db().admin().ping();
    console.log('✅ Direct MongoClient connection successful');
    await client.close();
    tests.push({ test: 'MongoClient', status: 'success' });
  } catch (error) {
    console.log('❌ Direct MongoClient connection failed:', error.message);
    tests.push({ test: 'MongoClient', status: 'failed', error: error.message });
  }

  // Test 2: Mongoose connection
  try {
    console.log('\n2. Testing Mongoose connection...');
    await mongoose.connect(MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      family: 4, // Force IPv4
    });
    console.log('✅ Mongoose connection successful');
    await mongoose.disconnect();
    tests.push({ test: 'Mongoose', status: 'success' });
  } catch (error) {
    console.log('❌ Mongoose connection failed:', error.message);
    tests.push({ test: 'Mongoose', status: 'failed', error: error.message });
  }

  // Test 3: Alternative connection string format
  try {
    console.log('\n3. Testing alternative connection format...');
    // Try with different connection options
    const altClient = new MongoClient(MONGODB_URI, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      family: 4,
      useUnifiedTopology: true,
    });
    
    await altClient.connect();
    await altClient.db().admin().ping();
    console.log('✅ Alternative connection format successful');
    await altClient.close();
    tests.push({ test: 'Alternative', status: 'success' });
  } catch (error) {
    console.log('❌ Alternative connection format failed:', error.message);
    tests.push({ test: 'Alternative', status: 'failed', error: error.message });
  }

  // Test 4: DNS resolution test
  try {
    console.log('\n4. Testing DNS resolution...');
    const dns = require('dns').promises;
    const hostname = 'appsdb.3ujo1.mongodb.net';
    const addresses = await dns.resolve4(hostname);
    console.log('✅ DNS resolution successful:', addresses);
    tests.push({ test: 'DNS', status: 'success', addresses });
  } catch (error) {
    console.log('❌ DNS resolution failed:', error.message);
    tests.push({ test: 'DNS', status: 'failed', error: error.message });
  }

  return tests;
}

// Alternative connection string generator
export function generateAlternativeConnectionString() {
  const original = process.env.MONGODB_URI;
  
  if (!original) return null;

  // Extract components
  const match = original.match(/mongodb\+srv:\/\/([^:]+):([^@]+)@([^\/]+)\/(.+)/);
  
  if (!match) return null;

  const [, username, password, cluster, params] = match;
  
  // Generate alternative formats
  const alternatives = [
    // Standard format with explicit options
    `mongodb+srv://${username}:${password}@${cluster}/${params}&retryWrites=true&w=majority&family=4`,
    
    // With specific server selection timeout
    `mongodb+srv://${username}:${password}@${cluster}/${params}&serverSelectionTimeoutMS=5000&family=4`,
    
    // With connection timeout
    `mongodb+srv://${username}:${password}@${cluster}/${params}&connectTimeoutMS=10000&serverSelectionTimeoutMS=10000`,
  ];

  return alternatives;
}

// Health check function for API routes
export async function mongoHealthCheck() {
  try {
    const client = new MongoClient(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 3000,
      family: 4,
    });
    
    await client.connect();
    const result = await client.db().admin().ping();
    await client.close();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      ping: result
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}
