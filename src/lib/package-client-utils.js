// Client-side package utilities (no server dependencies)

/**
 * Get predefined package categories for form options (client-safe)
 */
export function getPackageCategories() {
  return [
    { value: 'individual', label: 'Individual', slug: 'individual' },
    { value: 'couples', label: 'Couples', slug: 'couples' },
    { value: 'families', label: 'Families', slug: 'families' }
  ];
}

/**
 * Check if a package category is one of the predefined types (client-safe)
 */
export function isPredefinedCategory(category) {
  return ['individual', 'couples', 'families'].includes(category);
}

/**
 * Get package type configuration for UI display (client-safe)
 */
export function getPackageTypeConfig(category) {
  const configs = {
    individual: {
      name: 'Individual',
      icon: '👤',
      description: 'Perfect for solo travelers seeking adventure and tranquility',
      color: 'blue'
    },
    couples: {
      name: 'Couples',
      icon: '💑',
      description: 'Romantic getaway for two with intimate experiences',
      color: 'pink'
    },
    families: {
      name: 'Families',
      icon: '👨‍👩‍👧‍👦',
      description: 'Fun-filled adventure for the whole family',
      color: 'green'
    }
  };
  
  return configs[category] || configs.individual;
}

/**
 * Format currency for display (client-safe)
 */
export function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}

/**
 * Validate booking form data (client-safe)
 */
export function validateBookingForm(formData) {
  const errors = {};
  
  // Required fields
  if (!formData.firstname || formData.firstname.trim().length < 2) {
    errors.firstname = 'First name must be at least 2 characters long';
  }

  if (!formData.surname || formData.surname.trim().length < 2) {
    errors.surname = 'Surname must be at least 2 characters long';
  }
  
  if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (!formData.phone || formData.phone.trim().length < 8) {
    errors.phone = 'Please enter a valid phone number';
  }
  
  if (!formData.category) {
    errors.category = 'Please select a package category';
  }
  
  if (!formData.numberOfGuests || formData.numberOfGuests < 1) {
    errors.numberOfGuests = 'Please specify number of guests (minimum 1)';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Prepare booking data for API submission (client-safe)
 */
export function prepareBookingData(formData, selectedPackage) {
  if (!selectedPackage) {
    throw new Error('No package selected');
  }
  
  return {
    packageId: selectedPackage._id,
    guestInfo: {
      firstname: formData.firstname.trim(),
      surname: formData.surname.trim(),
      name: `${formData.firstname.trim()} ${formData.surname.trim()}`, // Legacy support
      email: formData.email.trim().toLowerCase(),
      phone: formData.phone.trim(),
    },
    guests: {
      adults: parseInt(formData.numberOfGuests) || 1,
      children: 0,
      guestType: formData.category,
    },
    guestDetails: [{
      firstname: formData.firstname.trim(),
      surname: formData.surname.trim(),
      name: `${formData.firstname.trim()} ${formData.surname.trim()}`, // Legacy support
    }],
    checkIn: formData.checkIn || new Date(), // Use selected date or default to today
    checkOut: formData.checkOut || new Date(Date.now() + 24 * 60 * 60 * 1000), // Use selected date or default 1 day
    specialRequests: {
      other: formData.specialRequests || ''
    },
    source: 'website',
  };
}
