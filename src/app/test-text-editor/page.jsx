'use client';

import { useState } from 'react';
import TextEditor from '@/components/TextEditor';

export default function TestTextEditorPage() {
  const [editorContent, setEditorContent] = useState('');

  const handleContentChange = (content) => {
    setEditorContent(content);
  };

  const handleClear = () => {
    setEditorContent('');
  };

  const handleSampleContent = () => {
    const sampleHtml = `<p><strong>Welcome to the Enhanced Rich Text Editor!</strong></p><p><em>This is italic text</em> and this is <span style="color: rgb(230, 0, 0);">red colored text</span>.</p><p>Test the new features:</p><p>• Select text and adjust <strong>line height</strong> from 0.4 to 2.0</p><p>• Convert text to <strong>email links</strong> (select: <EMAIL>)</p><p>• Convert text to <strong>custom URL links</strong> (select: "Click here" then enter any URL)</p><p>• Watch for loading indicators during link conversion</p><p>Try selecting "Visit our website" and convert it to a URL link, then enter https://google.com when prompted. The text will remain "Visit our website" but link to Google.</p><p>This paragraph has multiple lines to better demonstrate line height changes. Select this entire paragraph and try different line height values to see the spacing change in real-time.</p>`;
    setEditorContent(sampleHtml);
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              TextEditor Component Demo
            </h1>
            <p className="text-gray-600">
              Test the enhanced rich text editor with bold, italic, font family, color formatting,
              line height adjustment, and text-to-link conversion features.
            </p>
          </div>

          {/* Control buttons */}
          <div className="mb-6 flex gap-3">
            <button
              onClick={handleSampleContent}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Load Sample Content
            </button>
            <button
              onClick={handleClear}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Clear Content
            </button>
          </div>

          {/* TextEditor Component */}
          <TextEditor
            value={editorContent}
            onChange={handleContentChange}
            placeholder="Start typing your rich text content here..."
            className="w-full"
          />

          {/* Raw HTML Output (for debugging) */}
          <div className="mt-8 p-4 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Raw HTML Output:
            </h4>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap break-all">
              {editorContent || 'No content yet...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
