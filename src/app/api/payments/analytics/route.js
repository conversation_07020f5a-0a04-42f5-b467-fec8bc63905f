import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Package } from '@/models/Package';

// GET /api/payments/analytics - Get payment analytics (no authentication required)
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    let dateFilter = {
      'payment.status': { $exists: true } // Only bookings with payment info
    };

    if (startDate && endDate) {
      dateFilter['payment.paidAt'] = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(period));
      dateFilter['payment.paidAt'] = { $gte: daysAgo };
    }
    
    // Get payment analytics
    const analytics = await Promise.all([
      getPaymentOverview(dateFilter),
      getPaymentTrends(dateFilter),
      getPaymentMethodBreakdown(dateFilter),
      getRefundAnalytics(dateFilter),
      getFailureAnalytics(dateFilter),
      getRevenueByPackage(dateFilter),
    ]);
    
    const [
      overview,
      trends,
      paymentMethods,
      refunds,
      failures,
      revenueByPackage,
    ] = analytics;
    
    return NextResponse.json({
      success: true,
      data: {
        overview,
        trends,
        paymentMethods,
        refunds,
        failures,
        revenueByPackage,
        period: parseInt(period),
        dateRange: {
          start: startDate || dateFilter['payment.paidAt'].$gte,
          end: endDate || new Date(),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching payment analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch payment analytics',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Get payment overview statistics from bookings
async function getPaymentOverview(dateFilter) {
  const overview = await Booking.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$pricing.totalAmount' },
        totalFees: {
          $sum: {
            $add: [
              { $ifNull: ['$payment.stripeFee', 0] },
              { $ifNull: ['$payment.applicationFee', 0] }
            ]
          }
        },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$payment.status', 'paid'] }, 1, 0] },
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$payment.status', 'failed'] }, 1, 0] },
        },
        refundedPayments: {
          $sum: { $cond: [{ $in: ['$payment.status', ['refunded', 'partial']] }, 1, 0] },
        },
        averagePaymentAmount: { $avg: '$pricing.totalAmount' },
      },
    },
  ]);

  const result = overview[0] || {
    totalPayments: 0,
    totalAmount: 0,
    totalFees: 0,
    successfulPayments: 0,
    failedPayments: 0,
    refundedPayments: 0,
    averagePaymentAmount: 0,
  };

  // Calculate net amount
  result.totalNetAmount = result.totalAmount - result.totalFees;

  // Calculate success rate
  result.successRate = result.totalPayments > 0
    ? (result.successfulPayments / result.totalPayments) * 100
    : 0;

  return result;
}

// Get payment trends over time from bookings
async function getPaymentTrends(dateFilter) {
  const trends = await Booking.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: {
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: { $ifNull: ['$payment.paidAt', '$createdAt'] },
            },
          },
          status: '$payment.status',
        },
        count: { $sum: 1 },
        amount: { $sum: '$pricing.totalAmount' },
        fees: {
          $sum: {
            $add: [
              { $ifNull: ['$payment.stripeFee', 0] },
              { $ifNull: ['$payment.applicationFee', 0] }
            ]
          }
        },
      },
    },
    {
      $group: {
        _id: '$_id.date',
        data: {
          $push: {
            status: '$_id.status',
            count: '$count',
            amount: '$amount',
            fees: '$fees',
          },
        },
        totalCount: { $sum: '$count' },
        totalAmount: { $sum: '$amount' },
        totalFees: { $sum: '$fees' },
      },
    },
    { $sort: { _id: 1 } },
  ]);

  return trends;
}

// Get payment method breakdown from bookings
async function getPaymentMethodBreakdown(dateFilter) {
  const breakdown = await Booking.aggregate([
    { $match: { ...dateFilter, 'payment.status': 'paid' } },
    {
      $group: {
        _id: { $ifNull: ['$payment.method', 'card'] },
        count: { $sum: 1 },
        amount: { $sum: '$pricing.totalAmount' },
        averageAmount: { $avg: '$pricing.totalAmount' },
        fees: {
          $sum: {
            $add: [
              { $ifNull: ['$payment.stripeFee', 0] },
              { $ifNull: ['$payment.applicationFee', 0] }
            ]
          }
        },
      },
    },
    { $sort: { count: -1 } },
  ]);

  return breakdown;
}

// Get refund analytics from bookings
async function getRefundAnalytics(dateFilter) {
  const refunds = await Booking.aggregate([
    {
      $match: {
        ...dateFilter,
        'payment.status': { $in: ['refunded', 'partial'] }
      }
    },
    {
      $group: {
        _id: null,
        totalRefunds: { $sum: 1 },
        totalRefundAmount: { $sum: { $ifNull: ['$payment.refundAmount', 0] } },
        averageRefundAmount: { $avg: { $ifNull: ['$payment.refundAmount', 0] } },
      },
    },
  ]);

  return {
    overview: refunds[0] || {
      totalRefunds: 0,
      totalRefundAmount: 0,
      averageRefundAmount: 0,
    },
    byReason: [], // Simplified for now
  };
}

// Get failure analytics from bookings
async function getFailureAnalytics(dateFilter) {
  const failures = await Booking.aggregate([
    { $match: { ...dateFilter, 'payment.status': 'failed' } },
    {
      $group: {
        _id: null,
        totalFailures: { $sum: 1 },
      },
    },
  ]);

  return {
    overview: failures[0] || {
      totalFailures: 0,
      uniqueFailureCodes: [],
    },
    byCode: [], // Simplified for now
  };
}

// Get revenue by package from bookings
async function getRevenueByPackage(dateFilter) {
  const revenueByPackage = await Booking.aggregate([
    { $match: { ...dateFilter, 'payment.status': 'paid' } },
    {
      $lookup: {
        from: 'packages',
        localField: 'package',
        foreignField: '_id',
        as: 'packageInfo',
      },
    },
    { $unwind: '$packageInfo' },
    {
      $group: {
        _id: {
          packageId: '$packageInfo._id',
          packageName: '$packageInfo.name',
          category: '$packageInfo.category',
        },
        totalRevenue: { $sum: '$pricing.totalAmount' },
        totalBookings: { $sum: 1 },
        averageBookingValue: { $avg: '$pricing.totalAmount' },
        totalFees: {
          $sum: {
            $add: [
              { $ifNull: ['$payment.stripeFee', 0] },
              { $ifNull: ['$payment.applicationFee', 0] }
            ]
          }
        },
      },
    },
    {
      $addFields: {
        netRevenue: { $subtract: ['$totalRevenue', '$totalFees'] }
      }
    },
    { $sort: { totalRevenue: -1 } },
    { $limit: 20 },
  ]);

  return revenueByPackage;
}
