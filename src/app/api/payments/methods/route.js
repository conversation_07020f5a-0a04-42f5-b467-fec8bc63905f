import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import {
  createOrRetrieveCustomer,
  getCustomerPaymentMethods,
  createSetupIntent,
  detachPaymentMethod
} from '@/lib/stripe';

// GET /api/payments/methods - Get user's saved payment methods (no authentication required)
export async function GET(request) {
  try {
    await connectDB();
    
    // For guest users, we'll need to get userId from query params or request body
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'userId is required',
        },
        { status: 400 }
      );
    }
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get or create Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Get payment methods
    const paymentMethods = await getCustomerPaymentMethods(stripeCustomer.id);
    
    // Format payment methods for response
    const formattedMethods = paymentMethods.map(method => ({
      id: method.id,
      type: method.type,
      card: method.card ? {
        brand: method.card.brand,
        last4: method.card.last4,
        exp_month: method.card.exp_month,
        exp_year: method.card.exp_year,
        funding: method.card.funding,
      } : null,
      created: method.created,
    }));
    
    return NextResponse.json({
      success: true,
      data: {
        customerId: stripeCustomer.id,
        paymentMethods: formattedMethods,
      },
    });
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch payment methods',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/payments/methods - Create setup intent for saving payment method (no authentication required)
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'userId is required',
        },
        { status: 400 }
      );
    }
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get or create Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Create setup intent
    const setupIntent = await createSetupIntent(stripeCustomer.id);
    
    return NextResponse.json({
      success: true,
      data: {
        setupIntent: {
          id: setupIntent.id,
          client_secret: setupIntent.client_secret,
          status: setupIntent.status,
        },
        customerId: stripeCustomer.id,
      },
      message: 'Setup intent created successfully',
    });
  } catch (error) {
    console.error('Error creating setup intent:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create setup intent',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/payments/methods - Remove saved payment method (no authentication required)
export async function DELETE(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const paymentMethodId = searchParams.get('paymentMethodId');
    const userId = searchParams.get('userId');

    if (!paymentMethodId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'paymentMethodId is required',
        },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'userId is required',
        },
        { status: 400 }
      );
    }
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Verify the payment method belongs to this customer
    const paymentMethods = await getCustomerPaymentMethods(stripeCustomer.id);
    const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId);
    
    if (!paymentMethod) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Payment method not found or does not belong to this user',
        },
        { status: 404 }
      );
    }
    
    // Detach payment method
    await detachPaymentMethod(paymentMethodId);
    
    return NextResponse.json({
      success: true,
      message: 'Payment method removed successfully',
      data: {
        paymentMethodId,
      },
    });
  } catch (error) {
    console.error('Error removing payment method:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to remove payment method',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
