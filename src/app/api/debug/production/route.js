import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Blog } from '@/models/InfoMarker';

// GET /api/debug/production - Production debugging endpoint
export async function GET(request) {
  try {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      environmentVariables: {
        MONGODB_URI: process.env.MONGODB_URI ? 'Set' : 'Not Set',
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'Not Set',
        NODE_ENV: process.env.NODE_ENV || 'Not Set',
      },
      tests: []
    };

    // Test 1: Database Connection
    try {
      console.log('Testing database connection...');
      const dbConnection = await Promise.race([
        connectDB(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Database connection timeout')), 10000)
        )
      ]);
      
      debugInfo.tests.push({
        name: 'Database Connection',
        status: 'success',
        message: 'Connected successfully'
      });
    } catch (error) {
      debugInfo.tests.push({
        name: 'Database Connection',
        status: 'failed',
        error: error.message
      });
    }

    // Test 2: Info Markers Collection
    try {
      console.log('Testing info markers collection...');
      const count = await Blog.countDocuments();
      const sample = await Blog.findOne().lean();
      
      debugInfo.tests.push({
        name: 'Info Markers Collection',
        status: 'success',
        message: `Found ${count} documents`,
        sampleDocument: sample ? {
          id: sample._id,
          title: sample.title,
          hasImage: !!sample.image,
          hasBody1: !!sample.body1,
          hasBody2: !!sample.body2,
          createdAt: sample.createdAt
        } : null
      });
    } catch (error) {
      debugInfo.tests.push({
        name: 'Info Markers Collection',
        status: 'failed',
        error: error.message
      });
    }

    // Test 3: API Route Response
    try {
      debugInfo.tests.push({
        name: 'API Route Response',
        status: 'success',
        message: 'API route is responding correctly'
      });
    } catch (error) {
      debugInfo.tests.push({
        name: 'API Route Response',
        status: 'failed',
        error: error.message
      });
    }

    // Determine overall health
    const failedTests = debugInfo.tests.filter(test => test.status === 'failed');
    debugInfo.overallHealth = failedTests.length === 0 ? 'healthy' : 'unhealthy';
    debugInfo.failedTestsCount = failedTests.length;

    // Add recommendations
    debugInfo.recommendations = [];
    
    if (failedTests.length === 0) {
      debugInfo.recommendations.push('All systems are functioning normally');
    } else {
      debugInfo.recommendations.push('Some systems are experiencing issues');
      
      const dbTest = debugInfo.tests.find(test => test.name === 'Database Connection');
      if (dbTest && dbTest.status === 'failed') {
        debugInfo.recommendations.push('Check MongoDB connection string and network connectivity');
        debugInfo.recommendations.push('Verify MongoDB Atlas cluster is running and accessible');
        debugInfo.recommendations.push('Check if server IP is whitelisted in MongoDB Atlas');
      }
      
      const collectionTest = debugInfo.tests.find(test => test.name === 'Info Markers Collection');
      if (collectionTest && collectionTest.status === 'failed') {
        debugInfo.recommendations.push('Check if the Blog collection exists in the database');
        debugInfo.recommendations.push('Verify the InfoMarker model schema is correct');
      }
    }

    return NextResponse.json(debugInfo, { 
      status: debugInfo.overallHealth === 'healthy' ? 200 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error) {
    console.error('Production debug error:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      error: 'Debug endpoint failed',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      recommendations: [
        'Critical system error detected',
        'Check server logs for detailed error information',
        'Verify all environment variables are set correctly',
        'Consider restarting the application server'
      ]
    }, { 
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
