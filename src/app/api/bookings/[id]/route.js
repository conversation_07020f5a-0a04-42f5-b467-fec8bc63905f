import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { sendBookingCancellationEmail } from '@/lib/email-service';

// GET /api/bookings/[id] - Get single booking (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;

    const booking = await Booking.findOne({
      $or: [
        { _id: id },
        { bookingNumber: id }
      ]
    })
    .populate('customer', 'name email phone address emergencyContact')
    .populate('package', 'name slug category pricing inclusions exclusions')
    .lean();

    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: booking,
    });
  } catch (error) {
    console.error('Error fetching booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/bookings/[id] - Update booking (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    delete body.bookingNumber;
    
    const updatedBooking = await Booking.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    )
    .populate('customer', 'name email phone')
    .populate('package', 'name slug category');
    
    if (!updatedBooking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/bookings/[id] - Delete booking with email notification
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const reason = searchParams.get('reason') || 'Cancelled by administrator';
    const sendEmail = searchParams.get('sendEmail') !== 'false'; // Default to true

    // Find booking with customer and package details for email
    const booking = await Booking.findById(id)
      .populate('customer', 'name firstname surname email phone')
      .populate('package', 'name category');

    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }

    // Store booking data for email before deletion
    const bookingForEmail = {
      _id: booking._id,
      bookingNumber: booking.bookingNumber,
      dates: booking.dates,
      guests: booking.guests,
      pricing: booking.pricing,
      payment: booking.payment,
      package: booking.package,
      customer: booking.customer
    };

    // Delete the booking (actual deletion, not just status change)
    await Booking.findByIdAndDelete(id);

    // Send cancellation email if customer has email and sendEmail is true
    let emailResult = null;
    if (sendEmail && booking.customer?.email) {
      try {
        emailResult = await sendBookingCancellationEmail(bookingForEmail, booking.customer, reason);
        console.log(`Cancellation email sent for booking ${booking.bookingNumber}:`, emailResult);
      } catch (emailError) {
        console.error('Failed to send cancellation email:', emailError);
        emailResult = { success: false, error: emailError.message };
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Booking deleted successfully',
      data: {
        id: booking._id,
        bookingNumber: booking.bookingNumber,
        emailSent: emailResult?.success || false,
        emailError: emailResult?.success === false ? emailResult.error : null
      },
    });
  } catch (error) {
    console.error('Error deleting booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/bookings/[id] - Partial update booking (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { action, ...data } = body;
    
    const booking = await Booking.findById(id);
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    let updateOperation = {};
    
    switch (action) {
      case 'confirm':
        updateOperation = {
          status: 'confirmed',
        };
        break;
        
      case 'check_in':
        updateOperation = {
          status: 'checked_in',
          'checkInOut.checkInTime': new Date(),
          'checkInOut.checkInNotes': data.notes,
          'checkInOut.checkedInBy': data.staffId,
        };
        break;
        
      case 'check_out':
        updateOperation = {
          status: 'checked_out',
          'checkInOut.checkOutTime': new Date(),
          'checkInOut.checkOutNotes': data.notes,
          'checkInOut.checkedOutBy': data.staffId,
        };
        break;
        
      case 'add_communication':
        updateOperation = {
          $push: {
            communications: {
              type: data.type,
              direction: data.direction,
              subject: data.subject,
              content: data.content,
              author: data.authorId,
              timestamp: new Date(),
            },
          },
        };
        break;
        
      case 'add_note':
        updateOperation = {
          $push: {
            internalNotes: {
              content: data.content,
              author: data.authorId,
              createdAt: new Date(),
              isPrivate: data.isPrivate || true,
            },
          },
        };
        break;
        
      case 'update_payment_status':
        updateOperation = {
          'payment.status': data.status,
          'payment.paidAmount': data.paidAmount,
          'payment.remainingAmount': data.remainingAmount,
        };
        break;
        
      default:
        // Direct field updates
        updateOperation = data;
    }
    
    const updatedBooking = await Booking.findByIdAndUpdate(
      id,
      updateOperation,
      { 
        new: true, 
        runValidators: true 
      }
    )
    .populate('customer', 'name email phone')
    .populate('package', 'name slug category');
    
    return NextResponse.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error patching booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
