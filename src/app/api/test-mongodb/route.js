import { NextResponse } from 'next/server';
import { testMongoDBConnection, mongoHealthCheck, generateAlternativeConnectionString } from '@/lib/mongodb-test';

export async function GET() {
  try {
    console.log('Starting MongoDB connection test...');
    
    // Run comprehensive tests
    const testResults = await testMongoDBConnection();
    
    // Get health check
    const healthCheck = await mongoHealthCheck();
    
    // Generate alternative connection strings
    const alternatives = generateAlternativeConnectionString();
    
    const response = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      mongodbUri: process.env.MONGODB_URI ? 'Set' : 'Not Set',
      testResults,
      healthCheck,
      alternatives,
      recommendations: []
    };

    // Add recommendations based on test results
    const failedTests = testResults.filter(test => test.status === 'failed');
    
    if (failedTests.length > 0) {
      response.recommendations.push('MongoDB connection is failing. Check network connectivity.');
      
      const dnsTest = testResults.find(test => test.test === 'DNS');
      if (dnsTest && dnsTest.status === 'failed') {
        response.recommendations.push('DNS resolution failed. Check internet connection or try alternative DNS servers.');
      }
      
      response.recommendations.push('Consider using JWT session strategy as fallback.');
      response.recommendations.push('Verify MongoDB Atlas cluster is running and accessible.');
      response.recommendations.push('Check if IP address is whitelisted in MongoDB Atlas.');
    } else {
      response.recommendations.push('MongoDB connection is working properly.');
    }

    return NextResponse.json(response, { 
      status: healthCheck.status === 'healthy' ? 200 : 500 
    });
    
  } catch (error) {
    console.error('MongoDB test error:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      recommendations: [
        'MongoDB connection test failed completely.',
        'Check if MONGODB_URI environment variable is set correctly.',
        'Verify network connectivity to MongoDB Atlas.',
        'Consider using fallback authentication strategy.'
      ]
    }, { status: 500 });
  }
}
