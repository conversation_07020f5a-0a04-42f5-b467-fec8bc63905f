import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';

// GET /api/clients/[id] - Get single client with full details (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const includeBookings = searchParams.get('includeBookings') === 'true';
    const includePayments = searchParams.get('includePayments') === 'true';
    
    // Get client details
    const client = await User.findById(id).select('-password').lean();
    
    if (!client) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Client not found',
        },
        { status: 404 }
      );
    }
    
    // Get booking history if requested
    if (includeBookings) {
      const bookings = await Booking.find({ customer: id })
        .populate('package', 'name slug category pricing')
        .sort('-createdAt')
        .lean();
      
      client.bookings = bookings;
    }
    
    // Get payment history if requested
    if (includePayments) {
      const payments = await Payment.find({ customer: id })
        .populate('booking', 'bookingNumber dates.checkIn dates.checkOut')
        .sort('-createdAt')
        .lean();
      
      client.payments = payments;
    }
    
    // Get client statistics
    const stats = await getClientStats(id);
    client.stats = stats;
    
    return NextResponse.json({
      success: true,
      data: client,
    });
  } catch (error) {
    console.error('Error fetching client:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch client',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/clients/[id] - Update client (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    delete body.password; // Password updates should go through separate endpoint
    
    const updatedClient = await User.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    ).select('-password');
    
    if (!updatedClient) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Client not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedClient,
      message: 'Client updated successfully',
    });
  } catch (error) {
    console.error('Error updating client:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'Client with this email already exists',
        },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update client',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/clients/[id] - Delete client (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    
    // Check if client has active bookings
    const activeBookings = await Booking.countDocuments({
      customer: id,
      status: { $in: ['pending', 'confirmed', 'checked_in'] },
    });
    
    if (activeBookings > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot Delete',
          message: `Cannot delete client with ${activeBookings} active booking(s)`,
        },
        { status: 409 }
      );
    }
    
    const deletedClient = await User.findByIdAndDelete(id);
    
    if (!deletedClient) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Client not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Client deleted successfully',
      data: { id: deletedClient._id },
    });
  } catch (error) {
    console.error('Error deleting client:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete client',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/clients/[id] - Partial update client (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { action, ...data } = body;
    
    let updateOperation = {};
    
    switch (action) {
      case 'activate':
        updateOperation = {
          isActive: true,
          isBlocked: false,
        };
        break;
        
      case 'deactivate':
        updateOperation = {
          isActive: false,
        };
        break;
        
      case 'block':
        updateOperation = {
          isBlocked: true,
        };
        break;
        
      case 'unblock':
        updateOperation = {
          isBlocked: false,
        };
        break;
        
      case 'change_role':
        updateOperation = {
          role: data.role,
        };
        break;
        
      case 'add_note':
        updateOperation = {
          $push: {
            notes: {
              content: data.content,
              author: data.authorId,
              createdAt: new Date(),
            },
          },
        };
        break;
        
      case 'update_preferences':
        updateOperation = {
          preferences: data.preferences,
        };
        break;
        
      default:
        // Direct field updates
        updateOperation = data;
    }
    
    const updatedClient = await User.findByIdAndUpdate(
      id,
      updateOperation,
      { 
        new: true, 
        runValidators: true 
      }
    ).select('-password');
    
    if (!updatedClient) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Client not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedClient,
      message: 'Client updated successfully',
    });
  } catch (error) {
    console.error('Error patching client:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update client',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to get client statistics
async function getClientStats(clientId) {
  try {
    // Booking statistics
    const bookingStats = await Booking.aggregate([
      { $match: { customer: clientId } },
      {
        $group: {
          _id: null,
          totalBookings: { $sum: 1 },
          totalSpent: { $sum: '$pricing.totalAmount' },
          averageBookingValue: { $avg: '$pricing.totalAmount' },
          lastBooking: { $max: '$createdAt' },
          upcomingBookings: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$dates.checkIn', new Date()] },
                    { $in: ['$status', ['pending', 'confirmed']] }
                  ]
                },
                1,
                0
              ]
            }
          },
          completedBookings: {
            $sum: {
              $cond: [{ $eq: ['$status', 'checked_out'] }, 1, 0]
            }
          },
          cancelledBookings: {
            $sum: {
              $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0]
            }
          },
        },
      },
    ]);
    
    // Payment statistics
    const paymentStats = await Payment.aggregate([
      { $match: { customer: clientId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          amount: { $sum: '$amount' },
        },
      },
    ]);
    
    // Booking status breakdown
    const statusBreakdown = await Booking.aggregate([
      { $match: { customer: clientId } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);
    
    return {
      bookings: bookingStats[0] || {
        totalBookings: 0,
        totalSpent: 0,
        averageBookingValue: 0,
        lastBooking: null,
        upcomingBookings: 0,
        completedBookings: 0,
        cancelledBookings: 0,
      },
      payments: paymentStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          amount: stat.amount,
        };
        return acc;
      }, {}),
      statusBreakdown: statusBreakdown.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
    };
  } catch (error) {
    console.error('Error getting client stats:', error);
    return {
      bookings: {},
      payments: {},
      statusBreakdown: {},
    };
  }
}
