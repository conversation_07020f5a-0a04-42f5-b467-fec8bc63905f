import { NextResponse } from 'next/server';
import { uploadMultipleFilesServer } from '@/lib/server-file-upload';

// Configure upload settings for pages
const UPLOAD_CONFIG = {
  maxFileSize: 15 * 1024 * 1024, // 15MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  maxFiles: 10 // Allow multiple files for bulk upload
};

// POST /api/upload/pages - Upload images for pages
export async function POST(request) {
  try {
    console.log('Pages upload API called');

    const formData = await request.formData();

    // Support both 'files' (multiple) and 'file' (single) field names
    let files = formData.getAll('files');

    // If no 'files' found, check for single 'file'
    if (!files || files.length === 0) {
      const singleFile = formData.get('file');
      if (singleFile) {
        files = [singleFile];
      }
    }

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > UPLOAD_CONFIG.maxFiles) {
      return NextResponse.json(
        { success: false, error: `Too many files. Maximum ${UPLOAD_CONFIG.maxFiles} allowed.` },
        { status: 400 }
      );
    }

    // Validate files
    for (const file of files) {
      if (file.size > UPLOAD_CONFIG.maxFileSize) {
        return NextResponse.json(
          { success: false, error: `File ${file.name} is too large. Maximum size: ${UPLOAD_CONFIG.maxFileSize / 1024 / 1024}MB` },
          { status: 400 }
        );
      }

      if (!UPLOAD_CONFIG.allowedTypes.includes(file.type)) {
        return NextResponse.json(
          { success: false, error: `File ${file.name} has invalid type. Allowed: ${UPLOAD_CONFIG.allowedTypes.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Upload files
    const results = await uploadMultipleFilesServer(files, 'pages');

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    if (successful.length > 0) {
      return NextResponse.json({
        success: true,
        message: `${successful.length} file(s) uploaded successfully`,
        files: successful,
        data: successful // For compatibility
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'All uploads failed', details: failed },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Pages upload error:', error);
    return NextResponse.json(
      { success: false, error: 'Upload failed', message: error.message },
      { status: 500 }
    );
  }
}

// GET - Get upload configuration (for frontend validation)
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      config: {
        maxFileSize: UPLOAD_CONFIG.maxFileSize,
        allowedTypes: UPLOAD_CONFIG.allowedTypes,
        maxFiles: UPLOAD_CONFIG.maxFiles,
        folder: 'pages'
      }
    });
  } catch (error) {
    console.error('Error getting upload config:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get upload configuration' },
      { status: 500 }
    );
  }
}
