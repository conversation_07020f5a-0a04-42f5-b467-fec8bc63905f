import { NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';
import Contact from '@/models/Contact';
import connectDB from '@/lib/mongodb';

// POST /api/contact - Send contact form email (no authentication required)
export async function POST(request) {
  try {
    // Connect to database
    await connectDB();

    const body = await request.json();
    const { name, email, message } = body;

    // Get request metadata
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const referrer = request.headers.get('referer') || 'direct';

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Name, email, and message are required',
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Please provide a valid email address',
        },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.length < 10) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Message must be at least 10 characters long',
        },
        { status: 400 }
      );
    }

    // Create email content
    const subject = `New Contact Form Message from ${name}`;
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">New Contact Form Message</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Elephant Island Lodge</p>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
          <div style="margin-bottom: 20px;">
            <h3 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px;">Contact Details</h3>
            <div style="background-color: white; padding: 15px; border-radius: 6px; border-left: 4px solid #3498db;">
              <p style="margin: 0 0 8px 0;"><strong>Name:</strong> ${name}</p>
              <p style="margin: 0;"><strong>Email:</strong> <a href="mailto:${email}" style="color: #3498db; text-decoration: none;">${email}</a></p>
            </div>
          </div>
          
          <div style="margin-bottom: 20px;">
            <h3 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 18px;">Message</h3>
            <div style="background-color: white; padding: 20px; border-radius: 6px; border-left: 4px solid #27ae60;">
              <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${message}</p>
            </div>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
            <p style="margin: 0; color: #6c757d; font-size: 14px; text-align: center;">
              This message was sent from the contact form on the Elephant Island Lodge website.
            </p>
            <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 14px; text-align: center;">
              Received on: ${new Date().toLocaleString('en-US', { 
                timeZone: 'Africa/Gaborone',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
              })}
            </p>
          </div>
        </div>
      </div>
    `;

    const textContent = `
New Contact Form Message - Elephant Island Lodge

Contact Details:
Name: ${name}
Email: ${email}

Message:
${message}

---
This message was sent from the contact form on the Elephant Island Lodge website.
Received on: ${new Date().toLocaleString('en-US', { 
  timeZone: 'Africa/Gaborone',
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
  timeZoneName: 'short'
})}
    `;

    // First, save the contact form submission to database as backup
    const contactSubmission = new Contact({
      name,
      email,
      message,
      metadata: {
        ipAddress,
        userAgent,
        referrer,
        source: 'website_contact_form',
      },
    });

    const savedContact = await contactSubmission.save();
    console.log('Contact form submission saved to database:', savedContact._id);

    // Try to send email
    let emailResult = null;
    let emailSent = false;
    let emailError = null;

    try {
      emailResult = await sendEmail({
        to: process.env.CONTACT_EMAIL || process.env.EMAIL_FROM || '<EMAIL>',
        subject,
        html: htmlContent,
        text: textContent,
      });

      // Update contact record with email success
      savedContact.emailSent = true;
      savedContact.emailSentAt = new Date();
      savedContact.emailMessageId = emailResult.messageId;
      await savedContact.save();

      emailSent = true;
      console.log('Contact form email sent successfully:', {
        messageId: emailResult.messageId,
        contactId: savedContact._id,
        from: email,
        name,
      });

    } catch (emailError) {
      // Update contact record with email failure
      savedContact.emailError = emailError.message;
      await savedContact.save();

      console.error('Email sending failed, but contact saved to database:', {
        contactId: savedContact._id,
        error: emailError.message,
      });

      // Don't throw the error - we'll handle it below
    }

    // Return success if contact was saved, even if email failed
    return NextResponse.json({
      success: true,
      message: emailSent
        ? 'Your message has been sent successfully. We will get back to you soon!'
        : 'Your message has been received and saved. We will get back to you soon!',
      contactId: savedContact._id,
      ...(emailResult && { messageId: emailResult.messageId }),
    });

  } catch (error) {
    console.error('Error processing contact form:', {
      error: error.message,
      stack: error.stack,
      code: error.code,
    });

    // Check if it's a database error or validation error
    let userMessage = 'Failed to process your message. Please try again later.';
    let statusCode = 500;

    if (error.name === 'ValidationError') {
      userMessage = 'Please check your input and try again.';
      statusCode = 400;
    } else if (error.message.includes('duplicate key')) {
      userMessage = 'A message with this information already exists.';
      statusCode = 409;
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Processing Error',
        message: userMessage,
        // Only include error details in development
        ...(process.env.NODE_ENV === 'development' && {
          debug: {
            error: error.message,
            name: error.name,
            code: error.code
          }
        })
      },
      { status: statusCode }
    );
  }
}

// GET /api/contact - Show API information
export async function GET() {
  return NextResponse.json({
    message: 'Contact Form API',
    description: 'Send POST request with name, email, and message to submit contact form',
    requiredFields: ['name', 'email', 'message'],
    example: {
      name: 'John Doe',
      email: '<EMAIL>',
      message: 'Hello, I would like to inquire about booking a stay at your lodge.'
    }
  });
}
