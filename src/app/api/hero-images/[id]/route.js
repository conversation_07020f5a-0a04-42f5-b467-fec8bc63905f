import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroImage } from '@/models/HeroImage';

// GET /api/hero-images/[id] - Get single hero image (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    const heroImage = await HeroImage.findById(id);

    if (!heroImage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero image not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: heroImage,
    });
  } catch (error) {
    console.error('Error fetching hero image:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch hero image',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/hero-images/[id] - Update single hero image (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;
    const body = await request.json();

    // No special validation needed for carousel use case

    const updatedHeroImage = await HeroImage.findByIdAndUpdate(
      id,
      body,
      { new: true, runValidators: true }
    );

    if (!updatedHeroImage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero image not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedHeroImage,
      message: 'Hero image updated successfully',
    });
  } catch (error) {
    console.error('Error updating hero image:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update hero image',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/hero-images/[id] - Delete single hero image (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    const { id } = await params;

    const deletedHeroImage = await HeroImage.findByIdAndDelete(id);

    if (!deletedHeroImage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Hero image not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: deletedHeroImage,
      message: 'Hero image deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting hero image:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete hero image',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
