import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';

// Development fallback data
const FALLBACK_PAGES_DATA = {
  island: {
    title: 'Welcome to The Island',
    image: '',
    body1: 'Discover the beauty and wonder of our pristine island paradise.',
    additionalContent: []
  },
  experiences: {
    title: 'Unforgettable Experiences',
    image: '',
    body1: 'Create memories that will last a lifetime with our curated experiences.',
    additionalContent: []
  },
  testimonials: {
    testimonials: []
  },
  locationAndcontacts: {
    title: 'Location & Contact',
    image: '',
    body1: 'Find us and get in touch for your perfect island getaway.',
    additionalContent: []
  },
  booking: {
    details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
  }
};

// GET - Fetch the single pages document with all 4 sections
export async function GET(request) {
  try {
    console.log('Attempting to connect to MongoDB...');
    await connectDB();
    console.log('MongoDB connection successful, fetching pages...');

    // Get the single pages document (there should only be one)
    let pages = await Page.findOne().lean();

    // If no pages document exists, initialize it
    if (!pages) {
      console.log('No pages document found, initializing default pages...');
      await Page.initializeDefaultPages();
      pages = await Page.findOne().lean();
    }

    console.log('Pages fetched successfully');
    return NextResponse.json({
      success: true,
      data: pages
    });

  } catch (error) {
    console.error('Error fetching pages:', error);

    // Development fallback when MongoDB is not available
    if (process.env.NODE_ENV === 'development') {
      console.log('MongoDB unavailable, using fallback data for development...');
      return NextResponse.json({
        success: true,
        data: FALLBACK_PAGES_DATA,
        fallback: true
      });
    }

    // Provide more specific error messages
    let errorMessage = 'Failed to fetch pages';
    if (error.name === 'MongoNetworkTimeoutError') {
      errorMessage = 'Database connection timeout. Please try again.';
    } else if (error.name === 'MongoServerSelectionError') {
      errorMessage = 'Unable to connect to database server.';
    } else if (error.name === 'MongooseError') {
      errorMessage = 'Database query error.';
    }

    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
        error: error.message,
        errorType: error.name
      },
      { status: 500 }
    );
  }
}

// POST - Update specific page section
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();
    const { section, data } = body;

    if (!section || !data) {
      return NextResponse.json(
        { success: false, message: 'Section and data are required' },
        { status: 400 }
      );
    }

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    if (!validSections.includes(section)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Ensure booking field exists (for backward compatibility)
    if (!pages.booking) {
      pages.booking = {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      };
      await pages.save();
    }

    // Run migration to clean up deprecated fields if needed
    // Check if any additional content items have body2 fields
    const needsMigration = (pages.island?.additionalContent?.some(item => item.body2 !== undefined)) ||
                          (pages.experiences?.additionalContent?.some(item => item.body2 !== undefined));

    if (needsMigration) {
      console.log('Running migration to remove deprecated body2 fields...');
      await Page.migrateDeprecatedFields();
      // Refresh the pages document after migration
      pages = await Page.findOne();
    }

    // Update the specific section
    pages[section] = data;
    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `${section} section updated successfully`,
      data: updatedPages
    });

  } catch (error) {
    console.error('Error updating page section:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page section', error: error.message },
      { status: 500 }
    );
  }
}

// PATCH - Update multiple sections at once
export async function PATCH(request) {
  try {
    await connectDB();

    const body = await request.json();

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Update multiple sections
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    let updatedSections = [];

    for (const [section, data] of Object.entries(body)) {
      if (validSections.includes(section)) {
        pages[section] = data;
        updatedSections.push(section);
      }
    }

    if (updatedSections.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No valid sections provided' },
        { status: 400 }
      );
    }

    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `Updated sections: ${updatedSections.join(', ')}`,
      data: updatedPages
    });

  } catch (error) {
    console.error('Error in bulk update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk update', error: error.message },
      { status: 500 }
    );
  }
}

// DELETE - Reset all pages to default
export async function DELETE(request) {
  try {
    await connectDB();

    // Delete the existing pages document
    await Page.deleteMany({});

    // Reinitialize with defaults
    await Page.initializeDefaultPages();
    const newPages = await Page.findOne();

    return NextResponse.json({
      success: true,
      message: 'All pages reset to default values',
      data: newPages
    });

  } catch (error) {
    console.error('Error resetting pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to reset pages', error: error.message },
      { status: 500 }
    );
  }
}
