import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { HeroVideo } from '@/models/HeroVideo';

// GET /api/hero-videos/active - Get the currently active hero video (public access)
export async function GET() {
  try {
    await connectDB();

    // Find the active hero video first
    let video = await HeroVideo.findOne({ isActive: true });

    // If no active video, get the most recent one
    if (!video) {
      video = await HeroVideo.findOne().sort({ createdAt: -1 });
    }

    // If still no video, return no video available
    if (!video) {
      return NextResponse.json({
        success: false,
        message: 'No hero videos available'
      });
    }
    
    // Return simplified video data
    return NextResponse.json({
      success: true,
      data: {
        _id: video._id,
        name: video.name,
        url: video.url,
        isActive: video.isActive,
      }
    });

  } catch (error) {
    console.error('Error fetching hero video:', error);

    // Return error on any database issues
    return NextResponse.json({
      success: false,
      message: 'Error fetching hero video'
    }, { status: 500 });
  }
}
