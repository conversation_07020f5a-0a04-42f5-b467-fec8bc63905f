import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// Development fallback data for 360s
const FALLBACK_360S_DATA = [
  {
    _id: 'fallback_360_001',
    name: 'New_entrance_360_002',
    originalFileName: 'New_entrance_360_002.jpg',
    url: '/uploads/360s/New_entrance_360_002.jpg',
    priority: 1,
    markerList: [],
    cameraPosition: -0.0001,
    _360Rotation: -0.0001,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: 'fallback_360_002',
    name: 'livingroom_001',
    originalFileName: 'livingroom_001.jpg',
    url: '/uploads/360s/livingroom_001.jpg',
    priority: 2,
    markerList: [],
    cameraPosition: -0.0001,
    _360Rotation: -0.0001,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// GET /api/360s/public - Get all 360s for public viewing (no authentication required)
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-priority';
    const id = searchParams.get('id'); // For specific 360 lookup by name
    
    // Build query
    const query = {};
    
    // If specific ID/name is requested
    if (id) {
      query.$or = [
        { _id: id },
        { name: id },
        { originalFileName: id }
      ];
    }
    
    // Search by name or originalFileName
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { originalFileName: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await _360Settings.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await _360Settings.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching public 360s:', error);

    // Development fallback when MongoDB is not available
    if (process.env.NODE_ENV === 'development') {
      console.log('MongoDB unavailable, using fallback data for public 360s...');

      const { searchParams } = new URL(request.url);
      const search = searchParams.get('search');
      const limit = parseInt(searchParams.get('limit')) || 50;
      const page = parseInt(searchParams.get('page')) || 1;
      const sort = searchParams.get('sort') || '-priority';
      const id = searchParams.get('id');

      let filteredData = [...FALLBACK_360S_DATA];

      // Filter by ID if specified
      if (id) {
        filteredData = filteredData.filter(item =>
          item._id === id ||
          item.name === id ||
          item.originalFileName === id
        );
      }

      // Filter by search if specified
      if (search) {
        filteredData = filteredData.filter(item =>
          item.name.toLowerCase().includes(search.toLowerCase()) ||
          item.originalFileName.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply sorting (basic implementation)
      if (sort === '-priority') {
        filteredData.sort((a, b) => b.priority - a.priority);
      } else if (sort === 'priority') {
        filteredData.sort((a, b) => a.priority - b.priority);
      }

      // Apply pagination
      const skip = (page - 1) * limit;
      const paginatedData = filteredData.slice(skip, skip + limit);

      return NextResponse.json({
        success: true,
        data: paginatedData,
        pagination: {
          page,
          limit,
          total: filteredData.length,
          pages: Math.ceil(filteredData.length / limit),
        },
        fallback: true
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
