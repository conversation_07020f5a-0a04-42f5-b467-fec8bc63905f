import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { 
  migrateSingle360ToFirebase, 
  generateMigrationReport, 
  needsMigration,
  cleanupLocalFiles 
} from '@/lib/360-url-migration';

/**
 * POST /api/360s/migrate-to-firebase
 * Migrate 360° records from local URLs to Firebase URLs
 */
export async function POST(request) {
  try {
    const { dryRun = true, cleanup = false } = await request.json();

    console.log(`Starting 360° URL migration (dryRun: ${dryRun}, cleanup: ${cleanup})`);

    // Connect to database
    const { db } = await connectToDatabase();
    const collection = db.collection('360s');

    // Find all 360° records with local URLs
    const localRecords = await collection.find({
      url: { $regex: '^/?uploads/' }
    }).toArray();

    console.log(`Found ${localRecords.length} records with local URLs`);

    if (localRecords.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No records need migration',
        report: {
          total: 0,
          successful: 0,
          migrated: 0,
          alreadyMigrated: 0,
          failed: 0,
          results: [],
        },
      });
    }

    // Perform migration
    const migrationResults = [];

    for (const record of localRecords) {
      console.log(`Processing record: ${record.name} (${record._id})`);
      
      if (dryRun) {
        // Dry run - just check what would be migrated
        migrationResults.push({
          success: true,
          migrated: needsMigration(record.url),
          originalUrl: record.url,
          newUrl: `[DRY RUN] Would upload to Firebase`,
          dryRun: true,
          recordId: record._id,
          recordName: record.name,
        });
      } else {
        // Actual migration
        const migrationResult = await migrateSingle360ToFirebase(record);
        
        if (migrationResult.success && migrationResult.migrated) {
          // Update database with new Firebase URL
          try {
            await collection.updateOne(
              { _id: record._id },
              { 
                $set: { 
                  url: migrationResult.newUrl,
                  migratedToFirebase: true,
                  migrationDate: new Date(),
                  originalLocalUrl: migrationResult.originalUrl,
                }
              }
            );
            
            console.log(`Updated database for ${record.name}: ${migrationResult.newUrl}`);
            migrationResult.databaseUpdated = true;
          } catch (dbError) {
            console.error(`Failed to update database for ${record.name}:`, dbError);
            migrationResult.databaseUpdated = false;
            migrationResult.dbError = dbError.message;
          }
        }
        
        migrationResult.recordId = record._id;
        migrationResult.recordName = record.name;
        migrationResults.push(migrationResult);
      }
    }

    // Generate report
    const report = generateMigrationReport(migrationResults);

    // Optional cleanup of local files
    let cleanupReport = null;
    if (cleanup && !dryRun) {
      cleanupReport = cleanupLocalFiles(migrationResults, false);
    } else if (cleanup && dryRun) {
      cleanupReport = cleanupLocalFiles(migrationResults, true);
    }

    console.log(`Migration completed: ${report.migrated} migrated, ${report.failed} failed`);

    return NextResponse.json({
      success: true,
      message: dryRun 
        ? `Dry run completed: ${report.total} records would be processed`
        : `Migration completed: ${report.migrated} records migrated successfully`,
      dryRun,
      report,
      cleanup: cleanupReport,
    });

  } catch (error) {
    console.error('Migration API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message,
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/360s/migrate-to-firebase
 * Check migration status and get preview of what would be migrated
 */
export async function GET() {
  try {
    console.log('Checking 360° migration status...');

    // Connect to database
    const { db } = await connectToDatabase();
    const collection = db.collection('360s');

    // Count records by URL type
    const totalRecords = await collection.countDocuments();
    const localRecords = await collection.countDocuments({
      url: { $regex: '^/?uploads/' }
    });
    const firebaseRecords = await collection.countDocuments({
      url: { $regex: '^https://firebasestorage' }
    });
    const otherRecords = totalRecords - localRecords - firebaseRecords;

    // Get sample local records
    const sampleLocalRecords = await collection.find(
      { url: { $regex: '^/?uploads/' } },
      { projection: { name: 1, url: 1, createdAt: 1 } }
    ).limit(10).toArray();

    // Get sample Firebase records
    const sampleFirebaseRecords = await collection.find(
      { url: { $regex: '^https://firebasestorage' } },
      { projection: { name: 1, url: 1, createdAt: 1 } }
    ).limit(5).toArray();

    return NextResponse.json({
      success: true,
      status: {
        total: totalRecords,
        needsMigration: localRecords,
        alreadyMigrated: firebaseRecords,
        other: otherRecords,
      },
      samples: {
        local: sampleLocalRecords,
        firebase: sampleFirebaseRecords,
      },
      recommendations: {
        shouldMigrate: localRecords > 0,
        message: localRecords > 0 
          ? `${localRecords} records need migration to Firebase`
          : 'All records are already using Firebase URLs',
      },
    });

  } catch (error) {
    console.error('Migration status API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message,
      },
      { status: 500 }
    );
  }
}
