import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// PATCH /api/360s/[id]/partial-delete - Clear URL field only (partial deletion)
export async function PATCH(request, { params }) {
  try {
    console.log('360° Partial Delete Request Started');

    // Connect to database with timeout
    const dbConnection = await Promise.race([
      connectDB(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout')), 10000)
      )
    ]);

    console.log('Database connected successfully');

    const { id } = await params;
    console.log('Processing partial delete for ID:', id);

    // Validate ID format
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid 360 ID format',
        },
        { status: 400 }
      );
    }

    // First check if the document exists
    const existingDoc = await _360Settings.findById(id);
    if (!existingDoc) {
      console.log('Document not found with ID:', id);
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    console.log('Document found, proceeding with partial delete (clearing URL)');

    // Perform partial deletion - clear only the URL field
    const updated360 = await Promise.race([
      _360Settings.findByIdAndUpdate(
        id,
        { 
          $unset: { url: "" }, // Remove the URL field
          $set: { 
            originalFileName: "", // Also clear the original filename
            updatedAt: new Date() // Update the timestamp
          }
        },
        {
          new: true,
          runValidators: false,
          lean: false,
          upsert: false
        }
      ),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database update timeout')), 10000)
      )
    ]);

    console.log('Partial delete completed');

    if (!updated360) {
      console.log('No document found with ID:', id);
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    console.log('Partial delete successful, returning response');

    return NextResponse.json({
      success: true,
      data: updated360,
      message: 'Image file removed successfully. Markers and settings preserved.',
    });
  } catch (error) {
    console.error('Error in partial delete:', error);
    console.error('Error stack:', error.stack);

    // Handle specific error types
    if (error.message.includes('timeout')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Timeout Error',
          message: 'Operation timed out. Please try again.',
        },
        { status: 408 }
      );
    }

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid 360 ID format',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to perform partial delete',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
