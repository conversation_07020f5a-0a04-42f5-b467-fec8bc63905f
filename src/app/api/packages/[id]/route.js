import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Package } from '@/models/Package';
import { isPredefinedPackage } from '@/lib/package-utils';

// GET /api/packages/[id] - Get single package (public)
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    
    // Find by ID or slug
    const packageData = await Package.findOne({
      $or: [
        { _id: id },
        { slug: id }
      ]
    }).lean();

    if (!packageData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: packageData,
    });
  } catch (error) {
    console.error('Error fetching package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch package',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/packages/[id] - Update package (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    // Update slug if name changed
    if (body.name) {
      body.slug = body.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }
    
    const updatedPackage = await Package.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedPackage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedPackage,
      message: 'Package updated successfully',
    });
  } catch (error) {
    console.error('Error updating package:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'Package with this name already exists',
        },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update package',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/packages/[id] - Delete package (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;

    // Check if package exists and get its data
    const packageData = await Package.findById(id);
    if (!packageData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }

    // Prevent deletion of predefined packages
    if (isPredefinedPackage(packageData.slug)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Operation Not Allowed',
          message: 'Cannot delete predefined package types (Individual, Couples, Families). These packages can only be edited.',
        },
        { status: 403 }
      );
    }

    // Check if package has active bookings
    const { Booking } = await import('@/models/Booking');
    const activeBookings = await Booking.countDocuments({
      package: id,
      status: { $in: ['pending', 'confirmed', 'checked_in'] },
    });
    
    if (activeBookings > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot Delete',
          message: `Cannot delete package with ${activeBookings} active booking(s)`,
        },
        { status: 409 }
      );
    }
    
    const deletedPackage = await Package.findByIdAndDelete(id);
    
    if (!deletedPackage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Package deleted successfully',
      data: { id: deletedPackage._id },
    });
  } catch (error) {
    console.error('Error deleting package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete package',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/packages/[id] - Partial update package (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Common patch operations
    const { action, ...data } = body;
    
    let updateOperation = {};
    
    switch (action) {
      case 'toggle_active':
        updateOperation = {
          'availability.isActive': !data.currentStatus,
        };
        break;
        
      case 'toggle_featured':
        updateOperation = {
          featured: !data.currentStatus,
        };
        break;
        
      case 'update_pricing':
        updateOperation = {
          pricing: data.pricing,
        };
        break;
        
      case 'add_image':
        updateOperation = {
          $push: { images: data.image },
        };
        break;
        
      case 'remove_image':
        updateOperation = {
          $pull: { images: { _id: data.imageId } },
        };
        break;
        
      case 'set_primary_image':
        // First, unset all primary images
        await Package.findByIdAndUpdate(id, {
          $set: { 'images.$[].isPrimary': false },
        });
        
        // Then set the specified image as primary
        updateOperation = {
          $set: { 'images.$[elem].isPrimary': true },
        };
        break;
        
      default:
        // Direct field updates
        updateOperation = data;
    }
    
    const options = {
      new: true,
      runValidators: true,
    };
    
    if (action === 'set_primary_image') {
      options.arrayFilters = [{ 'elem._id': data.imageId }];
    }
    
    const updatedPackage = await Package.findByIdAndUpdate(
      id,
      updateOperation,
      options
    );
    
    if (!updatedPackage) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedPackage,
      message: 'Package updated successfully',
    });
  } catch (error) {
    console.error('Error patching package:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update package',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
