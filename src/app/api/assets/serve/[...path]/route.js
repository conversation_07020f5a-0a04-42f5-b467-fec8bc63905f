import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Serve general assets with proper CORS headers for production
 * This API route handles serving local assets when Firebase is not available
 */

// GET /api/assets/serve/[...path] - Serve assets with proper headers
export async function GET(request, { params }) {
  try {
    const { path: assetPath } = await params;
    
    if (!assetPath || !Array.isArray(assetPath)) {
      return NextResponse.json(
        { error: 'Invalid asset path' },
        { status: 400 }
      );
    }

    // Reconstruct the file path
    const filePath = assetPath.join('/');
    
    // Security: Prevent directory traversal
    if (filePath.includes('..') || filePath.includes('\\')) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }

    // Try different possible locations for the asset
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'assets', filePath),
      path.join(process.cwd(), 'public', 'assets', 'images', filePath),
      path.join(process.cwd(), 'public', 'assets', 'ui', filePath),
      path.join(process.cwd(), 'public', 'assets', 'icons', filePath),
      path.join(process.cwd(), 'public', 'uploads', filePath),
      path.join(process.cwd(), 'public', filePath),
    ];

    let fileBuffer = null;
    let actualPath = null;
    let mimeType = 'application/octet-stream'; // Default

    // Try to find the file in possible locations
    for (const possiblePath of possiblePaths) {
      try {
        await fs.access(possiblePath);
        fileBuffer = await fs.readFile(possiblePath);
        actualPath = possiblePath;
        
        // Determine MIME type based on file extension
        const ext = path.extname(possiblePath).toLowerCase();
        switch (ext) {
          case '.png':
            mimeType = 'image/png';
            break;
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
          case '.svg':
            mimeType = 'image/svg+xml';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          case '.tiff':
          case '.tif':
            mimeType = 'image/tiff';
            break;
          case '.ico':
            mimeType = 'image/x-icon';
            break;
          case '.css':
            mimeType = 'text/css';
            break;
          case '.js':
            mimeType = 'application/javascript';
            break;
          case '.json':
            mimeType = 'application/json';
            break;
          case '.pdf':
            mimeType = 'application/pdf';
            break;
          case '.mp4':
            mimeType = 'video/mp4';
            break;
          case '.mp3':
            mimeType = 'audio/mpeg';
            break;
          case '.wav':
            mimeType = 'audio/wav';
            break;
          case '.ttf':
            mimeType = 'font/ttf';
            break;
          case '.woff':
            mimeType = 'font/woff';
            break;
          case '.woff2':
            mimeType = 'font/woff2';
            break;
          default:
            mimeType = 'application/octet-stream';
        }
        
        break;
      } catch (error) {
        // File not found in this location, try next
        continue;
      }
    }

    if (!fileBuffer) {
      console.error(`Asset not found: ${filePath}`);
      return NextResponse.json(
        { error: 'Asset not found' },
        { status: 404 }
      );
    }

    console.log(`Serving asset: ${actualPath}`);

    // Create response with proper headers for assets
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
        'Access-Control-Allow-Origin': '*', // Allow CORS
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'X-Content-Type-Options': 'nosniff',
      },
    });

    return response;

  } catch (error) {
    console.error('Error serving asset:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
}

// Handle HEAD requests for asset validation
export async function HEAD(request, { params }) {
  try {
    const { path: assetPath } = await params;
    
    if (!assetPath || !Array.isArray(assetPath)) {
      return new NextResponse(null, { status: 400 });
    }

    const filePath = assetPath.join('/');
    
    // Security check
    if (filePath.includes('..') || filePath.includes('\\')) {
      return new NextResponse(null, { status: 400 });
    }

    // Try to find the file
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'assets', filePath),
      path.join(process.cwd(), 'public', 'assets', 'images', filePath),
      path.join(process.cwd(), 'public', 'assets', 'ui', filePath),
      path.join(process.cwd(), 'public', 'assets', 'icons', filePath),
      path.join(process.cwd(), 'public', 'uploads', filePath),
      path.join(process.cwd(), 'public', filePath),
    ];

    for (const possiblePath of possiblePaths) {
      try {
        const stats = await fs.stat(possiblePath);
        
        // Determine MIME type
        const ext = path.extname(possiblePath).toLowerCase();
        let mimeType = 'application/octet-stream';
        switch (ext) {
          case '.png': mimeType = 'image/png'; break;
          case '.jpg':
          case '.jpeg': mimeType = 'image/jpeg'; break;
          case '.webp': mimeType = 'image/webp'; break;
          case '.svg': mimeType = 'image/svg+xml'; break;
          case '.gif': mimeType = 'image/gif'; break;
          case '.css': mimeType = 'text/css'; break;
          case '.js': mimeType = 'application/javascript'; break;
          case '.json': mimeType = 'application/json'; break;
          case '.pdf': mimeType = 'application/pdf'; break;
          case '.ttf': mimeType = 'font/ttf'; break;
          case '.woff': mimeType = 'font/woff'; break;
          case '.woff2': mimeType = 'font/woff2'; break;
        }

        return new NextResponse(null, {
          status: 200,
          headers: {
            'Content-Type': mimeType,
            'Content-Length': stats.size.toString(),
            'Cache-Control': 'public, max-age=31536000, immutable',
            'Access-Control-Allow-Origin': '*',
            'Last-Modified': stats.mtime.toUTCString(),
          },
        });
      } catch (error) {
        continue;
      }
    }

    return new NextResponse(null, { status: 404 });

  } catch (error) {
    console.error('Error in HEAD request for asset:', error);
    return new NextResponse(null, { status: 500 });
  }
}
