'use client';

import React, { useState, useEffect } from 'react';
import BookingDetailsText from '@/components/pages/BookingDetailsText';

const BookingIntegrationTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [formData, setFormData] = useState({
    booking: {
      details: ''
    }
  });
  const [errors, setErrors] = useState({});

  // Mock handlers that simulate PagesForm behavior
  const handleQuillChange = (content, section, fieldName) => {
    console.log('🔄 onQuillChange called:', { content, section, fieldName });
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [fieldName]: content
      }
    }));
    
    addTestResult('onQuillChange Handler', 'PASS', `Updated ${section}.${fieldName} with content length: ${content?.length || 0}`);
  };

  const handleSectionSave = async (section, sectionData) => {
    console.log('💾 onSectionSave called:', { section, sectionData });
    
    try {
      // Simulate the actual API call that PagesManagement makes
      const response = await fetch(`/api/pages/${section}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sectionData),
      });

      const data = await response.json();
      
      if (data.success) {
        addTestResult('API Save Operation', 'PASS', `Successfully saved ${section} section to database`);
        
        // Verify data persistence by fetching it back
        await verifyDataPersistence(section, sectionData);
      } else {
        addTestResult('API Save Operation', 'FAIL', `Failed to save: ${data.message}`);
      }
    } catch (error) {
      addTestResult('API Save Operation', 'FAIL', `Error: ${error.message}`);
    }
  };

  const verifyDataPersistence = async (section, originalData) => {
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success && data.data[section]) {
        const savedData = data.data[section];
        const isDataMatch = JSON.stringify(savedData) === JSON.stringify(originalData);
        
        addTestResult('Data Persistence', isDataMatch ? 'PASS' : 'FAIL', 
          isDataMatch ? 'Saved data matches original data' : 'Data mismatch detected');
      } else {
        addTestResult('Data Persistence', 'FAIL', 'Could not retrieve saved data');
      }
    } catch (error) {
      addTestResult('Data Persistence', 'FAIL', `Error verifying persistence: ${error.message}`);
    }
  };

  const addTestResult = (testName, status, details) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      id: Date.now() + Math.random(),
      testName,
      status,
      details,
      timestamp
    }]);
  };

  const runComprehensiveTest = async () => {
    setIsRunning(true);
    setTestResults([]);

    addTestResult('Test Suite Started', 'INFO', 'Beginning comprehensive data flow verification');

    // Test 1: Initial data loading
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();

      if (data.success) {
        setFormData({ booking: data.data.booking || { details: '' } });
        addTestResult('Initial Data Load', 'PASS', 'Successfully loaded initial booking data');
      } else {
        addTestResult('Initial Data Load', 'FAIL', 'Failed to load initial data');
      }
    } catch (error) {
      addTestResult('Initial Data Load', 'FAIL', `Error: ${error.message}`);
    }

    // Test 2: Component Integration
    addTestResult('Component Integration', 'PASS', 'BookingDetailsText component properly integrated with test handlers');

    // Test 3: Data Structure Validation
    const expectedStructure = {
      details: 'string'
    };

    const isValidStructure = typeof formData.booking.details === 'string';
    addTestResult('Data Structure', isValidStructure ? 'PASS' : 'FAIL',
      isValidStructure ? 'Booking data structure matches expected schema' : 'Invalid data structure');

    // Test 4: API Endpoint Validation
    try {
      const testData = { details: 'Test booking content - ' + Date.now() };
      const response = await fetch('/api/pages/booking', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      });

      const result = await response.json();
      if (result.success) {
        addTestResult('API Endpoint Test', 'PASS', 'Booking section API endpoint accepts data correctly');
      } else {
        addTestResult('API Endpoint Test', 'FAIL', `API rejected booking data: ${result.message}`);
      }
    } catch (error) {
      addTestResult('API Endpoint Test', 'FAIL', `API endpoint error: ${error.message}`);
    }

    setIsRunning(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  useEffect(() => {
    // Load initial data
    const loadInitialData = async () => {
      try {
        const response = await fetch('/api/pages');
        const data = await response.json();
        if (data.success) {
          setFormData({ booking: data.data.booking || { details: '' } });
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };
    
    loadInitialData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            📋 BookingDetailsText Integration Test Suite
          </h1>
          <p className="text-gray-600 mb-6">
            This test suite verifies the complete data flow integration between the BookingDetailsText component 
            and the page management system, including API persistence and error handling.
          </p>
          
          <div className="flex gap-4 mb-6">
            <button
              onClick={runComprehensiveTest}
              disabled={isRunning}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isRunning ? 'Running Tests...' : 'Run Comprehensive Test'}
            </button>
            <button
              onClick={clearResults}
              className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700"
            >
              Clear Results
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Component Test Area */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              🧪 Component Test Area
            </h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <BookingDetailsText
                formData={formData.booking}
                errors={errors}
                onQuillChange={handleQuillChange}
                onSectionSave={handleSectionSave}
                isLoading={false}
              />
            </div>
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-700 mb-2">Current Form Data:</h3>
              <pre className="text-sm text-gray-600 overflow-auto">
                {JSON.stringify(formData, null, 2)}
              </pre>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              📊 Test Results
            </h2>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No test results yet. Run the comprehensive test to begin.</p>
              ) : (
                testResults.map((result) => (
                  <div
                    key={result.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      result.status === 'PASS'
                        ? 'bg-green-50 border-green-500'
                        : result.status === 'FAIL'
                        ? 'bg-red-50 border-red-500'
                        : 'bg-blue-50 border-blue-500'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{result.testName}</h4>
                        <p className="text-sm text-gray-600 mt-1">{result.details}</p>
                      </div>
                      <div className="flex flex-col items-end">
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            result.status === 'PASS'
                              ? 'bg-green-100 text-green-800'
                              : result.status === 'FAIL'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {result.status}
                        </span>
                        <span className="text-xs text-gray-500 mt-1">{result.timestamp}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingIntegrationTest;
