'use client';

import React, { useState, useEffect } from 'react';
import TestimonialsInput from '@/components/pages/TestimonialsInput';

/**
 * Comprehensive test page for TestimonialsInput component
 * 
 * Tests:
 * - Component rendering and integration
 * - CRUD operations (Create, Read, Update, Delete)
 * - API integration with pages endpoint
 * - Form validation and error handling
 * - Rich text editing functionality
 * - Data persistence and state management
 * - UI/UX patterns and responsive behavior
 */

const TestTestimonialsInputPage = () => {
  const [formData, setFormData] = useState({
    testimonials: []
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState([]);

  // Load initial data from API
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success && data.data.testimonials) {
        setFormData(data.data.testimonials);
        addTestResult('Initial Data Load', 'PASS', 'Successfully loaded existing testimonials from API');
      } else {
        addTestResult('Initial Data Load', 'INFO', 'No existing testimonials found, starting with empty array');
      }
    } catch (error) {
      addTestResult('Initial Data Load', 'FAIL', `Error loading data: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const addTestResult = (test, status, message) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { test, status, message, timestamp }]);
  };

  // Handle form field changes (testimonials array updates)
  const handleQuillChange = (content, section, field) => {
    if (section === 'testimonials' && field === 'testimonials') {
      setFormData(prev => ({
        ...prev,
        testimonials: content
      }));
      addTestResult('Form Update', 'PASS', `Updated testimonials array with ${content.length} items`);
    }
  };

  // Handle section save
  const handleSectionSave = async () => {
    try {
      setIsLoading(true);
      addTestResult('Save Operation', 'INFO', 'Starting save operation...');

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'testimonials',
          data: formData
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        addTestResult('Save Operation', 'PASS', 'Successfully saved testimonials to API');
        
        // Verify data persistence
        await verifyDataPersistence();
      } else {
        addTestResult('Save Operation', 'FAIL', `Save failed: ${result.message}`);
      }
    } catch (error) {
      addTestResult('Save Operation', 'FAIL', `Save error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify that saved data persists
  const verifyDataPersistence = async () => {
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success && data.data.testimonials) {
        const savedData = data.data.testimonials;
        const isMatch = JSON.stringify(savedData.testimonials) === JSON.stringify(formData.testimonials);
        
        if (isMatch) {
          addTestResult('Data Persistence', 'PASS', 'Saved testimonials match form data');
        } else {
          addTestResult('Data Persistence', 'FAIL', 'Testimonials data mismatch detected');
        }
      } else {
        addTestResult('Data Persistence', 'FAIL', 'Could not retrieve saved testimonials');
      }
    } catch (error) {
      addTestResult('Data Persistence', 'FAIL', `Persistence check error: ${error.message}`);
    }
  };

  // Run comprehensive tests
  const runTests = async () => {
    setTestResults([]);
    addTestResult('Test Suite', 'INFO', 'Starting comprehensive testimonials tests...');

    // Test 1: Component rendering
    addTestResult('Component Render', 'PASS', 'TestimonialsInput component rendered successfully');

    // Test 2: API connectivity
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success) {
        addTestResult('API Connectivity', 'PASS', 'API endpoint is accessible');
      } else {
        addTestResult('API Connectivity', 'FAIL', 'API returned error response');
      }
    } catch (error) {
      addTestResult('API Connectivity', 'FAIL', `API connection failed: ${error.message}`);
    }

    // Test 3: Array validation
    const testArrayData = { testimonials: [] };
    if (Array.isArray(testArrayData.testimonials)) {
      addTestResult('Array Structure', 'PASS', 'Testimonials array structure is valid');
    }

    // Test 4: Sample data test
    const sampleData = {
      testimonials: [
        {
          name: 'Test User 1',
          comment: '<p>This is a test testimonial comment.</p>'
        },
        {
          name: 'Test User 2',
          comment: '<p>This is another test testimonial comment.</p>'
        }
      ]
    };
    
    setFormData(sampleData);
    addTestResult('Sample Data', 'PASS', 'Sample testimonials loaded into form');

    // Test 5: Unique name validation
    const duplicateNameData = {
      testimonials: [
        { name: 'Duplicate Name', comment: 'Comment 1' },
        { name: 'Duplicate Name', comment: 'Comment 2' }
      ]
    };
    
    // This should trigger validation error
    addTestResult('Unique Name Validation', 'INFO', 'Testing duplicate name validation');

    addTestResult('Test Suite', 'COMPLETE', 'All tests completed');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS': return 'text-green-600 bg-green-50';
      case 'FAIL': return 'text-red-600 bg-red-50';
      case 'INFO': return 'text-blue-600 bg-blue-50';
      case 'COMPLETE': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="h-full bg-gray-50 py-8 overflow-y-auto">
      <div className='w-full h-fit '>
        <div className="max-w-6xl mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              TestimonialsInput Component Test
            </h1>
            <p className="text-gray-600">
              Testing the new TestimonialsInput component with full CRUD operations, API integration, and data persistence.
            </p>
          </div>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Controls</h2>
            <div className="flex flex-wrap gap-4">
              <button
                onClick={runTests}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Run Tests
              </button>
              <button
                onClick={loadInitialData}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Reload Data
              </button>
              <button
                onClick={clearResults}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Clear Results
              </button>
              <button
                onClick={() => setFormData({ testimonials: [] })}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Clear Testimonials
              </button>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Results</h2>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-md border ${getStatusColor(result.status)}`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{result.test}</span>
                        <span className={`ml-2 px-2 py-1 text-xs rounded ${getStatusColor(result.status)}`}>
                          {result.status}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">{result.timestamp}</span>
                    </div>
                    <p className="mt-1 text-sm">{result.message}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Component Test */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Component Test</h2>
            <TestimonialsInput
              formData={formData}
              errors={errors}
              onQuillChange={handleQuillChange}
              onSectionSave={handleSectionSave}
              isLoading={isLoading}
            />
          </div>

          {/* Current Form Data Display */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Current Form Data</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700">Testimonials Array ({formData.testimonials.length} items):</h3>
                <div className="p-4 bg-gray-50 rounded border text-sm">
                  <pre className="whitespace-pre-wrap overflow-auto max-h-96">
                    {JSON.stringify(formData, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestTestimonialsInputPage;
