'use client';

import { useState } from 'react';
import TextEditor from '@/components/common/TextEditor';
import HtmlContentDisplay from '@/components/HtmlContentDisplay';

const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'size', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

export default function TestFontSizes() {
  const [content, setContent] = useState('<p>This is <span class="ql-size-small">small text</span>, this is normal text, this is <span class="ql-size-large">large text</span>, and this is <span class="ql-size-huge">huge text</span>.</p>');

  return (
    <div className="min-h-screen bg-gray-100 p-8 mb-20 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          ReactQuill Font Size Fix Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Instructions
          </h2>
          <p className="text-gray-600 mb-4">
            <strong>Test Steps:</strong>
          </p>
          <ol className="list-decimal list-inside text-gray-600 space-y-2 mb-4">
            <li>Use the ReactQuill editor below to create text with different font sizes</li>
            <li>Select text and use the font size dropdown (Aa icon) to change sizes</li>
            <li>Compare the "HtmlContentDisplay (Fixed)" with "Raw HTML (Before Fix)" sections</li>
            <li>The fixed version should properly display all font sizes</li>
          </ol>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">ReactQuill Editor</h3>
          <TextEditor
            theme="snow"
            value={content}
            onChange={setContent}
            modules={quillModules}
            formats={quillFormats}
            placeholder="Type here and test different font sizes..."
            style={{ minHeight: '150px' }}
            className="border rounded-md border-gray-300"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4 text-green-600">
              ✅ HtmlContentDisplay (Fixed)
            </h3>
            <div className="bg-gray-50 p-4 rounded border min-h-[100px]">
              <HtmlContentDisplay htmlString={content} />
            </div>
            <p className="text-sm text-gray-500 mt-2">
              This should properly display all font sizes from ReactQuill
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4 text-red-600">
              ❌ Raw HTML (Before Fix)
            </h3>
            <div className="bg-gray-50 p-4 rounded border min-h-[100px]" dangerouslySetInnerHTML={{ __html: content }} />
            <p className="text-sm text-gray-500 mt-2">
              This shows how it looked before the fix (font sizes may not work)
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">HTML Source</h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            <code>{content}</code>
          </pre>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">CSS Classes Reference</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-4">
              <code className="bg-gray-100 px-2 py-1 rounded">.ql-size-small</code>
              <span className="ql-size-small">Small text (0.75em)</span>
            </div>
            <div className="flex items-center space-x-4">
              <code className="bg-gray-100 px-2 py-1 rounded">normal</code>
              <span>Normal text (1em)</span>
            </div>
            <div className="flex items-center space-x-4">
              <code className="bg-gray-100 px-2 py-1 rounded">.ql-size-large</code>
              <span className="ql-size-large">Large text (1.5em)</span>
            </div>
            <div className="flex items-center space-x-4">
              <code className="bg-gray-100 px-2 py-1 rounded">.ql-size-huge</code>
              <span className="ql-size-huge">Huge text (2.5em)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
