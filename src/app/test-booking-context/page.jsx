'use client';

import HtmlContentDisplay from '@/components/HtmlContentDisplay';

export default function TestBookingContext() {
  const testContent = `
    <p>This is a test of font sizes in the booking context:</p>
    <ul>
      <li><span class="ql-size-small">Small text: Check-out time is 11:00 AM</span></li>
      <li>Normal text: Standard booking policies apply</li>
      <li><span class="ql-size-large">Large text: Contact us for special requests</span></li>
      <li><span class="ql-size-huge">Huge text: IMPORTANT NOTICE</span></li>
    </ul>
    <p>Mixed sizes: <span class="ql-size-small">small</span> and <span class="ql-size-large">large</span> and <span class="ql-size-huge">huge</span> text together.</p>
  `;

  return (
    <div className="h-screen bg-gray-100 p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          BookingFormComponent Context Test
        </h1>
        
        <div className="space-y-6">
          {/* Normal Context */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              ✅ Normal Context (White Background)
            </h3>
            <div className="bg-gray-50 p-4 rounded border">
              <HtmlContentDisplay htmlString={testContent} />
            </div>
          </div>

          {/* BookingFormComponent Context Simulation */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              🎯 BookingFormComponent Context Simulation
            </h3>
            <div className="booing-form flex text-white w-full h-fit overflow-hidden bg-black p-4 rounded">
              <div className="flex mx-auto w-full">
                <div className="flex w-full flex-col h-fit px-2">
                  <div className="w-full">
                    <HtmlContentDisplay htmlString={testContent} />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Alternative Context Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              🔍 Alternative Context (text-white class)
            </h3>
            <div className="text-white bg-black p-4 rounded">
              <HtmlContentDisplay htmlString={testContent} />
            </div>
          </div>

          {/* Raw HTML for Comparison */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              📄 Raw HTML (No HtmlContentDisplay)
            </h3>
            <div className="text-white bg-black p-4 rounded">
              <div dangerouslySetInnerHTML={{ __html: testContent }} />
            </div>
          </div>

          {/* CSS Classes Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              🧪 Direct CSS Classes Test
            </h3>
            <div className="text-white bg-black p-4 rounded space-y-2">
              <div className="ql-size-small">Direct ql-size-small class</div>
              <div className="ql-size-large">Direct ql-size-large class</div>
              <div className="ql-size-huge">Direct ql-size-huge class</div>
            </div>
          </div>

          {/* HTML Source */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              📋 HTML Source
            </h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
              <code>{testContent}</code>
            </pre>
          </div>

          {/* CSS Debug Info */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-yellow-800 mb-2">
              🔧 CSS Debug Information
            </h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p><strong>Expected Behavior:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Small text should be 0.75em (75% of normal size)</li>
                <li>Large text should be 1.5em (150% of normal size)</li>
                <li>Huge text should be 2.5em (250% of normal size)</li>
                <li>All text should inherit white color in black background contexts</li>
              </ul>
              <p className="mt-4"><strong>CSS Classes Applied:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><code>.html-content-display</code> and <code>.quill-content</code> on wrapper</li>
                <li><code>.booing-form</code> for BookingFormComponent context</li>
                <li><code>.text-white</code> for color inheritance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
