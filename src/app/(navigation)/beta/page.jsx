import React, { Suspense } from 'react'
// import LandinPageImage from '@/components/landingpage/LandinPageImage'
import Navbar from '@/components/Navbar'
import LandingpageCarousel from '@/components/LandingpageCarousel'

export default function page() {
  return (
    <div className="page-wrapper flex w-full text-white overflow-hidden">
      <div className='flex flex-col w-full h-fit overflow-y-auto'>
        {/* <LandinPageImage/> */}
        <LandingpageCarousel/>
        <Suspense fallback={null}>
          <Navbar/>
        </Suspense>
      </div>
    </div>
  )
}
