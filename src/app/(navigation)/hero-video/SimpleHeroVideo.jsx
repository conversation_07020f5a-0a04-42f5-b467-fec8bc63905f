'use client';

import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import LoadingComponent from '@/components/LoadingComponent';

export default function SimpleHeroVideo({ videoPath }) {
  const videoRef = useRef(null);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !videoPath) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    // Simple event handlers
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleError = () => {
      setIsLoading(false);
      setHasError(true);
      // Redirect to 360s after error
      setTimeout(() => {
        router.push('/360s?id=New_entrance_360_002');
      }, 2000);
    };
    const handleEnded = () => {
      // Redirect to 360s when video ends
      router.push('/360s?id=New_entrance_360_002');
    };

    // Add event listeners
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);
    video.addEventListener('ended', handleEnded);

    // Try to play the video
    const playVideo = async () => {
      try {
        video.muted = true; // Ensure muted for autoplay
        await video.play();
        setIsLoading(false);
      } catch (error) {
        console.log('Autoplay failed:', error);
        setIsLoading(false);
        // Don't treat autoplay failure as an error - user can manually play
      }
    };

    // Start playing when video can play
    video.addEventListener('canplay', playVideo, { once: true });

    // Cleanup
    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('canplay', playVideo);
    };
  }, [videoPath, router]);

  // Redirect if no video path
  useEffect(() => {
    if (!videoPath) {
      const timer = setTimeout(() => {
        router.push('/360s?id=entrance_360');
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [videoPath, router]);

  if (!videoPath) {
    return (
      // <div className="absolute inset-0 flex items-center justify-center bg-black">
      //   <div className="text-white text-center">
      //     <p className="text-lg mb-2">No video available</p>
      //     <p className="text-sm opacity-75">Redirecting to 360° view...</p>
      //   </div>
      // </div>
      <LoadingComponent/>
    );
  }

  if (hasError) {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black">
        <div className="text-white text-center">
          <p className="text-lg mb-2">Video could not be loaded</p>
          <p className="text-sm opacity-75">Redirecting to 360° view...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 w-full h-full">
      {/* Loading overlay */}
      {isLoading && (
        // <div className="absolute inset-0 flex items-center justify-center bg-black z-10">
        //   <div className="text-white text-center">
        //     <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        //     <p className="text-sm opacity-75">Loading video...</p>
        //   </div>
        // </div>
        <LoadingComponent/>
      )}

      {/* Simple video element */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        src={videoPath}
        autoPlay
        muted
        playsInline
        preload="auto"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
      >
        <source src={videoPath} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
}
