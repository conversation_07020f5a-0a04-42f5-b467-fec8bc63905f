import React from 'react';
import _360LandingPageWrapper from '@/components/360s/_360LandingPageWrapper';

export default async function page(props) {
  const searchParams = await props.searchParams
  const query = searchParams.id
  // console.log(query)
  return (
    <div className="w-full bg-black overflow-hidden">
      <_360LandingPageWrapper query={query}/>
    </div>
  )
}

export const metadata = {
  title: 'elephantislandsbotswana - 360 panaromas',
  description: 'View and manage 360° panoramic images for Elephant Island Lodge',
};

