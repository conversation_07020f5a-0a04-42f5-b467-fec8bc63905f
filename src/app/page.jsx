import React, { Suspense } from 'react'
import ImageScalerComponent from '@/components/ImageScalerComponent';
import Navbar from '@/components/Navbar';
import LandingpageCarousel from '@/components/LandingpageCarouselUpdate';
import CountdownTimer from '@/components/CountdownTimerUpdate';

export default function page() {
  return (
    <main className="flex w-full h-full items-center justify-center overflow-hidden">
      <LandingpageCarousel/>
      <CountdownTimer targetDate={new Date('2025-09-06T00:00:00')} />
    </main>
  )
}
