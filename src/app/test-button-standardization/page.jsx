'use client';

import React, { useState } from 'react';
import ActionButtonGroup, { ActionButton } from '@/components/pages/ActionButtonGroup';

/**
 * Comprehensive test page for standardized button system
 * Tests all button variants, responsive behavior, and functionality
 */

const TestButtonStandardizationPage = () => {
  const [mode, setMode] = useState('view');
  const [isLoading, setIsLoading] = useState(false);
  const [hasContent, setHasContent] = useState(true);
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, status, message) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { test, status, message, timestamp }]);
  };

  const handleButtonClick = (buttonName) => {
    addTestResult('Button Click', 'PASS', `${buttonName} button clicked successfully`);
  };

  const handleLoadingTest = async () => {
    setIsLoading(true);
    addTestResult('Loading State', 'INFO', 'Testing loading state...');
    
    setTimeout(() => {
      setIsLoading(false);
      addTestResult('Loading State', 'PASS', 'Loading state test completed');
    }, 2000);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS': return 'text-green-600 bg-green-50';
      case 'FAIL': return 'text-red-600 bg-red-50';
      case 'INFO': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Button Standardization Test Suite
          </h1>
          <p className="text-gray-600">
            Comprehensive testing of the standardized ActionButtonGroup component and individual ActionButton variants.
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Controls</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setMode(mode === 'view' ? 'edit' : 'view')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Toggle Mode ({mode})
            </button>
            <button
              onClick={() => setHasContent(!hasContent)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Toggle Content ({hasContent ? 'Has Content' : 'No Content'})
            </button>
            <button
              onClick={handleLoadingTest}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Test Loading State
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear Results
            </button>
          </div>
        </div>

        {/* ActionButtonGroup Tests */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* View Mode Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">ActionButtonGroup - View Mode</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-700">Sample Component Header</h4>
                <ActionButtonGroup
                  mode="view"
                  isLoading={isLoading}
                  hasContent={hasContent}
                  onEdit={() => handleButtonClick('Edit')}
                  onDelete={() => handleButtonClick('Delete')}
                  customButtons={[
                    {
                      mode: 'view',
                      variant: 'success',
                      onClick: () => handleButtonClick('Save Section'),
                      label: 'Save Section'
                    }
                  ]}
                />
              </div>
              <p className="text-sm text-gray-600">This demonstrates view mode buttons with custom Save Section button.</p>
            </div>
          </div>

          {/* Edit Mode Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">ActionButtonGroup - Edit Mode</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-700">Sample Component Header</h4>
                <ActionButtonGroup
                  mode="edit"
                  isLoading={isLoading}
                  hasContent={hasContent}
                  onSave={() => handleButtonClick('Save')}
                  onCancel={() => handleButtonClick('Cancel')}
                  onDirectSave={() => handleButtonClick('Save Direct')}
                  onLoadSample={() => handleButtonClick('Load Sample')}
                  showDirectSave={true}
                  showLoadSample={true}
                />
              </div>
              <p className="text-sm text-gray-600">This demonstrates edit mode buttons with all utility buttons enabled.</p>
            </div>
          </div>
        </div>

        {/* Individual Button Variants Test */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Individual ActionButton Variants</h3>
          
          {/* Button Sizes */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Button Sizes</h4>
            <div className="flex flex-wrap items-center gap-4">
              <ActionButton size="small" onClick={() => handleButtonClick('Small')}>
                Small Button
              </ActionButton>
              <ActionButton size="default" onClick={() => handleButtonClick('Default')}>
                Default Button
              </ActionButton>
              <ActionButton size="large" onClick={() => handleButtonClick('Large')}>
                Large Button
              </ActionButton>
            </div>
          </div>

          {/* Button Variants */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Button Variants</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <ActionButton variant="primary" onClick={() => handleButtonClick('Primary')}>
                Primary
              </ActionButton>
              <ActionButton variant="secondary" onClick={() => handleButtonClick('Secondary')}>
                Secondary
              </ActionButton>
              <ActionButton variant="destructive" onClick={() => handleButtonClick('Destructive')}>
                Destructive
              </ActionButton>
              <ActionButton variant="success" onClick={() => handleButtonClick('Success')}>
                Success
              </ActionButton>
              <ActionButton variant="purple" onClick={() => handleButtonClick('Purple')}>
                Purple
              </ActionButton>
              <ActionButton variant="outlinedPrimary" onClick={() => handleButtonClick('Outlined Primary')}>
                Outlined Primary
              </ActionButton>
              <ActionButton variant="outlinedDestructive" onClick={() => handleButtonClick('Outlined Destructive')}>
                Outlined Destructive
              </ActionButton>
            </div>
          </div>

          {/* Button States */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Button States</h4>
            <div className="flex flex-wrap gap-4">
              <ActionButton onClick={() => handleButtonClick('Normal')}>
                Normal State
              </ActionButton>
              <ActionButton disabled onClick={() => handleButtonClick('Disabled')}>
                Disabled State
              </ActionButton>
              <ActionButton loading loadingText="Loading..." onClick={() => handleButtonClick('Loading')}>
                Loading State
              </ActionButton>
            </div>
          </div>
        </div>

        {/* Responsive Test */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Responsive Design Test</h3>
          <p className="text-sm text-gray-600 mb-4">
            Resize your browser window to test responsive behavior. Buttons should wrap appropriately on smaller screens.
          </p>
          
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-700">Responsive Button Group</h4>
              <ActionButtonGroup
                mode="edit"
                isLoading={false}
                hasContent={true}
                onSave={() => handleButtonClick('Responsive Save')}
                onCancel={() => handleButtonClick('Responsive Cancel')}
                onDirectSave={() => handleButtonClick('Responsive Direct Save')}
                onLoadSample={() => handleButtonClick('Responsive Load Sample')}
                showDirectSave={true}
                showLoadSample={true}
                responsive={true}
                customButtons={[
                  {
                    variant: 'success',
                    onClick: () => handleButtonClick('Custom 1'),
                    label: 'Custom Button 1'
                  },
                  {
                    variant: 'purple',
                    onClick: () => handleButtonClick('Custom 2'),
                    label: 'Custom Button 2'
                  }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Test Results</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-md border ${getStatusColor(result.status)}`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <span className="font-medium">{result.test}</span>
                      <span className={`ml-2 px-2 py-1 text-xs rounded ${getStatusColor(result.status)}`}>
                        {result.status}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">{result.timestamp}</span>
                  </div>
                  <p className="mt-1 text-sm">{result.message}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestButtonStandardizationPage;
