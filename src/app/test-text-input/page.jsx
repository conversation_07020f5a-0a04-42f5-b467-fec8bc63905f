'use client';

import { useState } from 'react';
import TextEditor from '@/components/TextEditor';

export default function TestTextInputPage() {
  const [content, setContent] = useState('');
  const [debugInfo, setDebugInfo] = useState('');

  const handleContentChange = (newContent) => {
    setContent(newContent);
    setDebugInfo(`Content updated: ${newContent.length} characters`);
  };

  const handleTestInput = () => {
    const testContent = '<p>This is a test input from button click.</p>';
    setContent(testContent);
  };

  const handleClear = () => {
    setContent('');
    setDebugInfo('Content cleared');
  };

  return (
    <div className="h-full overflow-y-auto bg-gray-100 py-8">
      <div className="max-w-6xl mx-auto px-4 h-fit">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Text Input Test
            </h1>
            <p className="text-gray-600">
              Simple test to verify text input functionality in the TextEditor component.
            </p>
          </div>

          {/* Status and Debug Info */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Test Instructions:</h3>
            <ol className="text-blue-700 text-sm space-y-1 list-decimal list-inside">
              <li>Click in the editor area below (white box with toolbar)</li>
              <li>Try typing some text - you should see a cursor and text appearing</li>
              <li>Try using Bold (B) and Italic (I) buttons</li>
              <li>Try the font dropdown and color picker</li>
              <li>Check that the preview section updates in real-time</li>
            </ol>
            {debugInfo && (
              <div className="mt-3 p-2 bg-blue-100 rounded text-sm">
                <strong>Debug:</strong> {debugInfo}
              </div>
            )}
          </div>

          {/* Control buttons */}
          <div className="mb-6 flex gap-3">
            <button
              onClick={handleTestInput}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Insert Test Text
            </button>
            <button
              onClick={handleClear}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Clear All
            </button>
          </div>

          {/* Simple Test Editor - Removed (was using Quill) */}

          {/* TextEditor Component */}
          <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              TextEditor Component:
            </h3>
            <TextEditor
              value={content}
              onChange={handleContentChange}
              placeholder="Click here and start typing to test text input..."
              className="w-full"
              minHeight="250px"
            />
          </div>

          {/* Status Display */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-lg font-medium text-gray-800 mb-2">
                Content Length:
              </h4>
              <div className="text-2xl font-bold text-blue-600">
                {content.length} characters
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {content ? 'Content detected ✅' : 'No content yet ❌'}
              </div>
            </div>

            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-lg font-medium text-gray-800 mb-2">
                Raw HTML:
              </h4>
              <div className="text-xs text-gray-600 font-mono bg-white p-2 rounded border max-h-32 overflow-y-auto">
                {content || 'No content...'}
              </div>
            </div>
          </div>

          {/* Expected Behavior */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <h4 className="text-lg font-medium text-yellow-800 mb-2">
              Expected Behavior:
            </h4>
            <ul className="text-yellow-700 text-sm space-y-1 list-disc list-inside">
              <li>Clicking in the editor should show a blinking cursor</li>
              <li>Typing should immediately show text in the editor</li>
              <li>The preview section should update in real-time</li>
              <li>The character count should increase as you type</li>
              <li>Bold/Italic buttons should work on selected text</li>
              <li>Font dropdown should show font options</li>
              <li>Color picker should allow text color changes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
