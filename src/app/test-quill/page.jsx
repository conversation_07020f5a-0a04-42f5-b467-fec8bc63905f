'use client';

import { useState } from 'react';
import TextEditor from '@/components/common/TextEditor';
import HtmlContentDisplay from '@/components/HtmlContentDisplay';

const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'size', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

export default function TestQuillPage() {
  const [content1, setContent1] = useState('<p>Test content 1</p>');
  const [content2, setContent2] = useState('<p>Test content 2</p>');
  const [content3, setContent3] = useState('<p>Test content 3</p>');

  return (
    <div className="min-h-screen bg-gray-100 p-8 mb-20 overflow-y-auto">
      <div className="max-w-4xl h-fit mx-auto overflow-y-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          ReactQuill Deprecation Warning Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Instructions
          </h2>
          <p className="text-gray-600 mb-4">
            <strong>Font Size Testing:</strong> Use the font size dropdown in the toolbar to test different sizes (small, normal, large, huge).
            Compare the "HtmlContentDisplay" preview with the "Raw HTML" preview to verify font sizes are rendered correctly.
          </p>
          <p className="text-gray-600 mb-4">
            <strong>Console Check:</strong> Open your browser's developer console and check for any deprecation warnings
            related to 'DOMNodeInserted' or mutation events. If the fix is working correctly,
            you should not see any such warnings.
          </p>
          <p className="text-gray-600">
            Try typing in the editors below, change font sizes, and watch both the console for warnings and the preview sections for proper font size rendering.
          </p>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Editor 1</h3>
            <TextEditor
              theme="snow"
              value={content1}
              onChange={setContent1}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type something here..."
              style={{ minHeight: '120px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Editor 2</h3>
            <TextEditor
              theme="snow"
              value={content2}
              onChange={setContent2}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type something here..."
              style={{ minHeight: '120px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Editor 3</h3>
            <TextEditor
              theme="snow"
              value={content3}
              onChange={setContent3}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Type something here..."
              style={{ minHeight: '120px' }}
              className="border rounded-md border-gray-300"
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Content Preview with HtmlContentDisplay</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-700">Editor 1 Content:</h4>
              <div className="bg-gray-50 p-3 rounded border">
                <HtmlContentDisplay htmlString={content1} />
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Editor 2 Content:</h4>
              <div className="bg-gray-50 p-3 rounded border">
                <HtmlContentDisplay htmlString={content2} />
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Editor 3 Content:</h4>
              <div className="bg-gray-50 p-3 rounded border">
                <HtmlContentDisplay htmlString={content3} />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Raw HTML Preview (for comparison)</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-700">Editor 1 Raw HTML:</h4>
              <div className="bg-gray-50 p-3 rounded border" dangerouslySetInnerHTML={{ __html: content1 }} />
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Editor 2 Raw HTML:</h4>
              <div className="bg-gray-50 p-3 rounded border" dangerouslySetInnerHTML={{ __html: content2 }} />
            </div>
            <div>
              <h4 className="font-medium text-gray-700">Editor 3 Raw HTML:</h4>
              <div className="bg-gray-50 p-3 rounded border" dangerouslySetInnerHTML={{ __html: content3 }} />
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">
            Console Check
          </h3>
          <p className="text-yellow-700">
            If you see this page loading without any deprecation warnings in the console, 
            the fix is working correctly! Try interacting with the editors above.
          </p>
        </div>
      </div>
    </div>
  );
}
