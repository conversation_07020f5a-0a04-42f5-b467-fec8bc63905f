'use client';

import React, { useState } from 'react';

const BookingFixTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test, status, message) => {
    setTestResults(prev => [...prev, {
      id: Date.now() + Math.random(),
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    addResult('Test Started', 'INFO', 'Testing BookingDetailsText data persistence fix');

    // Test 1: Test individual section endpoint
    try {
      const testData = { details: `Test booking content - ${Date.now()}` };
      const response = await fetch('/api/pages/booking', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testData)
      });

      const result = await response.json();
      if (result.success) {
        addResult('Individual Section API', 'PASS', 'Successfully saved to /api/pages/booking');
        
        // Verify the data was saved
        const getResponse = await fetch('/api/pages');
        const getData = await getResponse.json();
        
        if (getData.success && getData.data.booking.details === testData.details) {
          addResult('Data Persistence Check', 'PASS', 'Data correctly persisted in database');
        } else {
          addResult('Data Persistence Check', 'FAIL', 'Data not found in database after save');
        }
      } else {
        addResult('Individual Section API', 'FAIL', `API error: ${result.message}`);
      }
    } catch (error) {
      addResult('Individual Section API', 'FAIL', `Request error: ${error.message}`);
    }

    // Test 2: Test main PATCH endpoint with booking section
    try {
      const testData2 = { details: `Main endpoint test - ${Date.now()}` };
      const response = await fetch('/api/pages', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ booking: testData2 })
      });

      const result = await response.json();
      if (result.success) {
        addResult('Main PATCH Endpoint', 'PASS', 'Successfully saved booking via main endpoint');
        
        // Verify the data was saved
        const getResponse = await fetch('/api/pages');
        const getData = await getResponse.json();
        
        if (getData.success && getData.data.booking.details === testData2.details) {
          addResult('Main Endpoint Persistence', 'PASS', 'Data correctly persisted via main endpoint');
        } else {
          addResult('Main Endpoint Persistence', 'FAIL', 'Data not persisted via main endpoint');
        }
      } else {
        addResult('Main PATCH Endpoint', 'FAIL', `API error: ${result.message}`);
      }
    } catch (error) {
      addResult('Main PATCH Endpoint', 'FAIL', `Request error: ${error.message}`);
    }

    // Test 3: Simulate the actual component flow
    try {
      const componentTestData = { details: `Component flow test - ${Date.now()}` };
      
      // This simulates what PagesManagement.handleSave does
      const response = await fetch('/api/pages/booking', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(componentTestData)
      });

      const result = await response.json();
      if (result.success) {
        addResult('Component Flow Simulation', 'PASS', 'Component data flow works correctly');
      } else {
        addResult('Component Flow Simulation', 'FAIL', `Component flow error: ${result.message}`);
      }
    } catch (error) {
      addResult('Component Flow Simulation', 'FAIL', `Component flow error: ${error.message}`);
    }

    setIsRunning(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            🔧 BookingDetailsText Persistence Fix Test
          </h1>
          <p className="text-gray-600 mb-6">
            This test verifies that the booking section data persistence issue has been resolved.
            It tests both API endpoints and simulates the actual component data flow.
          </p>
          
          <button
            onClick={runTests}
            disabled={isRunning}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isRunning ? 'Running Tests...' : 'Run Persistence Tests'}
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Results</h2>
          
          {testResults.length === 0 ? (
            <p className="text-gray-500 italic">No test results yet. Click "Run Persistence Tests" to begin.</p>
          ) : (
            <div className="space-y-3">
              {testResults.map((result) => (
                <div
                  key={result.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    result.status === 'PASS'
                      ? 'bg-green-50 border-green-500'
                      : result.status === 'FAIL'
                      ? 'bg-red-50 border-red-500'
                      : 'bg-blue-50 border-blue-500'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-800">{result.test}</h3>
                      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    </div>
                    <div className="flex flex-col items-end">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          result.status === 'PASS'
                            ? 'bg-green-100 text-green-800'
                            : result.status === 'FAIL'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}
                      >
                        {result.status}
                      </span>
                      <span className="text-xs text-gray-500 mt-1">{result.timestamp}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">🔍 What This Test Checks:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Individual section API endpoint (/api/pages/booking) accepts booking data</li>
            <li>• Main PATCH endpoint (/api/pages) now includes 'booking' in validSections</li>
            <li>• Data actually persists in the database after save operations</li>
            <li>• Component data flow simulation matches real-world usage</li>
          </ul>
        </div>

        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">🛠️ Fixes Applied:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Added 'booking' to validSections array in /api/pages PATCH endpoint</li>
            <li>• Fixed PagesForm onSectionSave callback to properly pass sectionData parameter</li>
            <li>• Ensured BookingDetailsText component data flows correctly to database</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default BookingFixTest;
