'use client';

import React, { useState, useEffect } from 'react';
import LocationAndContactsInput from '@/components/pages/LocationAndContactsInput';

/**
 * Test page for LocationAndContactsInput component
 * 
 * This page tests:
 * - Component rendering
 * - API integration with pages endpoint
 * - Form data handling
 * - Rich text editing functionality
 * - Data persistence
 */

const TestLocationContactsInputPage = () => {
  const [formData, setFormData] = useState({
    title: '',
    body: '',
    details: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState([]);

  // Load initial data from API
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success && data.data.locationAndcontacts) {
        setFormData(data.data.locationAndcontacts);
        addTestResult('Initial Data Load', 'PASS', 'Successfully loaded existing data from API');
      } else {
        addTestResult('Initial Data Load', 'INFO', 'No existing data found, starting with empty form');
      }
    } catch (error) {
      addTestResult('Initial Data Load', 'FAIL', `Error loading data: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const addTestResult = (test, status, message) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { test, status, message, timestamp }]);
  };

  // Handle form field changes
  const handleQuillChange = (content, section, field) => {
    setFormData(prev => ({
      ...prev,
      [field]: content
    }));
    addTestResult('Form Update', 'PASS', `Updated ${field} field`);
  };

  // Handle section save
  const handleSectionSave = async () => {
    try {
      setIsLoading(true);
      addTestResult('Save Operation', 'INFO', 'Starting save operation...');

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'locationAndcontacts',
          data: formData
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        addTestResult('Save Operation', 'PASS', 'Successfully saved data to API');
        
        // Verify data persistence
        await verifyDataPersistence();
      } else {
        addTestResult('Save Operation', 'FAIL', `Save failed: ${result.message}`);
      }
    } catch (error) {
      addTestResult('Save Operation', 'FAIL', `Save error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify that saved data persists
  const verifyDataPersistence = async () => {
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success && data.data.locationAndcontacts) {
        const savedData = data.data.locationAndcontacts;
        const isMatch = JSON.stringify(savedData) === JSON.stringify(formData);
        
        if (isMatch) {
          addTestResult('Data Persistence', 'PASS', 'Saved data matches form data');
        } else {
          addTestResult('Data Persistence', 'FAIL', 'Data mismatch detected');
        }
      } else {
        addTestResult('Data Persistence', 'FAIL', 'Could not retrieve saved data');
      }
    } catch (error) {
      addTestResult('Data Persistence', 'FAIL', `Persistence check error: ${error.message}`);
    }
  };

  // Run comprehensive tests
  const runTests = async () => {
    setTestResults([]);
    addTestResult('Test Suite', 'INFO', 'Starting comprehensive tests...');

    // Test 1: Component rendering
    addTestResult('Component Render', 'PASS', 'LocationAndContactsInput component rendered successfully');

    // Test 2: API connectivity
    try {
      const response = await fetch('/api/pages');
      const data = await response.json();
      
      if (data.success) {
        addTestResult('API Connectivity', 'PASS', 'API endpoint is accessible');
      } else {
        addTestResult('API Connectivity', 'FAIL', 'API returned error response');
      }
    } catch (error) {
      addTestResult('API Connectivity', 'FAIL', `API connection failed: ${error.message}`);
    }

    // Test 3: Form validation
    const testFormData = { title: '', body: '', details: '' };
    if (!testFormData.title && !testFormData.body && !testFormData.details) {
      addTestResult('Form Validation', 'PASS', 'Empty form validation works correctly');
    }

    // Test 4: Sample data test
    const sampleData = {
      title: '<h2 style="color: white;">Test Title</h2>',
      body: '<p style="color: white;">Test body content</p>',
      details: '<p style="color: white;">Test details content</p>'
    };
    
    setFormData(sampleData);
    addTestResult('Sample Data', 'PASS', 'Sample data loaded into form');

    addTestResult('Test Suite', 'COMPLETE', 'All tests completed');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS': return 'text-green-600 bg-green-50';
      case 'FAIL': return 'text-red-600 bg-red-50';
      case 'INFO': return 'text-blue-600 bg-blue-50';
      case 'COMPLETE': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="h-screen w-full bg-gray-50 py-8">
      <div className='h-full w-full overflow-y-auto'>
        <div className="max-w-6xl mx-auto px-4 h-fit">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              LocationAndContactsInput Component Test
            </h1>
            <p className="text-gray-600">
              Testing the new LocationAndContactsInput component functionality, API integration, and data persistence.
            </p>
          </div>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Controls</h2>
            <div className="flex space-x-4">
              <button
                onClick={runTests}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Run Tests
              </button>
              <button
                onClick={loadInitialData}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Reload Data
              </button>
              <button
                onClick={clearResults}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Clear Results
              </button>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Results</h2>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-md border ${getStatusColor(result.status)}`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{result.test}</span>
                        <span className={`ml-2 px-2 py-1 text-xs rounded ${getStatusColor(result.status)}`}>
                          {result.status}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">{result.timestamp}</span>
                    </div>
                    <p className="mt-1 text-sm">{result.message}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Component Test */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Component Test</h2>
            <LocationAndContactsInput
              formData={formData}
              errors={errors}
              onQuillChange={handleQuillChange}
              onSectionSave={handleSectionSave}
              isLoading={isLoading}
            />
          </div>

          {/* Current Form Data Display */}
          <div className="bg-white rounded-lg shadow-md p-6 mt-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Current Form Data</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700">Title:</h3>
                <div className="p-2 bg-gray-50 rounded border text-sm">
                  {formData.title || 'No title set'}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Body:</h3>
                <div className="p-2 bg-gray-50 rounded border text-sm">
                  {formData.body || 'No body content set'}
                </div>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Details:</h3>
                <div className="p-2 bg-gray-50 rounded border text-sm">
                  {formData.details || 'No details set'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLocationContactsInputPage;
