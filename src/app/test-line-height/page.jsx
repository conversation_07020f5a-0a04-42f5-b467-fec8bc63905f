'use client';

import React, { useState } from 'react';
import TextEditor from '@/components/common/TextEditor';

export default function TestLineHeightPage() {
  const [content, setContent] = useState(`
    <p>This is a test paragraph with normal line height. Select this text and try applying different line heights to see the visual changes.</p>
    
    <p>This is another paragraph. You can select portions of text and apply different line heights to see how they affect the spacing between lines.</p>
    
    <p>Try selecting this text and applying:</p>
    <ul>
      <li>1.0 (Tight) - Very compact line spacing</li>
      <li>1.2 (Normal) - Standard line spacing</li>
      <li>1.5 (Relaxed) - More comfortable reading spacing</li>
      <li>2.0 (Double) - Double spaced lines</li>
    </ul>
    
    <p>You can also combine line height with other formatting like <strong>bold text</strong>, <em>italic text</em>, and different font sizes.</p>
  `);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    console.log('Content updated:', newContent);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Line Height Functionality Test
          </h1>
          
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-3">
              Instructions:
            </h2>
            <ol className="list-decimal list-inside space-y-2 text-gray-700">
              <li>Select any text in the editor below</li>
              <li>Choose a line height option from the dropdown (1.0 Tight, 1.2 Normal, 1.5 Relaxed, 2.0 Double)</li>
              <li>Observe the immediate visual changes in line spacing</li>
              <li>Try combining line height with bold, italic, and different font sizes</li>
              <li>Test applying different line heights to different paragraphs</li>
            </ol>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              TextEditor with Line Height Testing:
            </h3>
            <div className="border border-gray-300 rounded-lg">
              <TextEditor
                value={content}
                onChange={handleContentChange}
                placeholder="Start typing or select existing text to test line height functionality..."
                style={{ minHeight: '400px' }}
              />
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              Current Content (HTML):
            </h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
                {content}
              </pre>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">
              Rendered Preview:
            </h3>
            <div 
              className="border border-gray-300 rounded-lg p-4 bg-white"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>

          <div className="text-sm text-gray-600">
            <p><strong>Expected Behavior:</strong></p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Selecting text and applying line height should show immediate visual changes</li>
              <li>Different line height values should produce noticeably different spacing</li>
              <li>Line height should work with other formatting (bold, italic, font sizes)</li>
              <li>Multiple paragraphs can have different line heights applied independently</li>
              <li>The dropdown should reset after applying a line height</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
