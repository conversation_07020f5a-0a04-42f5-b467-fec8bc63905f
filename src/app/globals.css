@import "tailwindcss";
@import "../styles/fonts.css";

/* Configure Tailwind with custom font family */
@theme {
  --font-family-trasandina: 'Trasandina', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Admin interface base styling */
.admin-interface {
  font-family: var(--font-family-trasandina);
}

/* Offline notification animation */
@keyframes slide-down {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

@layer utilities {
  .odd-even tr:nth-child(odd) {
    @apply bg-red-500;
  }
  .odd-even tr:nth-child(even) {
    @apply bg-blue-500;
  }
}

/* a{
  color: white !important;
  text-decoration: none !important
} */

