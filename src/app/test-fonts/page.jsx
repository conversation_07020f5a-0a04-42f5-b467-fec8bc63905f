'use client';

import { useState } from 'react';
import TextEditor from '@/components/TextEditor';

export default function TestFontsPage() {
  const [editorContent, setEditorContent] = useState('');

  const handleContentChange = (content) => {
    setEditorContent(content);
  };

  const handleClear = () => {
    setEditorContent('');
  };

  const handleFontSample = () => {
    const sampleHtml = `<p><strong>Font Family Test</strong></p><p><span class="ql-font-Arial">This text is in Arial font family.</span></p><p><span class="ql-font-Times-New-Roman">This text is in Times New Roman font family.</span></p><p><span class="ql-font-Helvetica">This text is in Helvetica font family.</span></p><p><span class="ql-font-Georgia">This text is in Georgia font family.</span></p><p><span class="ql-font-Verdana">This text is in Verdana font family.</span></p><p><em>Try selecting text and changing fonts using the dropdown!</em></p>`;
    setEditorContent(sampleHtml);
  };

  const handleColorSample = () => {
    const sampleHtml = `<p><strong>Color Test</strong></p><p><span style="color: rgb(230, 0, 0);">Red text</span></p><p><span style="color: rgb(0, 138, 0);">Green text</span></p><p><span style="color: rgb(0, 0, 255);">Blue text</span></p><p><span style="color: rgb(255, 165, 0);">Orange text</span></p><p><span style="color: rgb(128, 0, 128);">Purple text</span></p>`;
    setEditorContent(sampleHtml);
  };

  const handleMixedSample = () => {
    const sampleHtml = `<p><strong style="color: rgb(230, 0, 0);" class="ql-font-Georgia">Bold Red Georgia Text</strong></p><p><em style="color: rgb(0, 138, 0);" class="ql-font-Times-New-Roman">Italic Green Times New Roman</em></p><p><span style="color: rgb(0, 0, 255);" class="ql-font-Arial">Blue Arial Text</span></p><p><strong><em style="color: rgb(255, 165, 0);" class="ql-font-Verdana">Bold Italic Orange Verdana</em></strong></p>`;
    setEditorContent(sampleHtml);
  };

  const handleDebugInfo = () => {
    console.log('Current editor content:', editorContent);
    console.log('Available fonts should be: Arial, Times-New-Roman, Helvetica, Georgia, Verdana');

    // Check if Quill CSS is loaded
    const quillCSS = document.querySelector('link[href*="quill"]');
    console.log('Quill CSS loaded:', !!quillCSS);

    // Check if our custom CSS is loaded
    const customCSS = document.querySelector('style, link[href*="quill-fixes"]');
    console.log('Custom CSS loaded:', !!customCSS);

    alert('Debug info logged to console. Open browser developer tools to see details.');
  };

  return (
    <div className="min-h-full overflow-y-auto bg-gray-100 py-8">
      <div className="max-w-6xl mx-auto px-4 h-fit">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Font Family & Color Test
            </h1>
            <p className="text-gray-600">
              Test the font family dropdown and color picker functionality.
            </p>
          </div>

          {/* Control buttons */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={handleFontSample}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Load Font Samples
            </button>
            <button
              onClick={handleColorSample}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Load Color Samples
            </button>
            <button
              onClick={handleMixedSample}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Load Mixed Samples
            </button>
            <button
              onClick={handleClear}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Clear Content
            </button>
            <button
              onClick={handleDebugInfo}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
            >
              Debug Info
            </button>
          </div>

          {/* Instructions */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Instructions:</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Use the toolbar buttons to format text (Bold, Italic)</li>
              <li>• Click the font dropdown (should show "Sans Serif" by default) to change font families</li>
              <li>• Click the color picker (A with underline) to change text colors</li>
              <li>• Select existing text to apply formatting</li>
              <li>• The preview below should show all formatting in real-time</li>
            </ul>
          </div>

          {/* TextEditor Component */}
          <TextEditor
            value={editorContent}
            onChange={handleContentChange}
            placeholder="Type here to test font families and colors..."
            className="w-full"
            minHeight="300px"
          />

          {/* Font Family Reference */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-lg font-medium text-gray-800 mb-3">
                Available Font Families:
              </h4>
              <div className="space-y-2 text-sm">
                <div style={{ fontFamily: 'Arial, sans-serif' }}>Arial (Sans-serif)</div>
                <div style={{ fontFamily: 'Times New Roman, serif' }}>Times New Roman (Serif)</div>
                <div style={{ fontFamily: 'Helvetica, sans-serif' }}>Helvetica (Sans-serif)</div>
                <div style={{ fontFamily: 'Georgia, serif' }}>Georgia (Serif)</div>
                <div style={{ fontFamily: 'Verdana, sans-serif' }}>Verdana (Sans-serif)</div>
              </div>
            </div>

            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-lg font-medium text-gray-800 mb-3">
                Raw HTML Output:
              </h4>
              <pre className="text-xs text-gray-600 whitespace-pre-wrap break-all max-h-40 overflow-y-auto">
                {editorContent || 'No content yet...'}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
