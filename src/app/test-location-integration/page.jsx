'use client';

import React, { useState, useEffect } from 'react';
import LocationAndContactsInput from '@/components/pages/LocationAndContactsInput';

/**
 * Simple integration test for LocationAndContactsInput component
 * Tests the component in isolation to verify it works correctly
 */

const TestLocationIntegrationPage = () => {
  const [formData, setFormData] = useState({
    title: '',
    body: '',
    details: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [testStatus, setTestStatus] = useState('Ready');

  // Mock the onQuillChange function that PagesForm would provide
  const handleQuillChange = (content, section, field) => {
    console.log('QuillChange called:', { content, section, field });
    setFormData(prev => ({
      ...prev,
      [field]: content
    }));
    setTestStatus(`Updated ${field} field`);
  };

  // Mock the onSectionSave function that PagesForm would provide
  const handleSectionSave = async () => {
    console.log('SectionSave called with data:', formData);
    setIsLoading(true);
    setTestStatus('Saving...');
    
    try {
      // Simulate API call
      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'locationAndcontacts',
          data: formData
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        setTestStatus('Save successful!');
      } else {
        setTestStatus(`Save failed: ${result.message}`);
      }
    } catch (error) {
      setTestStatus(`Save error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/api/pages');
        const data = await response.json();
        
        if (data.success && data.data.locationAndcontacts) {
          setFormData(data.data.locationAndcontacts);
          setTestStatus('Data loaded from API');
        } else {
          setTestStatus('No existing data found');
        }
      } catch (error) {
        setTestStatus(`Load error: ${error.message}`);
      }
    };

    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            LocationAndContactsInput Integration Test
          </h1>
          <p className="text-gray-600">
            Testing the component integration with PagesForm-like props and handlers.
          </p>
        </div>

        {/* Status Display */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Test Status</h2>
          <p className="text-sm text-gray-600">
            Status: <span className="font-medium text-blue-600">{testStatus}</span>
          </p>
          <p className="text-sm text-gray-600 mt-1">
            Loading: <span className="font-medium">{isLoading ? 'Yes' : 'No'}</span>
          </p>
        </div>

        {/* Component Test */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Component Test</h2>
          
          <LocationAndContactsInput
            formData={formData}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={handleSectionSave}
            isLoading={isLoading}
          />
        </div>

        {/* Debug Information */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Debug Information</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Current Form Data:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(formData, null, 2)}
              </pre>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Current Errors:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(errors, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        {/* Manual Test Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Manual Test Controls</h2>
          
          <div className="flex space-x-4">
            <button
              onClick={() => {
                setFormData({
                  title: '<h2 style="color: white;">Test Title</h2>',
                  body: '<p style="color: white;">Test body content</p>',
                  details: '<p style="color: white;">Test details content</p>'
                });
                setTestStatus('Sample data loaded');
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Load Sample Data
            </button>
            
            <button
              onClick={() => {
                setFormData({ title: '', body: '', details: '' });
                setTestStatus('Data cleared');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear Data
            </button>
            
            <button
              onClick={() => {
                setErrors({
                  'locationAndcontacts.title': 'Test title error',
                  'locationAndcontacts.body': 'Test body error',
                  'locationAndcontacts.details': 'Test details error'
                });
                setTestStatus('Test errors added');
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Test Errors
            </button>
            
            <button
              onClick={() => {
                setErrors({});
                setTestStatus('Errors cleared');
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Clear Errors
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLocationIntegrationPage;
