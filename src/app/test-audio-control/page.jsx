'use client'

import { useContextAudio } from '@/contexts/useContextAudio'
import AudioPlayer from '@/components/AudioPlayer'

export default function TestAudioControl() {
  const { playAuido, setPlayAuido, pauseAudio, setPauseAuido, isPlaying } = useContextAudio()

  const handleStartAudio = () => {
    console.log('Triggering audio playback via context')
    setPlayAuido(true)
  }

  const handlePauseAudio = () => {
    console.log('Pausing audio via context')
    setPauseAuido(true)
  }

  const handleResumeAudio = () => {
    console.log('Resuming audio via context')
    setPauseAuido(false)
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Audio Control Test</h1>
        
        {/* Audio Player Component */}
        <AudioPlayer />
        
        {/* Control Panel */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Audio Controls</h2>
          
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={handleStartAudio}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Start Audio (playAuido = true)
            </button>
            
            <button
              onClick={handlePauseAudio}
              className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
            >
              Pause Audio
            </button>
            
            <button
              onClick={handleResumeAudio}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Resume Audio
            </button>
          </div>
          
          {/* Status Display */}
          <div className="bg-gray-50 rounded p-4">
            <h3 className="text-lg font-medium text-gray-700 mb-2">Audio Context State:</h3>
            <div className="space-y-1 text-sm">
              <div>
                <span className="font-medium">playAuido:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${playAuido ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
                  {playAuido ? 'true' : 'false'}
                </span>
              </div>
              <div>
                <span className="font-medium">pauseAudio:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${pauseAudio ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'}`}>
                  {pauseAudio ? 'true' : 'false'}
                </span>
              </div>
              <div>
                <span className="font-medium">isPlaying:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${isPlaying ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'}`}>
                  {isPlaying ? 'true' : 'false'}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <h3 className="text-lg font-medium text-blue-800 mb-2">Test Instructions:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Click "Start Audio" to trigger playback via the playAuido context state</li>
            <li>• The AudioPlayer should respond to playAuido changes and start with fade-in</li>
            <li>• Use Pause/Resume to test the pauseAudio functionality</li>
            <li>• Watch the context state indicators to see real-time updates</li>
            <li>• Check browser console for debug messages</li>
          </ul>
        </div>

        {/* Browser Policy Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">⚠️ Browser Audio Policy:</h3>
          <p className="text-sm text-yellow-700">
            Modern browsers require user interaction before allowing audio playback.
            The AudioPlayer will not auto-play on page load - you must click "Start Audio"
            to begin playback. This is expected behavior for web security.
          </p>
        </div>
      </div>
    </div>
  )
}
