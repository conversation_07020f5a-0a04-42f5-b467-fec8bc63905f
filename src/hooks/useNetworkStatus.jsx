'use client';

import { useState, useEffect } from 'react';

/**
 * Custom hook to monitor network connectivity status
 * Uses Navigator.onLine API to detect online/offline status
 * @returns {boolean} isOnline - Current network connectivity status
 */
export default function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }

    // Set initial status
    setIsOnline(navigator.onLine);

    // Event handlers for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Cleanup event listeners on unmount
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
}
