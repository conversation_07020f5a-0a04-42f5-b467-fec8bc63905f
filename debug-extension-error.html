<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Extension Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .solution-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .warning {
            color: #856404;
            font-weight: bold;
        }
        .success {
            color: #155724;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Browser Extension Error Debug</h1>
        
        <div class="error-box">
            <h3>⚠️ Error Identified</h3>
            <p><strong>Error:</strong> "Could not establish connection. Receiving end does not exist."</p>
            <p><strong>Source:</strong> Browser Extension (NOT your video upload code)</p>
            <p><strong>Impact:</strong> This error does NOT affect your video upload functionality</p>
        </div>

        <div class="solution-box">
            <h3>✅ Quick Solutions</h3>
            
            <div class="step">
                <h4>Solution 1: Test Video Upload (Recommended)</h4>
                <p>Your video upload should work normally despite this error. Test it now:</p>
                <button onclick="testVideoUpload()">Test Video Upload Functionality</button>
                <div id="upload-test-result"></div>
            </div>

            <div class="step">
                <h4>Solution 2: Hide Extension Errors</h4>
                <ol>
                    <li>Open Chrome DevTools (F12)</li>
                    <li>Go to Console tab</li>
                    <li>Right-click on the extension error</li>
                    <li>Select "Hide messages from this source"</li>
                </ol>
            </div>

            <div class="step">
                <h4>Solution 3: Identify Problematic Extension</h4>
                <button onclick="listExtensions()">Check Browser Extensions</button>
                <div id="extension-list"></div>
            </div>

            <div class="step">
                <h4>Solution 4: Test in Incognito Mode</h4>
                <p>Open your site in incognito mode (Ctrl+Shift+N) to test without extensions</p>
            </div>
        </div>

        <div class="error-box">
            <h3>🔍 Common Causes</h3>
            <ul>
                <li><strong>Ad Blockers:</strong> uBlock Origin, AdBlock Plus</li>
                <li><strong>Password Managers:</strong> LastPass, 1Password, Bitwarden</li>
                <li><strong>Developer Tools:</strong> React DevTools, Vue DevTools</li>
                <li><strong>Video Downloaders:</strong> Video DownloadHelper, etc.</li>
                <li><strong>Social Media Extensions:</strong> Facebook, Twitter tools</li>
            </ul>
        </div>

        <div id="debug-results"></div>
    </div>

    <script>
        async function testVideoUpload() {
            const resultDiv = document.getElementById('upload-test-result');
            resultDiv.innerHTML = '<p>Testing video upload API...</p>';
            
            try {
                // Test the video upload endpoint
                const response = await fetch('/api/upload/video-gallery', {
                    method: 'OPTIONS' // Just test if endpoint is accessible
                });
                
                if (response.status === 200 || response.status === 405) {
                    resultDiv.innerHTML = `
                        <div style="color: green; margin-top: 10px;">
                            ✅ <strong>Video upload API is working!</strong><br>
                            The extension error does not affect your upload functionality.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div style="color: orange; margin-top: 10px;">
                            ⚠️ API response: ${response.status}<br>
                            This may be unrelated to the extension error.
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: red; margin-top: 10px;">
                        ❌ Network error: ${error.message}<br>
                        Check your network connection.
                    </div>
                `;
            }
        }

        function listExtensions() {
            const resultDiv = document.getElementById('extension-list');
            
            // We can't actually list extensions from a webpage for security reasons
            resultDiv.innerHTML = `
                <div style="margin-top: 10px;">
                    <p><strong>To check your extensions:</strong></p>
                    <ol>
                        <li>Type <code>chrome://extensions/</code> in your address bar</li>
                        <li>Look for recently updated or problematic extensions</li>
                        <li>Try disabling extensions one by one</li>
                        <li>Common culprits: Ad blockers, password managers, social media tools</li>
                    </ol>
                    <p><em>Note: For security reasons, websites cannot directly access your extension list.</em></p>
                </div>
            `;
        }

        // Check if we're in the context where the error occurs
        window.addEventListener('error', function(e) {
            if (e.message.includes('Could not establish connection')) {
                const debugDiv = document.getElementById('debug-results');
                debugDiv.innerHTML = `
                    <div class="error-box">
                        <h3>🎯 Extension Error Detected</h3>
                        <p><strong>Confirmed:</strong> The extension error is occurring on this page.</p>
                        <p><strong>Action:</strong> This confirms it's a browser extension issue, not your code.</p>
                    </div>
                `;
            }
        });

        // Log console errors to help identify the source
        const originalConsoleError = console.error;
        console.error = function(...args) {
            if (args.some(arg => typeof arg === 'string' && arg.includes('Could not establish connection'))) {
                const debugDiv = document.getElementById('debug-results');
                debugDiv.innerHTML = `
                    <div class="error-box">
                        <h3>📍 Extension Error Source Identified</h3>
                        <p>The error is being logged to console. This confirms it's from a browser extension.</p>
                        <p><strong>Your video upload functionality is not affected.</strong></p>
                    </div>
                `;
            }
            originalConsoleError.apply(console, args);
        };
    </script>
</body>
</html>
