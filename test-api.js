// Simple test script to verify the /api/pages endpoint
// Run with: node test-api.js

const testApiEndpoint = async () => {
  try {
    console.log('🔄 Testing /api/pages endpoint...');
    
    // Note: This would need to be run in a browser environment or with proper fetch polyfill
    // For now, this is just a template for testing
    
    const response = await fetch('http://localhost:3000/api/pages', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-cache'
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('📥 Full API response:', JSON.stringify(data, null, 2));

    if (data.success && data.data) {
      const locationData = data.data.locationAndcontacts;
      console.log('📍 Location and contacts data:', JSON.stringify(locationData, null, 2));
      
      if (locationData) {
        console.log('✅ Location data found:');
        console.log('  - Title:', locationData.title || '(empty)');
        console.log('  - Body:', locationData.body || '(empty)');
        console.log('  - Details:', locationData.details || '(empty)');
      } else {
        console.log('⚠️ No locationAndcontacts data found in response');
      }
    } else {
      console.log('❌ API response indicates failure or missing data');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testApiEndpoint = testApiEndpoint;
  console.log('🎯 Test function available as window.testApiEndpoint()');
}

// Instructions for manual testing
console.log(`
🧪 API Testing Instructions:

1. Browser Console Test:
   - Open browser dev tools
   - Go to Console tab
   - Run: testApiEndpoint()

2. Manual Fetch Test:
   fetch('/api/pages')
     .then(r => r.json())
     .then(d => console.log('API Response:', d))
     .catch(e => console.error('API Error:', e))

3. Check Network Tab:
   - Open dev tools Network tab
   - Trigger the API call
   - Check request/response details

4. Expected Response Format:
   {
     "success": true,
     "data": {
       "locationAndcontacts": {
         "title": "Your Title",
         "body": "Your Body Content",
         "details": "Your Contact Details"
       }
     }
   }
`);
